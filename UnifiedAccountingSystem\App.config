<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <configSections>
        <sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
            <section name="UnifiedAccountingSystem.My.MySettings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false" />
        </sectionGroup>
    </configSections>
    
    <startup>
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8" />
    </startup>
    
    <!-- سلاسل الاتصال بقاعدة البيانات -->
    <connectionStrings>
        <!-- اتصال SQL Server المحلي -->
        <add name="DefaultConnection" 
             connectionString="Server=DESKTOP-7V6D7UH\SQLEXPRESS2014;Database=UnifiedAccountingDB;Integrated Security=true;TrustServerCertificate=true;" 
             providerName="System.Data.SqlClient" />
        
        <!-- اتصال SQL Server مع مصادقة SQL -->
        <add name="SqlServerConnection" 
             connectionString="Server=DESKTOP-7V6D7UH\SQLEXPRESS2014;Database=UnifiedAccountingDB;User Id=sa;Password=your_password;TrustServerCertificate=true;" 
             providerName="System.Data.SqlClient" />
        
        <!-- اتصال SQLite للاختبار -->
        <add name="SQLiteConnection" 
             connectionString="Data Source=|DataDirectory|\UnifiedAccounting.db;Version=3;" 
             providerName="System.Data.SQLite" />
    </connectionStrings>
    
    <!-- إعدادات التطبيق -->
    <appSettings>
        <!-- إعدادات عامة -->
        <add key="ApplicationName" value="نظام إدارة الرواتب والمحاسبة المتكامل" />
        <add key="ApplicationVersion" value="1.0.0" />
        <add key="OrganizationName" value="وزارة الشباب والرياضة - دائرة الطب الرياضي" />
        <add key="DepartmentName" value="شعبة الحسابات" />
        
        <!-- إعدادات قاعدة البيانات -->
        <add key="DatabaseProvider" value="SqlServer" /> <!-- SqlServer, SQLite -->
        <add key="ConnectionTimeout" value="30" />
        <add key="CommandTimeout" value="60" />
        <add key="EnableDatabaseLogging" value="true" />
        
        <!-- إعدادات الأمان -->
        <add key="PasswordMinLength" value="8" />
        <add key="PasswordRequireSpecialChar" value="true" />
        <add key="MaxLoginAttempts" value="5" />
        <add key="LockoutDurationMinutes" value="30" />
        <add key="SessionTimeoutMinutes" value="60" />
        <add key="EnableTwoFactorAuth" value="false" />
        
        <!-- إعدادات الرواتب -->
        <add key="RetirementDeductionRate" value="0.10" /> <!-- 10% -->
        <add key="GovernmentContributionRate" value="0.15" /> <!-- 15% -->
        <add key="DefaultCurrency" value="IQD" />
        <add key="CurrencySymbol" value="د.ع" />
        
        <!-- إعدادات التقارير -->
        <add key="ReportsPath" value="Reports" />
        <add key="ExportsPath" value="Exports" />
        <add key="BackupsPath" value="Backups" />
        <add key="DefaultReportFormat" value="PDF" />
        
        <!-- إعدادات الواجهة -->
        <add key="DefaultLanguage" value="ar-IQ" />
        <add key="DefaultTheme" value="Light" />
        <add key="EnableAnimations" value="true" />
        <add key="AutoSaveInterval" value="300" /> <!-- 5 minutes -->
        
        <!-- إعدادات التدقيق -->
        <add key="EnableAuditLog" value="true" />
        <add key="AuditLogRetentionDays" value="365" />
        <add key="LogUserActions" value="true" />
        <add key="LogDataChanges" value="true" />
        
        <!-- إعدادات النسخ الاحتياطي -->
        <add key="AutoBackupEnabled" value="true" />
        <add key="AutoBackupInterval" value="Daily" /> <!-- Daily, Weekly, Monthly -->
        <add key="BackupRetentionDays" value="30" />
        <add key="CompressBackups" value="true" />
    </appSettings>
    
    <!-- إعدادات المستخدم -->
    <userSettings>
        <UnifiedAccountingSystem.My.MySettings>
            <setting name="RememberedUsername" serializeAs="String">
                <value />
            </setting>
            <setting name="WindowSize" serializeAs="String">
                <value>1280,720</value>
            </setting>
            <setting name="WindowPosition" serializeAs="String">
                <value>Center</value>
            </setting>
            <setting name="LastSelectedModule" serializeAs="String">
                <value>Employees</value>
            </setting>
            <setting name="GridColumnWidths" serializeAs="String">
                <value />
            </setting>
            <setting name="RecentFiles" serializeAs="String">
                <value />
            </setting>
        </UnifiedAccountingSystem.My.MySettings>
    </userSettings>
    
    <!-- إعدادات التشخيص والتسجيل -->
    <system.diagnostics>
        <trace autoflush="true">
            <listeners>
                <add name="fileListener" 
                     type="System.Diagnostics.TextWriterTraceListener" 
                     initializeData="Logs\Application.log" />
            </listeners>
        </trace>
    </system.diagnostics>
    
    <!-- إعدادات وقت التشغيل -->
    <runtime>
        <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
            <dependentAssembly>
                <assemblyIdentity name="System.Data.SQLite" publicKeyToken="db937bc2d44ff139" culture="neutral" />
                <bindingRedirect oldVersion="0.0.0.0-1.0.118.0" newVersion="1.0.118.0" />
            </dependentAssembly>
        </assemblyBinding>
    </runtime>
</configuration>
