# -*- coding: utf-8 -*-
"""
إعدادات النظام الأساسية
System Configuration Settings
"""

import os
from pathlib import Path

# مسارات النظام
BASE_DIR = Path(__file__).resolve().parent.parent
DATABASE_PATH = BASE_DIR / "data" / "accounting_system.db"
BACKUP_DIR = BASE_DIR / "backups"
REPORTS_DIR = BASE_DIR / "reports"
LOGS_DIR = BASE_DIR / "logs"

# إعدادات قاعدة البيانات
DATABASE_CONFIG = {
    'type': 'sqlite',  # يمكن تغييرها إلى 'sqlserver' لاحقاً
    'sqlite_path': str(DATABASE_PATH),
    'sqlserver_config': {
        'server': 'DESKTOP-7V6D7UH\\SQLEXPRESS2014',
        'database': 'AccountingSystem',
        'trusted_connection': True
    }
}

# معلومات المؤسسة
ORGANIZATION_INFO = {
    'ministry': 'وزارة الشباب والرياضة',
    'department': 'دائرة الطب الرياضي',
    'section': 'قسم إداري',
    'division': 'شعبة الحسابات',
    'logo_path': 'assets/logo.png'
}

# الحسابات البنكية
BANK_ACCOUNTS = {
    'operational': {
        'number': '***************',
        'type': 'تشغيلي',
        'bank': 'مصرف الرافدين',
        'branch': 'فرع دور الضباط',
        'branch_code': '69'
    },
    'salary': {
        'number': '***************',
        'type': 'رواتب',
        'bank': 'مصرف الرافدين',
        'branch': 'فرع دور الضباط',
        'branch_code': '69'
    }
}

# إعدادات الواجهة
UI_CONFIG = {
    'window_size': (1200, 800),
    'login_size': (500, 650),
    'font_family': 'Cairo',
    'font_size': 12,
    'rtl_support': True,
    'theme': 'modern'
}

# إعدادات الرواتب والاستقطاعات
SALARY_CONFIG = {
    'deductions': {
        'retirement': 0.10,  # تقاعد 10%
        'government_contribution': 0.15,  # مساهمة حكومية 15%
        'income_tax': 0.05,  # ضريبة الدخل (متغيرة)
        'social_protection': 0.02  # حماية اجتماعية
    },
    'allowances': [
        'منصب', 'زوجية', 'أولاد', 'هندسية', 'شهادة',
        'حرفة', 'خطورة', 'نقل', 'جامعية'
    ]
}

# إعدادات التقارير
REPORTS_CONFIG = {
    'pdf_settings': {
        'page_size': 'A4',
        'margin': 20,
        'font_path': 'fonts/Cairo-Regular.ttf'
    },
    'excel_settings': {
        'default_sheet': 'البيانات',
        'rtl_support': True
    }
}

# إعدادات النسخ الاحتياطي
BACKUP_CONFIG = {
    'auto_backup': True,
    'backup_interval': 24,  # ساعة
    'max_backups': 30,  # عدد النسخ المحفوظة
    'compression': True
}

# إنشاء المجلدات المطلوبة
def create_directories():
    """إنشاء المجلدات الأساسية للنظام"""
    directories = [
        BASE_DIR / "data",
        BACKUP_DIR,
        REPORTS_DIR,
        LOGS_DIR,
        BASE_DIR / "assets",
        BASE_DIR / "fonts"
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)

if __name__ == "__main__":
    create_directories()
    print("تم إنشاء المجلدات الأساسية بنجاح")
