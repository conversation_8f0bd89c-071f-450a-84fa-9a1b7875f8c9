# -*- coding: utf-8 -*-
"""
إعدادات النظام الأساسية
System Configuration Settings
"""

import os
from pathlib import Path
from typing import Dict, Any

# مسارات النظام
BASE_DIR = Path(__file__).resolve().parent.parent
DATABASE_PATH = BASE_DIR / "data" / "accounting_system.db"
BACKUP_DIR = BASE_DIR / "backups"
REPORTS_DIR = BASE_DIR / "reports"
LOGS_DIR = BASE_DIR / "logs"
STATIC_DIR = BASE_DIR / "web_app" / "static"
TEMPLATES_DIR = BASE_DIR / "web_app" / "templates"

# إعدادات قاعدة البيانات
DATABASE_CONFIG = {
    'type': 'sqlite',  # يمكن تغييرها إلى 'sqlserver' لاحقاً
    'sqlite_path': str(DATABASE_PATH),
    'sqlserver_config': {
        'server': 'DESKTOP-7V6D7UH\\SQLEXPRESS2014',
        'database': 'AccountingSystem',
        'trusted_connection': True
    }
}

# إعدادات تطبيق الويب
WEB_CONFIG = {
    'host': '127.0.0.1',
    'port': 8000,
    'debug': True,
    'secret_key': 'your-secret-key-here-change-in-production',
    'algorithm': 'HS256',
    'access_token_expire_minutes': 30
}

# معلومات المؤسسة
ORGANIZATION_INFO = {
    'ministry': 'وزارة الشباب والرياضة',
    'department': 'دائرة الطب الرياضي',
    'section': 'قسم إداري',
    'division': 'شعبة الحسابات',
    'logo_path': 'assets/logo.png',
    'address': 'بغداد - العراق',
    'phone': '+964-1-XXXXXXX',
    'email': '<EMAIL>'
}

# الحسابات البنكية
BANK_ACCOUNTS = {
    'operational': {
        'number': '***************',
        'type': 'تشغيلي',
        'bank': 'مصرف الرافدين',
        'branch': 'فرع دور الضباط',
        'branch_code': '69'
    },
    'salary': {
        'number': '***************',
        'type': 'رواتب',
        'bank': 'مصرف الرافدين',
        'branch': 'فرع دور الضباط',
        'branch_code': '69'
    }
}

# إعدادات الواجهة
UI_CONFIG = {
    'desktop': {
        'window_size': (1400, 900),
        'login_size': (500, 650),
        'font_family': 'Cairo',
        'font_size': 12,
        'rtl_support': True,
        'theme': 'modern'
    },
    'web': {
        'rtl_support': True,
        'theme': 'bootstrap',
        'font_family': 'Cairo',
        'items_per_page': 25
    }
}

# إعدادات الرواتب والاستقطاعات
SALARY_CONFIG = {
    'deductions': {
        'retirement': {'rate': 0.10, 'name': 'تقاعد'},
        'government_contribution': {'rate': 0.15, 'name': 'مساهمة حكومية'},
        'income_tax': {'rate': 0.05, 'name': 'ضريبة الدخل'},
        'social_protection': {'rate': 0.02, 'name': 'حماية اجتماعية'}
    },
    'allowances': [
        'منصب', 'زوجية', 'أولاد', 'هندسية', 'شهادة',
        'حرفة', 'خطورة', 'نقل', 'جامعية'
    ],
    'additional_deductions': [
        'تنفيذ قضائي', 'أقساط', 'أمانات صحية', 'حجز مصرفي'
    ]
}

# دليل المحاسبة
ACCOUNTING_CHART = {
    'expense_types': [
        'نفقات تشغيلية',
        'رواتب وتعويضات',
        'نفقات سفر وإيفاد',
        'صيانة',
        'خدمات',
        'نشر وإعلام'
    ],
    'form_types': [
        'استمارة رواتب',
        'استمارة مصروفات',
        'استمارة سفر',
        'استمارة صيانة'
    ]
}

# إعدادات التقارير
REPORTS_CONFIG = {
    'pdf_settings': {
        'page_size': 'A4',
        'margin': 20,
        'font_path': 'fonts/Cairo-Regular.ttf',
        'header_font_size': 16,
        'body_font_size': 12
    },
    'excel_settings': {
        'default_sheet': 'البيانات',
        'rtl_support': True,
        'header_style': {
            'bold': True,
            'bg_color': '#4472C4',
            'font_color': 'white'
        }
    }
}

# إعدادات النسخ الاحتياطي
BACKUP_CONFIG = {
    'auto_backup': True,
    'backup_interval': 24,  # ساعة
    'max_backups': 30,  # عدد النسخ المحفوظة
    'compression': True,
    'backup_schedule': '02:00'  # الساعة 2:00 صباحاً
}

# إعدادات الأمان
SECURITY_CONFIG = {
    'password_min_length': 8,
    'session_timeout': 30,  # دقيقة
    'max_login_attempts': 3,
    'lockout_duration': 15  # دقيقة
}

# إنشاء المجلدات المطلوبة
def create_directories():
    """إنشاء المجلدات الأساسية للنظام"""
    directories = [
        BASE_DIR / "data",
        BACKUP_DIR,
        REPORTS_DIR,
        LOGS_DIR,
        BASE_DIR / "assets",
        BASE_DIR / "fonts",
        STATIC_DIR,
        TEMPLATES_DIR,
        BASE_DIR / "web_app" / "static" / "css",
        BASE_DIR / "web_app" / "static" / "js",
        BASE_DIR / "web_app" / "static" / "images"
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)

def get_database_url():
    """الحصول على رابط قاعدة البيانات"""
    if DATABASE_CONFIG['type'] == 'sqlite':
        return f"sqlite:///{DATABASE_CONFIG['sqlite_path']}"
    else:
        config = DATABASE_CONFIG['sqlserver_config']
        return f"mssql+pyodbc://{config['server']}/{config['database']}?trusted_connection=yes&driver=ODBC+Driver+17+for+SQL+Server"

if __name__ == "__main__":
    create_directories()
    print("تم إنشاء المجلدات الأساسية بنجاح")
