# -*- coding: utf-8 -*-
"""
دعم اللغة العربية والنصوص RTL
Arabic Language and RTL Text Support
"""

import arabic_reshaper
from bidi.algorithm import get_display
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QFontDatabase
from PyQt5.QtWidgets import QApplication
import os
from pathlib import Path

class ArabicSupport:
    def __init__(self):
        self.font_loaded = False
        self.default_font = None
        self.load_arabic_fonts()
    
    def load_arabic_fonts(self):
        """تحميل الخطوط العربية"""
        try:
            # محاولة تحميل خط Cairo
            font_paths = [
                "fonts/Cairo-Regular.ttf",
                "C:/Windows/Fonts/arial.ttf",  # خط احتياطي
                "C:/Windows/Fonts/tahoma.ttf"  # خط احتياطي آخر
            ]
            
            for font_path in font_paths:
                if os.path.exists(font_path):
                    font_id = QFontDatabase.addApplicationFont(font_path)
                    if font_id != -1:
                        font_families = QFontDatabase.applicationFontFamilies(font_id)
                        if font_families:
                            self.default_font = QFont(font_families[0], 12)
                            self.font_loaded = True
                            break
            
            # إذا لم يتم تحميل أي خط، استخدم خط النظام
            if not self.font_loaded:
                self.default_font = QFont("Arial", 12)
                
        except Exception as e:
            print(f"خطأ في تحميل الخطوط: {e}")
            self.default_font = QFont("Arial", 12)
    
    def reshape_arabic_text(self, text):
        """إعادة تشكيل النص العربي للعرض الصحيح"""
        if not text:
            return ""
        
        try:
            # إعادة تشكيل النص العربي
            reshaped_text = arabic_reshaper.reshape(text)
            # تطبيق خوارزمية BiDi للنص المختلط
            bidi_text = get_display(reshaped_text)
            return bidi_text
        except Exception as e:
            print(f"خطأ في معالجة النص العربي: {e}")
            return text
    
    def get_arabic_font(self, size=12, bold=False):
        """الحصول على خط عربي مناسب"""
        font = QFont(self.default_font)
        font.setPointSize(size)
        font.setBold(bold)
        return font
    
    def setup_rtl_widget(self, widget):
        """إعداد عنصر واجهة للدعم RTL"""
        widget.setLayoutDirection(Qt.RightToLeft)
        widget.setFont(self.get_arabic_font())
    
    def format_number_arabic(self, number):
        """تنسيق الأرقام للعرض العربي"""
        if isinstance(number, (int, float)):
            # تنسيق الرقم مع فواصل الآلاف
            formatted = f"{number:,.2f}" if isinstance(number, float) else f"{number:,}"
            return formatted
        return str(number)
    
    def format_currency(self, amount, currency="دينار"):
        """تنسيق المبالغ المالية"""
        formatted_amount = self.format_number_arabic(amount)
        return f"{formatted_amount} {currency}"
    
    def format_date_arabic(self, date_obj):
        """تنسيق التاريخ بالعربية"""
        if not date_obj:
            return ""
        
        months_arabic = {
            1: "كانون الثاني", 2: "شباط", 3: "آذار", 4: "نيسان",
            5: "أيار", 6: "حزيران", 7: "تموز", 8: "آب",
            9: "أيلول", 10: "تشرين الأول", 11: "تشرين الثاني", 12: "كانون الأول"
        }
        
        try:
            day = date_obj.day
            month = months_arabic.get(date_obj.month, str(date_obj.month))
            year = date_obj.year
            return f"{day} {month} {year}"
        except:
            return str(date_obj)
    
    def validate_arabic_input(self, text):
        """التحقق من صحة الإدخال العربي"""
        if not text:
            return True
        
        # السماح بالأحرف العربية والأرقام والمسافات وعلامات الترقيم الأساسية
        allowed_chars = set("أبتثجحخدذرزسشصضطظعغفقكلمنهويءآإئؤة" + 
                           "0123456789" + " .,()-/")
        
        return all(char in allowed_chars for char in text)
    
    def clean_arabic_text(self, text):
        """تنظيف النص العربي من الأحرف غير المرغوبة"""
        if not text:
            return ""
        
        # إزالة الأحرف الخاصة غير المرغوبة
        cleaned = text.strip()
        # توحيد المسافات
        cleaned = " ".join(cleaned.split())
        return cleaned

# إنشاء مثيل مشترك من دعم اللغة العربية
arabic_support = ArabicSupport()

def setup_arabic_application():
    """إعداد التطبيق لدعم اللغة العربية"""
    app = QApplication.instance()
    if app:
        app.setLayoutDirection(Qt.RightToLeft)
        app.setFont(arabic_support.get_arabic_font())
    
def apply_rtl_style():
    """تطبيق أنماط RTL على التطبيق"""
    return """
    QWidget {
        font-family: 'Cairo', 'Arial', sans-serif;
        font-size: 12px;
    }
    
    QLineEdit, QTextEdit, QPlainTextEdit {
        text-align: right;
        padding: 5px;
        border: 1px solid #ccc;
        border-radius: 4px;
    }
    
    QLabel {
        text-align: right;
        padding: 2px;
    }
    
    QTableWidget {
        gridline-color: #d0d0d0;
        selection-background-color: #3498db;
        alternate-background-color: #f8f9fa;
    }
    
    QTableWidget::item {
        text-align: right;
        padding: 5px;
    }
    
    QHeaderView::section {
        background-color: #34495e;
        color: white;
        padding: 8px;
        border: none;
        text-align: center;
        font-weight: bold;
    }
    
    QPushButton {
        background-color: #3498db;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        font-weight: bold;
    }
    
    QPushButton:hover {
        background-color: #2980b9;
    }
    
    QPushButton:pressed {
        background-color: #21618c;
    }
    
    QComboBox {
        padding: 5px;
        border: 1px solid #ccc;
        border-radius: 4px;
        text-align: right;
    }
    
    QGroupBox {
        font-weight: bold;
        border: 2px solid #cccccc;
        border-radius: 5px;
        margin: 10px 0px;
        padding-top: 10px;
    }
    
    QGroupBox::title {
        subcontrol-origin: margin;
        left: 10px;
        padding: 0 5px 0 5px;
    }
    """
