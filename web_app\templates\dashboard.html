{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-tachometer-alt me-2 text-primary"></i>
                لوحة التحكم
            </h1>
            <div class="text-muted">
                <i class="fas fa-calendar me-1"></i>
                <span id="current-date"></span>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            إجمالي الموظفين
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ stats.total_employees or 0 }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            الموظفون النشطون
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ stats.active_employees or 0 }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-user-check fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            رواتب الشهر الحالي
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ stats.current_month_salaries or 0 }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-money-bill-wave fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-right-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col me-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            إجمالي الأرصدة
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ "{:,.0f}".format(total_balance) }} د.ع
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-university fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Employees -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-users me-2"></i>
                    آخر الموظفين المضافين
                </h6>
                <a href="/employees" class="btn btn-sm btn-primary">
                    عرض الكل
                    <i class="fas fa-arrow-left ms-1"></i>
                </a>
            </div>
            <div class="card-body">
                {% if recent_employees %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الرقم الوظيفي</th>
                                <th>الاسم</th>
                                <th>المنصب</th>
                                <th>الراتب الأساسي</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for employee in recent_employees %}
                            <tr>
                                <td>{{ employee.employee_number }}</td>
                                <td>{{ employee.full_name }}</td>
                                <td>{{ employee.job_title }}</td>
                                <td>{{ "{:,.0f}".format(employee.basic_salary) }} د.ع</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-users fa-3x mb-3"></i>
                    <p>لا توجد بيانات موظفين</p>
                    <a href="/employees/add" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>
                        إضافة موظف جديد
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-bolt me-2"></i>
                    الإجراءات السريعة
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-6 mb-3">
                        <a href="/employees/add" class="btn btn-outline-primary w-100 py-3">
                            <i class="fas fa-user-plus fa-2x mb-2 d-block"></i>
                            إضافة موظف
                        </a>
                    </div>
                    <div class="col-6 mb-3">
                        <a href="/salaries/process" class="btn btn-outline-success w-100 py-3">
                            <i class="fas fa-calculator fa-2x mb-2 d-block"></i>
                            معالجة الرواتب
                        </a>
                    </div>
                    <div class="col-6 mb-3">
                        <a href="/reports" class="btn btn-outline-info w-100 py-3">
                            <i class="fas fa-chart-bar fa-2x mb-2 d-block"></i>
                            التقارير
                        </a>
                    </div>
                    <div class="col-6 mb-3">
                        <a href="/settings" class="btn btn-outline-warning w-100 py-3">
                            <i class="fas fa-cog fa-2x mb-2 d-block"></i>
                            الإعدادات
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Status -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-info-circle me-2"></i>
                    حالة النظام
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="d-flex align-items-center mb-3">
                            <div class="status-indicator bg-success me-3"></div>
                            <div>
                                <strong>قاعدة البيانات</strong><br>
                                <small class="text-muted">متصلة وتعمل بشكل طبيعي</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-center mb-3">
                            <div class="status-indicator bg-success me-3"></div>
                            <div>
                                <strong>النسخ الاحتياطي</strong><br>
                                <small class="text-muted">آخر نسخة: اليوم 02:00 ص</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-center mb-3">
                            <div class="status-indicator bg-warning me-3"></div>
                            <div>
                                <strong>التحديثات</strong><br>
                                <small class="text-muted">يوجد تحديث متاح</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.border-right-primary {
    border-right: 0.25rem solid #4e73df !important;
}

.border-right-success {
    border-right: 0.25rem solid #1cc88a !important;
}

.border-right-info {
    border-right: 0.25rem solid #36b9cc !important;
}

.border-right-warning {
    border-right: 0.25rem solid #f6c23e !important;
}

.text-xs {
    font-size: 0.7rem;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    flex-shrink: 0;
}

.card {
    transition: transform 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
}

.btn-outline-primary:hover,
.btn-outline-success:hover,
.btn-outline-info:hover,
.btn-outline-warning:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// عرض التاريخ الحالي
document.addEventListener('DOMContentLoaded', function() {
    const now = new Date();
    const options = { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric',
        weekday: 'long'
    };
    
    const arabicDate = now.toLocaleDateString('ar-IQ', options);
    document.getElementById('current-date').textContent = arabicDate;
});

// تحديث الإحصائيات كل 30 ثانية
setInterval(function() {
    // يمكن إضافة AJAX call لتحديث الإحصائيات
}, 30000);
</script>
{% endblock %}
