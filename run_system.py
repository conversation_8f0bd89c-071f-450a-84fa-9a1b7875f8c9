# -*- coding: utf-8 -*-
"""
ملف تشغيل النظام السريع
Quick System Launcher
"""

import sys
import os
import subprocess
import argparse
from pathlib import Path

def check_python_version():
    """التحقق من إصدار Python"""
    if sys.version_info < (3, 10):
        print("❌ خطأ: يتطلب Python 3.10 أو أحدث")
        print(f"الإصدار الحالي: {sys.version}")
        return False
    return True

def check_requirements():
    """التحقق من المتطلبات"""
    requirements_file = Path("requirements.txt")
    if not requirements_file.exists():
        print("❌ خطأ: ملف requirements.txt غير موجود")
        return False
    
    try:
        import PyQt5
        import fastapi
        import sqlalchemy
        print("✅ المتطلبات الأساسية متوفرة")
        return True
    except ImportError as e:
        print(f"❌ خطأ: مكتبة مفقودة - {e}")
        print("يرجى تشغيل: pip install -r requirements.txt")
        return False

def install_requirements():
    """تثبيت المتطلبات"""
    print("📦 جاري تثبيت المتطلبات...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ تم تثبيت المتطلبات بنجاح")
        return True
    except subprocess.CalledProcessError:
        print("❌ فشل في تثبيت المتطلبات")
        return False

def setup_database():
    """إعداد قاعدة البيانات"""
    print("🗄️ جاري إعداد قاعدة البيانات...")
    try:
        from config.settings import create_directories
        create_directories()
        print("✅ تم إعداد قاعدة البيانات بنجاح")
        return True
    except Exception as e:
        print(f"❌ خطأ في إعداد قاعدة البيانات: {e}")
        return False

def run_desktop_app():
    """تشغيل تطبيق سطح المكتب"""
    print("🖥️ جاري تشغيل تطبيق سطح المكتب...")
    try:
        subprocess.run([sys.executable, "main.py"])
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التطبيق")
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")

def run_web_app():
    """تشغيل تطبيق الويب"""
    print("🌐 جاري تشغيل تطبيق الويب...")
    print("📍 الرابط: http://localhost:8000")
    try:
        subprocess.run([sys.executable, "web_main.py"])
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الخادم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")

def show_system_info():
    """عرض معلومات النظام"""
    print("=" * 60)
    print("🏛️ نظام المحاسبة المتكامل")
    print("   وزارة الشباب والرياضة - دائرة الطب الرياضي")
    print("=" * 60)
    print(f"🐍 Python: {sys.version}")
    print(f"📁 المجلد: {Path.cwd()}")
    print(f"💻 النظام: {os.name}")
    print("=" * 60)

def show_menu():
    """عرض القائمة الرئيسية"""
    print("\n📋 القائمة الرئيسية:")
    print("1. تشغيل تطبيق سطح المكتب")
    print("2. تشغيل تطبيق الويب")
    print("3. تثبيت المتطلبات")
    print("4. إعداد قاعدة البيانات")
    print("5. عرض معلومات النظام")
    print("0. خروج")
    print("-" * 40)

def main():
    """الدالة الرئيسية"""
    parser = argparse.ArgumentParser(description="نظام المحاسبة المتكامل")
    parser.add_argument("--mode", choices=["desktop", "web", "setup"], 
                       help="وضع التشغيل")
    parser.add_argument("--install", action="store_true", 
                       help="تثبيت المتطلبات")
    parser.add_argument("--setup-db", action="store_true", 
                       help="إعداد قاعدة البيانات")
    
    args = parser.parse_args()
    
    # عرض معلومات النظام
    show_system_info()
    
    # التحقق من إصدار Python
    if not check_python_version():
        return
    
    # معالجة المعاملات
    if args.install:
        install_requirements()
        return
    
    if args.setup_db:
        setup_database()
        return
    
    if args.mode == "desktop":
        if check_requirements():
            run_desktop_app()
        return
    
    if args.mode == "web":
        if check_requirements():
            run_web_app()
        return
    
    if args.mode == "setup":
        install_requirements()
        setup_database()
        return
    
    # القائمة التفاعلية
    while True:
        show_menu()
        try:
            choice = input("اختر رقماً من القائمة: ").strip()
            
            if choice == "1":
                if check_requirements():
                    run_desktop_app()
                else:
                    print("يرجى تثبيت المتطلبات أولاً (الخيار 3)")
            
            elif choice == "2":
                if check_requirements():
                    run_web_app()
                else:
                    print("يرجى تثبيت المتطلبات أولاً (الخيار 3)")
            
            elif choice == "3":
                install_requirements()
            
            elif choice == "4":
                setup_database()
            
            elif choice == "5":
                show_system_info()
            
            elif choice == "0":
                print("👋 شكراً لاستخدام النظام")
                break
            
            else:
                print("❌ خيار غير صحيح، يرجى المحاولة مرة أخرى")
        
        except KeyboardInterrupt:
            print("\n👋 تم إنهاء البرنامج")
            break
        except Exception as e:
            print(f"❌ خطأ: {e}")

if __name__ == "__main__":
    main()
