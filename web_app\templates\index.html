{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<!-- شريط الوصول السريع -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-primary">
            <div class="card-header bg-primary text-white">
                <h6 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    الوصول السريع
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <div class="d-grid">
                            <a href="/login" class="btn btn-primary">
                                <i class="fas fa-sign-in-alt me-1"></i>
                                تسجيل الدخول
                            </a>
                        </div>
                    </div>
                    <div class="col-md-3 mb-2">
                        <div class="d-grid">
                            <a href="/employees" class="btn btn-success">
                                <i class="fas fa-users me-1"></i>
                                إدارة الموظفين
                            </a>
                        </div>
                    </div>
                    <div class="col-md-3 mb-2">
                        <div class="d-grid">
                            <a href="/employees/add" class="btn btn-info">
                                <i class="fas fa-user-plus me-1"></i>
                                إضافة موظف
                            </a>
                        </div>
                    </div>
                    <div class="col-md-3 mb-2">
                        <div class="d-grid">
                            <a href="/employees/import" class="btn btn-warning">
                                <i class="fas fa-file-excel me-1"></i>
                                استيراد Excel
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <!-- Hero Section -->
        <div class="jumbotron bg-gradient-primary text-white rounded p-5 mb-4">
            <div class="container text-center">
                <h1 class="display-4 fw-bold">
                    <i class="fas fa-calculator me-3"></i>
                    نظام المحاسبة المتكامل
                </h1>
                <p class="lead">
                    نظام شامل لإدارة رواتب الموظفين والمصروفات للمؤسسات الحكومية
                </p>
                <hr class="my-4">
                <p class="mb-4">{{ organization }}</p>
                <div class="d-flex justify-content-center gap-3 flex-wrap">
                    <a class="btn btn-light btn-lg px-4 py-3" href="/login" role="button">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        تسجيل الدخول
                    </a>
                    <a class="btn btn-outline-light btn-lg px-4 py-3" href="/employees" role="button">
                        <i class="fas fa-users me-2"></i>
                        إدارة الموظفين
                    </a>
                    <a class="btn btn-outline-light btn-lg px-4 py-3" href="/dashboard" role="button">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        لوحة التحكم
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Features Cards -->
    <div class="col-md-4 mb-4">
        <div class="card h-100 shadow-sm">
            <div class="card-body text-center">
                <div class="feature-icon bg-primary text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                    <i class="fas fa-users fa-2x"></i>
                </div>
                <h5 class="card-title">إدارة الموظفين</h5>
                <p class="card-text">
                    إدارة شاملة لبيانات الموظفين مع دعم الهيكل التنظيمي الحكومي
                    والمخصصات والاستقطاعات المختلفة.
                </p>
            </div>
        </div>
    </div>

    <div class="col-md-4 mb-4">
        <div class="card h-100 shadow-sm">
            <div class="card-body text-center">
                <div class="feature-icon bg-success text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                    <i class="fas fa-money-bill-wave fa-2x"></i>
                </div>
                <h5 class="card-title">نظام الرواتب</h5>
                <p class="card-text">
                    حساب الرواتب الشهرية تلقائياً مع المخصصات والاستقطاعات
                    وفقاً للنظام الحكومي العراقي.
                </p>
            </div>
        </div>
    </div>

    <div class="col-md-4 mb-4">
        <div class="card h-100 shadow-sm">
            <div class="card-body text-center">
                <div class="feature-icon bg-info text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                    <i class="fas fa-chart-bar fa-2x"></i>
                </div>
                <h5 class="card-title">التقارير المالية</h5>
                <p class="card-text">
                    تقارير شاملة ومفصلة مع إمكانية التصدير إلى PDF وExcel
                    ودعم كامل للغة العربية.
                </p>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-6 mb-4">
        <div class="card shadow-sm">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-building me-2"></i>
                    معلومات المؤسسة
                </h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <strong>الوزارة:</strong> وزارة الشباب والرياضة
                    </li>
                    <li class="mb-2">
                        <strong>الدائرة:</strong> دائرة الطب الرياضي
                    </li>
                    <li class="mb-2">
                        <strong>القسم:</strong> قسم إداري
                    </li>
                    <li class="mb-2">
                        <strong>الشعبة:</strong> شعبة الحسابات
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <div class="col-md-6 mb-4">
        <div class="card shadow-sm">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-university me-2"></i>
                    الحسابات البنكية
                </h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <strong>حساب تشغيلي:</strong> 069100011822001
                    </li>
                    <li class="mb-2">
                        <strong>حساب رواتب:</strong> 069100011822002
                    </li>
                    <li class="mb-2">
                        <strong>البنك:</strong> مصرف الرافدين
                    </li>
                    <li class="mb-2">
                        <strong>الفرع:</strong> فرع دور الضباط (69)
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-header bg-warning text-dark">
                <h5 class="card-title mb-0">
                    <i class="fas fa-star me-2"></i>
                    مميزات النظام
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                دعم كامل للغة العربية واتجاه RTL
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                واجهة حديثة ومتجاوبة
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                نظام صلاحيات متقدم
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                تصدير التقارير بصيغ متعددة
                            </li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                نسخ احتياطي تلقائي
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                استيراد وتصدير البيانات
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                دعم قواعد بيانات متعددة
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                تطبيق ويب وسطح مكتب
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.bg-gradient-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

.feature-icon {
    transition: transform 0.3s ease;
}

.card:hover .feature-icon {
    transform: scale(1.1);
}

.card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}

/* تحسين الأزرار */
.btn {
    transition: all 0.3s ease;
    border-radius: 8px;
    font-weight: 600;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn-lg {
    padding: 12px 24px;
    font-size: 1.1rem;
}

/* تأثيرات خاصة للأزرار الرئيسية */
.btn-light:hover {
    background-color: #ffffff;
    color: #007bff;
    border-color: #ffffff;
}

.btn-outline-light:hover {
    background-color: rgba(255,255,255,0.1);
    border-color: #ffffff;
    color: #ffffff;
}

/* تحسين شريط الوصول السريع */
.card-header {
    border-bottom: 2px solid rgba(255,255,255,0.2);
}

/* تأثيرات الأيقونات */
.fas {
    transition: transform 0.2s ease;
}

.btn:hover .fas {
    transform: scale(1.1);
}

/* تحسين الجمبوترون */
.jumbotron {
    border-radius: 15px !important;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

/* تحسين البطاقات */
.card {
    border-radius: 12px;
    border: none;
}

.card-header {
    border-radius: 12px 12px 0 0 !important;
}

/* تأثيرات متحركة للأيقونات */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.feature-icon:hover {
    animation: pulse 1s infinite;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحسين تفاعل الأزرار
    const buttons = document.querySelectorAll('.btn');

    buttons.forEach(button => {
        // تأثير النقر
        button.addEventListener('click', function(e) {
            // إنشاء تأثير الموجة
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.classList.add('ripple');

            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });

        // تأثير التحويم
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-3px) scale(1.02)';
        });

        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // تأثيرات البطاقات
    const cards = document.querySelectorAll('.card');

    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px)';
            this.style.boxShadow = '0 12px 35px rgba(0,0,0,0.2)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '0 4px 15px rgba(0,0,0,0.1)';
        });
    });

    // رسالة ترحيب
    setTimeout(() => {
        showWelcomeMessage();
    }, 1000);
});

function showWelcomeMessage() {
    // إنشاء رسالة ترحيب
    const welcomeAlert = document.createElement('div');
    welcomeAlert.className = 'alert alert-success alert-dismissible fade show position-fixed';
    welcomeAlert.style.cssText = `
        top: 20px;
        right: 20px;
        z-index: 1050;
        min-width: 300px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        border-radius: 10px;
    `;

    welcomeAlert.innerHTML = `
        <i class="fas fa-star me-2"></i>
        <strong>مرحباً بك!</strong> في نظام المحاسبة المتكامل
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(welcomeAlert);

    // إزالة الرسالة تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (welcomeAlert.parentNode) {
            welcomeAlert.remove();
        }
    }, 5000);
}

// تأثير الموجة للأزرار
const style = document.createElement('style');
style.textContent = `
    .btn {
        position: relative;
        overflow: hidden;
    }

    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.6);
        transform: scale(0);
        animation: ripple-animation 0.6s linear;
        pointer-events: none;
    }

    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }

    /* تحسين الانتقالات */
    .btn, .card {
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    /* تأثيرات إضافية للأيقونات */
    .feature-icon {
        position: relative;
    }

    .feature-icon::before {
        content: '';
        position: absolute;
        top: -5px;
        left: -5px;
        right: -5px;
        bottom: -5px;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        border-radius: 50%;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .card:hover .feature-icon::before {
        opacity: 1;
    }
`;
document.head.appendChild(style);
</script>
{% endblock %}
