# -*- coding: utf-8 -*-
"""
ملف إعداد التطبيق للتثبيت
Setup file for application installation
"""

from setuptools import setup, find_packages
import os
from pathlib import Path

# قراءة ملف README
def read_readme():
    readme_path = Path(__file__).parent / "README.md"
    if readme_path.exists():
        with open(readme_path, "r", encoding="utf-8") as f:
            return f.read()
    return ""

# قراءة المتطلبات
def read_requirements():
    requirements_path = Path(__file__).parent / "requirements.txt"
    if requirements_path.exists():
        with open(requirements_path, "r", encoding="utf-8") as f:
            return [line.strip() for line in f if line.strip() and not line.startswith("#")]
    return []

# معلومات التطبيق
APP_NAME = "نظام المحاسبة المتكامل"
APP_VERSION = "1.0.0"
APP_DESCRIPTION = "نظام شامل لإدارة رواتب الموظفين والمصروفات للمؤسسات الحكومية"
APP_AUTHOR = "وزارة الشباب والرياضة - دائرة الطب الرياضي"
APP_EMAIL = "<EMAIL>"
APP_URL = "https://github.com/moys-iraq/accounting-system"

setup(
    name="accounting-system",
    version=APP_VERSION,
    description=APP_DESCRIPTION,
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    author=APP_AUTHOR,
    author_email=APP_EMAIL,
    url=APP_URL,
    
    # الحزم
    packages=find_packages(),
    include_package_data=True,
    
    # المتطلبات
    install_requires=read_requirements(),
    
    # Python version requirement
    python_requires=">=3.10",
    
    # تصنيفات PyPI
    classifiers=[
        "Development Status :: 5 - Production/Stable",
        "Intended Audience :: Government",
        "Topic :: Office/Business :: Financial :: Accounting",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Operating System :: Microsoft :: Windows",
        "Natural Language :: Arabic",
    ],
    
    # الكلمات المفتاحية
    keywords="accounting, payroll, government, arabic, iraq, ministry",
    
    # ملفات البيانات
    package_data={
        "": [
            "*.txt",
            "*.md",
            "*.bat",
            "web_app/templates/*.html",
            "web_app/static/css/*.css",
            "web_app/static/js/*.js",
            "fonts/*.ttf",
            "assets/*.png",
            "assets/*.ico",
        ],
    },
    
    # نقاط الدخول
    entry_points={
        "console_scripts": [
            "accounting-desktop=main:main",
            "accounting-web=web_main:main",
            "accounting-setup=run_system:main",
        ],
    },
    
    # خيارات إضافية
    extras_require={
        "dev": [
            "pytest>=7.0.0",
            "pytest-qt>=4.0.0",
            "black>=22.0.0",
            "flake8>=4.0.0",
            "mypy>=0.950",
        ],
        "docs": [
            "sphinx>=4.0.0",
            "sphinx-rtd-theme>=1.0.0",
        ],
        "build": [
            "pyinstaller>=5.0.0",
            "cx-Freeze>=6.0.0",
        ],
    },
    
    # معلومات المشروع
    project_urls={
        "Bug Reports": f"{APP_URL}/issues",
        "Source": APP_URL,
        "Documentation": f"{APP_URL}/wiki",
    },
    
    # الترخيص
    license="MIT",
    
    # ملفات إضافية
    data_files=[
        ("", ["README.md", "requirements.txt", "start_system.bat"]),
    ],
)

# إعداد PyInstaller للتطبيق المستقل
PYINSTALLER_SPEC = """
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# تطبيق سطح المكتب
desktop_a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('config', 'config'),
        ('models', 'models'),
        ('database', 'database'),
        ('utils', 'utils'),
        ('desktop_app', 'desktop_app'),
        ('fonts', 'fonts'),
        ('assets', 'assets'),
    ],
    hiddenimports=[
        'PyQt5.QtCore',
        'PyQt5.QtGui',
        'PyQt5.QtWidgets',
        'sqlalchemy.dialects.sqlite',
        'arabic_reshaper',
        'bidi',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

desktop_pyz = PYZ(desktop_a.pure, desktop_a.zipped_data, cipher=block_cipher)

desktop_exe = EXE(
    desktop_pyz,
    desktop_a.scripts,
    [],
    exclude_binaries=True,
    name='AccountingSystem',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='assets/icon.ico',
)

# تطبيق الويب
web_a = Analysis(
    ['web_main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('config', 'config'),
        ('models', 'models'),
        ('database', 'database'),
        ('utils', 'utils'),
        ('web_app', 'web_app'),
    ],
    hiddenimports=[
        'fastapi',
        'uvicorn',
        'jinja2',
        'sqlalchemy.dialects.sqlite',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

web_pyz = PYZ(web_a.pure, web_a.zipped_data, cipher=block_cipher)

web_exe = EXE(
    web_pyz,
    web_a.scripts,
    [],
    exclude_binaries=True,
    name='AccountingSystemWeb',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,
    disable_windowed_traceback=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)

# جمع الملفات
coll = COLLECT(
    desktop_exe,
    desktop_a.binaries,
    desktop_a.zipfiles,
    desktop_a.datas,
    web_exe,
    web_a.binaries,
    web_a.zipfiles,
    web_a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='AccountingSystem',
)
"""

# كتابة ملف spec
def create_pyinstaller_spec():
    """إنشاء ملف PyInstaller spec"""
    spec_path = Path(__file__).parent / "accounting_system.spec"
    with open(spec_path, "w", encoding="utf-8") as f:
        f.write(PYINSTALLER_SPEC)
    print(f"تم إنشاء ملف PyInstaller spec: {spec_path}")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "create_spec":
        create_pyinstaller_spec()
    else:
        # تشغيل setup عادي
        setup()
