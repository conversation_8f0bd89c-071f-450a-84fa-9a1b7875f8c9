# -*- coding: utf-8 -*-
"""
نموذج المستخدم
User Model
"""

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text
from sqlalchemy.sql import func
from database.database_manager import Base
from passlib.context import Crypt<PERSON>ontext
from datetime import datetime

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

class User(Base):
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    password = Column(String(255), nullable=False)
    full_name = Column(String(100), nullable=False)
    role = Column(String(20), nullable=False, default='user')  # admin, manager, user
    email = Column(String(100), unique=True, index=True)
    phone = Column(String(20))
    is_active = Column(<PERSON><PERSON><PERSON>, default=True)
    last_login = Column(DateTime)
    failed_login_attempts = Column(Integer, default=0)
    locked_until = Column(DateTime)
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    notes = Column(Text)
    
    def __init__(self, username, password, full_name, role='user', email=None, phone=None, is_active=True):
        self.username = username
        self.password = self.hash_password(password)
        self.full_name = full_name
        self.role = role
        self.email = email
        self.phone = phone
        self.is_active = is_active
    
    def hash_password(self, password):
        """تشفير كلمة المرور"""
        return pwd_context.hash(password)
    
    def verify_password(self, password):
        """التحقق من كلمة المرور"""
        return pwd_context.verify(password, self.password)
    
    def update_password(self, new_password):
        """تحديث كلمة المرور"""
        self.password = self.hash_password(new_password)
    
    def record_login(self):
        """تسجيل آخر دخول"""
        self.last_login = datetime.now()
        self.failed_login_attempts = 0
        self.locked_until = None
    
    def record_failed_login(self):
        """تسجيل محاولة دخول فاشلة"""
        self.failed_login_attempts += 1
        if self.failed_login_attempts >= 3:
            from datetime import timedelta
            self.locked_until = datetime.now() + timedelta(minutes=15)
    
    def is_locked(self):
        """التحقق من حالة القفل"""
        if self.locked_until and datetime.now() < self.locked_until:
            return True
        return False
    
    def unlock_account(self):
        """إلغاء قفل الحساب"""
        self.failed_login_attempts = 0
        self.locked_until = None
    
    def has_permission(self, permission):
        """التحقق من الصلاحيات"""
        permissions = {
            'admin': [
                'manage_users', 'manage_employees', 'manage_salaries',
                'manage_accounts', 'view_reports', 'manage_system',
                'backup_restore', 'manage_fiscal_periods'
            ],
            'manager': [
                'manage_employees', 'manage_salaries', 'view_reports',
                'manage_accounts'
            ],
            'user': [
                'view_employees', 'view_salaries', 'view_reports'
            ]
        }
        
        role_permissions = permissions.get(self.role, [])
        return permission in role_permissions
    
    def to_dict(self):
        """تحويل إلى قاموس"""
        return {
            'id': self.id,
            'username': self.username,
            'full_name': self.full_name,
            'role': self.role,
            'email': self.email,
            'phone': self.phone,
            'is_active': self.is_active,
            'last_login': self.last_login.isoformat() if self.last_login else None,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    @classmethod
    def get_role_display_name(cls, role):
        """الحصول على اسم الدور بالعربية"""
        role_names = {
            'admin': 'مدير النظام',
            'manager': 'مدير',
            'user': 'مستخدم'
        }
        return role_names.get(role, role)
    
    def get_role_display(self):
        """الحصول على اسم الدور الحالي بالعربية"""
        return self.get_role_display_name(self.role)
    
    def __repr__(self):
        return f"<User(username='{self.username}', full_name='{self.full_name}', role='{self.role}')>"
