{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-users me-2 text-primary"></i>
                إدارة الموظفين
            </h1>
            <div>
                <a href="/employees/add" class="btn btn-primary me-2">
                    <i class="fas fa-plus me-1"></i>
                    إضافة موظف جديد
                </a>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                        <i class="fas fa-download me-1"></i>
                        تصدير
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="/employees/export/excel">
                            <i class="fas fa-file-excel me-1"></i>
                            Excel
                        </a></li>
                        <li><a class="dropdown-item" href="/employees/export/pdf">
                            <i class="fas fa-file-pdf me-1"></i>
                            PDF
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ employees|length }}</h4>
                        <p class="mb-0">إجمالي الموظفين</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ employees|selectattr("is_active")|list|length }}</h4>
                        <p class="mb-0">الموظفون النشطون</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-check fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ (employees|map(attribute="basic_salary")|sum)|round|int }}</h4>
                        <p class="mb-0">إجمالي الرواتب الأساسية</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-money-bill-wave fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ (employees|map(attribute="basic_salary")|sum / employees|length)|round|int if employees else 0 }}</h4>
                        <p class="mb-0">متوسط الراتب</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-chart-line fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- فلاتر البحث -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-search me-2"></i>
            البحث والتصفية
        </h5>
    </div>
    <div class="card-body">
        <form method="get" id="searchForm">
            <div class="row">
                <div class="col-md-3 mb-3">
                    <label for="search" class="form-label">البحث العام</label>
                    <input type="text" class="form-control" id="search" name="search" 
                           placeholder="اسم الموظف أو الرقم الوظيفي" value="{{ request.query_params.get('search', '') }}">
                </div>
                <div class="col-md-3 mb-3">
                    <label for="department" class="form-label">الدائرة</label>
                    <select class="form-select" id="department" name="department">
                        <option value="">جميع الدوائر</option>
                        <option value="دائرة الطب الرياضي" {{ 'selected' if request.query_params.get('department') == 'دائرة الطب الرياضي' }}>دائرة الطب الرياضي</option>
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="section" class="form-label">القسم</label>
                    <select class="form-select" id="section" name="section">
                        <option value="">جميع الأقسام</option>
                        <option value="قسم إداري" {{ 'selected' if request.query_params.get('section') == 'قسم إداري' }}>قسم إداري</option>
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="job_grade" class="form-label">الدرجة الوظيفية</label>
                    <select class="form-select" id="job_grade" name="job_grade">
                        <option value="">جميع الدرجات</option>
                        {% for grade in ['الأولى', 'الثانية', 'الثالثة', 'الرابعة', 'الخامسة', 'السادسة', 'السابعة', 'الثامنة', 'التاسعة', 'العاشرة'] %}
                        <option value="{{ grade }}" {{ 'selected' if request.query_params.get('job_grade') == grade }}>{{ grade }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="row">
                <div class="col-12">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search me-1"></i>
                        بحث
                    </button>
                    <a href="/employees" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>
                        مسح الفلاتر
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- جدول الموظفين -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            قائمة الموظفين ({{ employees|length }} موظف)
        </h5>
    </div>
    <div class="card-body">
        {% if employees %}
        <div class="table-responsive">
            <table class="table table-hover table-striped" id="employeesTable">
                <thead class="table-dark">
                    <tr>
                        <th>الرقم الوظيفي</th>
                        <th>الاسم الكامل</th>
                        <th>المنصب</th>
                        <th>الدرجة</th>
                        <th>الراتب الأساسي</th>
                        <th>القسم</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for employee in employees %}
                    <tr>
                        <td>
                            <strong>{{ employee.employee_number }}</strong>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                    {{ employee.full_name[0] }}
                                </div>
                                <div>
                                    <strong>{{ employee.full_name }}</strong>
                                    {% if employee.email %}
                                    <br><small class="text-muted">{{ employee.email }}</small>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td>{{ employee.job_title }}</td>
                        <td>
                            <span class="badge bg-info">{{ employee.job_grade }}</span>
                        </td>
                        <td>
                            <strong>{{ "{:,.0f}".format(employee.basic_salary) }}</strong>
                            <small class="text-muted d-block">دينار عراقي</small>
                        </td>
                        <td>
                            <small>{{ employee.section }}</small>
                        </td>
                        <td>
                            {% if employee.is_active %}
                            <span class="badge bg-success">نشط</span>
                            {% else %}
                            <span class="badge bg-danger">غير نشط</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="/employees/{{ employee.id }}" class="btn btn-outline-info" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="/employees/{{ employee.id }}/edit" class="btn btn-outline-warning" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="/employees/{{ employee.id }}/allowances" class="btn btn-outline-success" title="المخصصات">
                                    <i class="fas fa-plus-circle"></i>
                                </a>
                                <button type="button" class="btn btn-outline-danger" title="حذف" 
                                        onclick="confirmDelete({{ employee.id }}, '{{ employee.full_name }}')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-users fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد بيانات موظفين</h5>
            <p class="text-muted">ابدأ بإضافة موظف جديد للنظام</p>
            <a href="/employees/add" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>
                إضافة موظف جديد
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- نافذة تأكيد الحذف -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف الموظف <strong id="employeeName"></strong>؟</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    هذا الإجراء لا يمكن التراجع عنه!
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form method="post" id="deleteForm" style="display: inline;">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash me-1"></i>
                        حذف
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.avatar-sm {
    width: 35px;
    height: 35px;
    font-size: 14px;
    font-weight: bold;
}

.table th {
    border-top: none;
    font-weight: 600;
    text-align: center;
    vertical-align: middle;
}

.table td {
    vertical-align: middle;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.card-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.1);
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete(employeeId, employeeName) {
    document.getElementById('employeeName').textContent = employeeName;
    document.getElementById('deleteForm').action = '/employees/' + employeeId + '/delete';
    
    const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
    modal.show();
}

// البحث المباشر في الجدول
document.getElementById('search').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const table = document.getElementById('employeesTable');
    const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');
    
    for (let i = 0; i < rows.length; i++) {
        const row = rows[i];
        const text = row.textContent.toLowerCase();
        
        if (text.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    }
});

// تحديث العدادات عند التصفية
function updateCounters() {
    const visibleRows = document.querySelectorAll('#employeesTable tbody tr:not([style*="display: none"])');
    // يمكن إضافة تحديث العدادات هنا
}
</script>
{% endblock %}
