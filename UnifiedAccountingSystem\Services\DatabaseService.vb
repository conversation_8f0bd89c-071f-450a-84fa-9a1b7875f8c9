Imports System.Data.SqlClient
Imports System.Configuration

Namespace Services
    Public Class DatabaseService
        Private ReadOnly _connectionString As String

        Public Sub New()
            ' الحصول على سلسلة الاتصال من ملف التكوين
            _connectionString = ConfigurationManager.ConnectionStrings("DefaultConnection").ConnectionString
        End Sub

        Public Function ExecuteQuery(Of T)(query As String, parameters As Dictionary(Of String, Object)) As List(Of T)
            Using connection As New SqlConnection(_connectionString)
                Using command As New SqlCommand(query, connection)
                    ' إضافة المعاملات
                    If parameters IsNot Nothing Then
                        For Each param In parameters
                            command.Parameters.AddWithValue(param.Key, param.Value)
                        Next
                    End If

                    ' فتح الاتصال
                    connection.Open()

                    ' تنفيذ الاستعلام
                    Using reader As SqlDataReader = command.ExecuteReader()
                        Dim results As New List(Of T)
                        While reader.Read()
                            ' تحويل البيانات إلى الكائن المطلوب
                            Dim item = Activator.CreateInstance(Of T)()
                            For i As Integer = 0 To reader.FieldCount - 1
                                Dim property = GetType(T).GetProperty(reader.GetName(i))
                                If property IsNot Nothing AndAlso Not reader.IsDBNull(i) Then
                                    property.SetValue(item, reader.GetValue(i))
                                End If
                            Next
                            results.Add(item)
                        End While
                        Return results
                    End Using
                End Using
            End Using
        End Function

        Public Function ExecuteNonQuery(query As String, parameters As Dictionary(Of String, Object)) As Integer
            Using connection As New SqlConnection(_connectionString)
                Using command As New SqlCommand(query, connection)
                    ' إضافة المعاملات
                    If parameters IsNot Nothing Then
                        For Each param In parameters
                            command.Parameters.AddWithValue(param.Key, param.Value)
                        Next
                    End If

                    ' فتح الاتصال
                    connection.Open()

                    ' تنفيذ الاستعلام
                    Return command.ExecuteNonQuery()
                End Using
            End Using
        End Function

        Public Function ExecuteScalar(Of T)(query As String, parameters As Dictionary(Of String, Object)) As T
            Using connection As New SqlConnection(_connectionString)
                Using command As New SqlCommand(query, connection)
                    ' إضافة المعاملات
                    If parameters IsNot Nothing Then
                        For Each param In parameters
                            command.Parameters.AddWithValue(param.Key, param.Value)
                        Next
                    End If

                    ' فتح الاتصال
                    connection.Open()

                    ' تنفيذ الاستعلام
                    Dim result = command.ExecuteScalar()
                    If result Is Nothing OrElse result Is DBNull.Value Then
                        Return Nothing
                    End If
                    Return DirectCast(result, T)
                End Using
            End Using
        End Function

        Public Function ExecuteStoredProcedure(Of T)(procedureName As String, parameters As Dictionary(Of String, Object)) As List(Of T)
            Using connection As New SqlConnection(_connectionString)
                Using command As New SqlCommand(procedureName, connection)
                    command.CommandType = CommandType.StoredProcedure

                    ' إضافة المعاملات
                    If parameters IsNot Nothing Then
                        For Each param In parameters
                            command.Parameters.AddWithValue(param.Key, param.Value)
                        Next
                    End If

                    ' فتح الاتصال
                    connection.Open()

                    ' تنفيذ الإجراء المخزن
                    Using reader As SqlDataReader = command.ExecuteReader()
                        Dim results As New List(Of T)
                        While reader.Read()
                            ' تحويل البيانات إلى الكائن المطلوب
                            Dim item = Activator.CreateInstance(Of T)()
                            For i As Integer = 0 To reader.FieldCount - 1
                                Dim property = GetType(T).GetProperty(reader.GetName(i))
                                If property IsNot Nothing AndAlso Not reader.IsDBNull(i) Then
                                    property.SetValue(item, reader.GetValue(i))
                                End If
                            Next
                            results.Add(item)
                        End While
                        Return results
                    End Using
                End Using
            End Using
        End Function

        Public Function ExecuteTransaction(queries As List(Of String), parameters As List(Of Dictionary(Of String, Object))) As Boolean
            Using connection As New SqlConnection(_connectionString)
                connection.Open()
                Using transaction As SqlTransaction = connection.BeginTransaction()
                    Try
                        For i As Integer = 0 To queries.Count - 1
                            Using command As New SqlCommand(queries(i), connection, transaction)
                                ' إضافة المعاملات
                                If parameters IsNot Nothing AndAlso parameters.Count > i AndAlso parameters(i) IsNot Nothing Then
                                    For Each param In parameters(i)
                                        command.Parameters.AddWithValue(param.Key, param.Value)
                                    Next
                                End If

                                ' تنفيذ الاستعلام
                                command.ExecuteNonQuery()
                            End Using
                        Next

                        ' تأكيد العملية
                        transaction.Commit()
                        Return True
                    Catch ex As Exception
                        ' التراجع عن العملية في حالة حدوث خطأ
                        transaction.Rollback()
                        Throw
                    End Try
                End Using
            End Using
        End Function
    End Class
End Namespace
