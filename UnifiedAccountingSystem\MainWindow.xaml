<?xml version="1.0" encoding="utf-8"?>
<Window x:Class="MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="نظام إدارة الرواتب والمحاسبة" Height="720" Width="1280"
        WindowStartupLocation="CenterScreen"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.FontWeight="Medium"
        TextElement.FontSize="14"
        FontFamily="Cairo"
        FlowDirection="RightToLeft">
    
    <Window.Resources>
        <Storyboard x:Key="MenuOpen">
            <DoubleAnimation
                Storyboard.TargetProperty="Width"
                From="60" To="250" Duration="0:0:0.2"/>
        </Storyboard>
        <Storyboard x:Key="MenuClose">
            <DoubleAnimation
                Storyboard.TargetProperty="Width"
                From="250" To="60" Duration="0:0:0.2"/>
        </Storyboard>
    </Window.Resources>
    
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="250"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- القائمة الجانبية -->
        <Grid x:Name="GridMenu" Grid.Column="0" Background="{DynamicResource PrimaryHueDarkBrush}">
            <Grid.RowDefinitions>
                <RowDefinition Height="50"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="60"/>
            </Grid.RowDefinitions>
            
            <!-- رأس القائمة -->
            <Grid Grid.Row="0" Background="Transparent">
                <TextBlock Text="نظام الرواتب" 
                         Foreground="White" FontSize="18" 
                         HorizontalAlignment="Center"
                         VerticalAlignment="Center"/>
                <ToggleButton x:Name="ButtonToggleMenu" 
                            Width="30" Height="30"
                            Margin="10,0,0,0"
                            HorizontalAlignment="Left"
                            Style="{StaticResource MaterialDesignHamburgerToggleButton}"
                            Foreground="White"/>
            </Grid>
                
            <!-- قائمة الخيارات -->
            <ListView Grid.Row="1" Margin="0,20" Background="Transparent" 
                      BorderThickness="0" Foreground="White"
                      x:Name="ListViewMenu"
                      SelectionChanged="ListViewMenu_SelectionChanged">
                <ListViewItem Height="50" Tag="Users">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="AccountMultiple" Width="25" Height="25" Margin="10,0"/>
                        <TextBlock Text="المستخدمين" VerticalAlignment="Center"/>
                    </StackPanel>
                </ListViewItem>

                <ListViewItem Height="50" Tag="Settings">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Cog" Width="25" Height="25" Margin="10,0"/>
                        <TextBlock Text="الإعدادات والتهيئة" VerticalAlignment="Center"/>
                    </StackPanel>
                </ListViewItem>

                <ListViewItem Height="50" Tag="Accounts">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Bank" Width="25" Height="25" Margin="10,0"/>
                        <TextBlock Text="الحسابات" VerticalAlignment="Center"/>
                    </StackPanel>
                </ListViewItem>

                <ListViewItem Height="50" Tag="Employees">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="AccountTie" Width="25" Height="25" Margin="10,0"/>
                        <TextBlock Text="الموظفين" VerticalAlignment="Center"/>
                    </StackPanel>
                </ListViewItem>

                <ListViewItem Height="50" Tag="Salaries">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="CashMultiple" Width="25" Height="25" Margin="10,0"/>
                        <TextBlock Text="الرواتب" VerticalAlignment="Center"/>
                    </StackPanel>
                </ListViewItem>

                <ListViewItem Height="50" Tag="Reports">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="ChartBar" Width="25" Height="25" Margin="10,0"/>
                        <TextBlock Text="التقارير" VerticalAlignment="Center"/>
                    </StackPanel>
                </ListViewItem>
            </ListView>

            <!-- معلومات المستخدم -->
            <Grid Grid.Row="2" Background="{DynamicResource PrimaryHueMidBrush}">
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                    <Border Width="30" Height="30" CornerRadius="50" Background="White" Margin="0,0,10,0">
                        <materialDesign:PackIcon Kind="Account" Width="20" Height="20" 
                                               VerticalAlignment="Center" HorizontalAlignment="Center"/>
                    </Border>
                    <TextBlock x:Name="UserNameText" Text="اسم المستخدم" Foreground="White" 
                             VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Grid>

        <!-- منطقة عرض المحتوى -->
        <Grid Grid.Column="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="50"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- شريط العنوان -->
            <Grid Grid.Row="0" Background="White">
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,0,20,0">
                    <TextBlock x:Name="CurrentPageTitle" Text="الصفحة الرئيسية" 
                             VerticalAlignment="Center" FontSize="18"/>
                    <materialDesign:PackIcon Kind="ChevronLeft" Width="25" Height="25" 
                                           VerticalAlignment="Center" Margin="10,0"/>
                </StackPanel>
            </Grid>

            <!-- منطقة عرض الصفحات -->
            <Frame x:Name="PageContent" Grid.Row="1" NavigationUIVisibility="Hidden"/>
        </Grid>
    </Grid>
</Window>
