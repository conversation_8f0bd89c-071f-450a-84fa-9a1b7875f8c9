{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-user-plus me-2 text-primary"></i>
                إضافة موظف جديد
            </h1>
            <a href="/employees" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة إلى قائمة الموظفين
            </a>
        </div>
    </div>
</div>

<form method="post" class="needs-validation" novalidate>
    <div class="row">
        <!-- البيانات الأساسية -->
        <div class="col-lg-8">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-user me-2"></i>
                        البيانات الأساسية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="employee_number" class="form-label">
                                <i class="fas fa-id-badge me-1"></i>
                                الرقم الوظيفي <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="employee_number" name="employee_number" 
                                   required placeholder="مثال: 12345">
                            <div class="invalid-feedback">
                                يرجى إدخال الرقم الوظيفي
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="full_name" class="form-label">
                                <i class="fas fa-user me-1"></i>
                                الاسم الكامل <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="full_name" name="full_name" 
                                   required placeholder="الاسم الثلاثي أو الرباعي">
                            <div class="invalid-feedback">
                                يرجى إدخال الاسم الكامل
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="job_title" class="form-label">
                                <i class="fas fa-briefcase me-1"></i>
                                العنوان الوظيفي <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="job_title" name="job_title" 
                                   required placeholder="مثال: محاسب، كاتب، مدير">
                            <div class="invalid-feedback">
                                يرجى إدخال العنوان الوظيفي
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="job_grade" class="form-label">
                                <i class="fas fa-layer-group me-1"></i>
                                الدرجة الوظيفية <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" id="job_grade" name="job_grade" required>
                                <option value="">اختر الدرجة الوظيفية</option>
                                <option value="الأولى">الأولى</option>
                                <option value="الثانية">الثانية</option>
                                <option value="الثالثة">الثالثة</option>
                                <option value="الرابعة">الرابعة</option>
                                <option value="الخامسة">الخامسة</option>
                                <option value="السادسة">السادسة</option>
                                <option value="السابعة">السابعة</option>
                                <option value="الثامنة">الثامنة</option>
                                <option value="التاسعة">التاسعة</option>
                                <option value="العاشرة">العاشرة</option>
                            </select>
                            <div class="invalid-feedback">
                                يرجى اختيار الدرجة الوظيفية
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="qualification" class="form-label">
                                <i class="fas fa-graduation-cap me-1"></i>
                                الشهادة
                            </label>
                            <select class="form-select" id="qualification" name="qualification">
                                <option value="">اختر الشهادة</option>
                                <option value="ابتدائية">ابتدائية</option>
                                <option value="متوسطة">متوسطة</option>
                                <option value="إعدادية">إعدادية</option>
                                <option value="دبلوم">دبلوم</option>
                                <option value="بكالوريوس">بكالوريوس</option>
                                <option value="ماجستير">ماجستير</option>
                                <option value="دكتوراه">دكتوراه</option>
                            </select>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="stage" class="form-label">
                                <i class="fas fa-stairs me-1"></i>
                                المرحلة
                            </label>
                            <input type="text" class="form-control" id="stage" name="stage" 
                                   placeholder="مثال: الأولى، الثانية">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="basic_salary" class="form-label">
                                <i class="fas fa-money-bill-wave me-1"></i>
                                الراتب الأساسي <span class="text-danger">*</span>
                            </label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="basic_salary" name="basic_salary" 
                                       required min="0" step="1000" placeholder="0">
                                <span class="input-group-text">دينار عراقي</span>
                            </div>
                            <div class="invalid-feedback">
                                يرجى إدخال الراتب الأساسي
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="hire_date" class="form-label">
                                <i class="fas fa-calendar me-1"></i>
                                تاريخ التعيين
                            </label>
                            <input type="date" class="form-control" id="hire_date" name="hire_date">
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- معلومات إضافية -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        معلومات إضافية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="national_id" class="form-label">
                                <i class="fas fa-id-card me-1"></i>
                                رقم الهوية الوطنية
                            </label>
                            <input type="text" class="form-control" id="national_id" name="national_id" 
                                   placeholder="رقم الهوية أو البطاقة الموحدة">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="birth_date" class="form-label">
                                <i class="fas fa-birthday-cake me-1"></i>
                                تاريخ الميلاد
                            </label>
                            <input type="date" class="form-control" id="birth_date" name="birth_date">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">
                                <i class="fas fa-phone me-1"></i>
                                رقم الهاتف
                            </label>
                            <input type="tel" class="form-control" id="phone" name="phone" 
                                   placeholder="07xxxxxxxxx">
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">
                                <i class="fas fa-envelope me-1"></i>
                                البريد الإلكتروني
                            </label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   placeholder="<EMAIL>">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="address" class="form-label">
                            <i class="fas fa-map-marker-alt me-1"></i>
                            العنوان
                        </label>
                        <textarea class="form-control" id="address" name="address" rows="3" 
                                  placeholder="العنوان الكامل"></textarea>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- الهيكل التنظيمي -->
        <div class="col-lg-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-sitemap me-2"></i>
                        الهيكل التنظيمي
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="ministry" class="form-label">الوزارة</label>
                        <input type="text" class="form-control" id="ministry" name="ministry" 
                               value="وزارة الشباب والرياضة" readonly>
                    </div>
                    
                    <div class="mb-3">
                        <label for="department" class="form-label">الدائرة</label>
                        <select class="form-select" id="department" name="department">
                            <option value="دائرة الطب الرياضي" selected>دائرة الطب الرياضي</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="section" class="form-label">القسم</label>
                        <select class="form-select" id="section" name="section">
                            <option value="قسم إداري" selected>قسم إداري</option>
                            <option value="قسم فني">قسم فني</option>
                            <option value="قسم طبي">قسم طبي</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="division" class="form-label">الشعبة</label>
                        <select class="form-select" id="division" name="division">
                            <option value="شعبة الحسابات" selected>شعبة الحسابات</option>
                            <option value="شعبة الموارد البشرية">شعبة الموارد البشرية</option>
                            <option value="شعبة الخدمات">شعبة الخدمات</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <!-- معاينة الراتب -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-calculator me-2"></i>
                        معاينة الراتب
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between mb-2">
                        <span>الراتب الأساسي:</span>
                        <span id="preview_basic_salary">0 د.ع</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>المخصصات المتوقعة:</span>
                        <span id="preview_allowances">0 د.ع</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>الاستقطاعات المتوقعة:</span>
                        <span id="preview_deductions" class="text-danger">0 د.ع</span>
                    </div>
                    <hr>
                    <div class="d-flex justify-content-between">
                        <strong>الراتب الصافي المتوقع:</strong>
                        <strong id="preview_net_salary" class="text-success">0 د.ع</strong>
                    </div>
                    <small class="text-muted">
                        * هذه معاينة تقريبية. يمكن إضافة المخصصات والاستقطاعات لاحقاً
                    </small>
                </div>
            </div>
        </div>
    </div>
    
    <!-- أزرار الحفظ -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <button type="submit" class="btn btn-primary btn-lg me-2">
                                <i class="fas fa-save me-1"></i>
                                حفظ الموظف
                            </button>
                            <button type="button" class="btn btn-success btn-lg" onclick="saveAndAddAnother()">
                                <i class="fas fa-plus me-1"></i>
                                حفظ وإضافة آخر
                            </button>
                        </div>
                        <div>
                            <a href="/employees" class="btn btn-outline-secondary btn-lg">
                                <i class="fas fa-times me-1"></i>
                                إلغاء
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
{% endblock %}

{% block extra_css %}
<style>
.card-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
}

.form-label {
    font-weight: 600;
    color: #495057;
}

.text-danger {
    color: #dc3545 !important;
}

.input-group-text {
    background-color: #e9ecef;
    border-color: #ced4da;
}

.preview-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.was-validated .form-control:valid {
    border-color: #28a745;
}

.was-validated .form-control:invalid {
    border-color: #dc3545;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// التحقق من صحة النموذج
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// معاينة الراتب
function updateSalaryPreview() {
    const basicSalary = parseFloat(document.getElementById('basic_salary').value) || 0;
    
    // حساب تقريبي للمخصصات (يمكن تخصيصه لاحقاً)
    const estimatedAllowances = basicSalary * 0.2; // 20% تقريباً
    
    // حساب الاستقطاعات الأساسية
    const retirement = basicSalary * 0.10; // تقاعد 10%
    const govContribution = basicSalary * 0.15; // مساهمة حكومية 15%
    const totalDeductions = retirement + govContribution;
    
    // الراتب الصافي
    const netSalary = basicSalary + estimatedAllowances - totalDeductions;
    
    // تحديث العرض
    document.getElementById('preview_basic_salary').textContent = formatCurrency(basicSalary);
    document.getElementById('preview_allowances').textContent = formatCurrency(estimatedAllowances);
    document.getElementById('preview_deductions').textContent = formatCurrency(totalDeductions);
    document.getElementById('preview_net_salary').textContent = formatCurrency(netSalary);
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-IQ').format(amount) + ' د.ع';
}

// ربط حدث تغيير الراتب الأساسي
document.getElementById('basic_salary').addEventListener('input', updateSalaryPreview);

// حفظ وإضافة آخر
function saveAndAddAnother() {
    const form = document.querySelector('form');
    const input = document.createElement('input');
    input.type = 'hidden';
    input.name = 'add_another';
    input.value = '1';
    form.appendChild(input);
    form.submit();
}

// تنسيق رقم الهاتف
document.getElementById('phone').addEventListener('input', function() {
    let value = this.value.replace(/\D/g, '');
    if (value.length > 11) {
        value = value.substring(0, 11);
    }
    this.value = value;
});

// تنسيق الرقم الوظيفي
document.getElementById('employee_number').addEventListener('input', function() {
    this.value = this.value.replace(/\D/g, '');
});

// تحديث معاينة الراتب عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    updateSalaryPreview();
});
</script>
{% endblock %}
