Imports System.Windows
Imports System.Windows.Controls
Imports System.Threading.Tasks
Imports UnifiedAccountingSystem.Services
Imports UnifiedAccountingSystem.Models

Public Class LoginView
    Private _databaseService As DatabaseService
    Private _isLoggingIn As Boolean = False

    Public Sub New()
        InitializeComponent()
        _databaseService = New DatabaseService()
        
        ' إعداد النافذة
        InitializeWindow()
        
        ' إضافة معالجات الأحداث
        AddHandler KeyDown, AddressOf LoginView_KeyDown
        AddHandler UsernameTextBox.KeyDown, AddressOf TextBox_KeyDown
        AddHandler PasswordBox.KeyDown, AddressOf PasswordBox_KeyDown
    End Sub

    Private Sub InitializeWindow()
        ' تعيين القيم الافتراضية للاختبار
        UsernameTextBox.Text = "admin"
        PasswordBox.Password = "admin123"
        
        ' تركيز على حقل اسم المستخدم
        UsernameTextBox.Focus()
        
        ' إخفاء رسالة الخطأ
        ErrorMessage.Visibility = Visibility.Collapsed
    End Sub

    Private Sub CloseButton_Click(sender As Object, e As RoutedEventArgs)
        Application.Current.Shutdown()
    End Sub

    Private Sub MinimizeButton_Click(sender As Object, e As RoutedEventArgs)
        WindowState = WindowState.Minimized
    End Sub

    Private Async Sub LoginButton_Click(sender As Object, e As RoutedEventArgs)
        Await PerformLogin()
    End Sub

    Private Sub LoginView_KeyDown(sender As Object, e As KeyEventArgs)
        If e.Key = Key.Enter Then
            Await PerformLogin()
        ElseIf e.Key = Key.Escape Then
            Application.Current.Shutdown()
        End If
    End Sub

    Private Sub TextBox_KeyDown(sender As Object, e As KeyEventArgs)
        If e.Key = Key.Enter Then
            PasswordBox.Focus()
        End If
    End Sub

    Private Sub PasswordBox_KeyDown(sender As Object, e As KeyEventArgs)
        If e.Key = Key.Enter Then
            Await PerformLogin()
        End If
    End Sub

    Private Async Function PerformLogin() As Task
        ' منع تسجيل الدخول المتعدد
        If _isLoggingIn Then Return
        _isLoggingIn = True

        Try
            ' إخفاء رسالة الخطأ
            ErrorMessage.Visibility = Visibility.Collapsed

            ' التحقق من صحة البيانات المدخلة
            If Not ValidateInput() Then
                _isLoggingIn = False
                Return
            End If

            ' تعطيل الواجهة أثناء تسجيل الدخول
            SetUIEnabled(False)
            LoginButton.Content = "جاري تسجيل الدخول..."

            ' محاولة تسجيل الدخول
            Dim username As String = UsernameTextBox.Text.Trim()
            Dim password As String = PasswordBox.Password

            ' التحقق من بيانات المستخدم
            Dim user As User = Await AuthenticateUser(username, password)

            If user IsNot Nothing Then
                ' تسجيل دخول ناجح
                Await ShowSuccessMessage()
                
                ' حفظ بيانات المستخدم إذا كان مطلوباً
                If RememberCheckBox.IsChecked Then
                    SaveUserCredentials(username)
                End If

                ' فتح النافذة الرئيسية
                OpenMainWindow(user)
            Else
                ' فشل في تسجيل الدخول
                ShowErrorMessage("اسم المستخدم أو كلمة المرور غير صحيحة")
            End If

        Catch ex As Exception
            ShowErrorMessage($"حدث خطأ أثناء تسجيل الدخول: {ex.Message}")
        Finally
            ' إعادة تفعيل الواجهة
            SetUIEnabled(True)
            LoginButton.Content = "تسجيل الدخول"
            _isLoggingIn = False
        End Try
    End Function

    Private Function ValidateInput() As Boolean
        ' التحقق من اسم المستخدم
        If String.IsNullOrWhiteSpace(UsernameTextBox.Text) Then
            ShowErrorMessage("يرجى إدخال اسم المستخدم")
            UsernameTextBox.Focus()
            Return False
        End If

        ' التحقق من كلمة المرور
        If String.IsNullOrWhiteSpace(PasswordBox.Password) Then
            ShowErrorMessage("يرجى إدخال كلمة المرور")
            PasswordBox.Focus()
            Return False
        End If

        Return True
    End Function

    Private Async Function AuthenticateUser(username As String, password As String) As Task(Of User)
        ' للاختبار - استخدام بيانات ثابتة
        If username = "admin" AndAlso password = "admin123" Then
            Await Task.Delay(1500) ' محاكاة وقت الاستعلام
            Return New User With {
                .UserId = 1,
                .Username = "admin",
                .FullName = "مدير النظام",
                .Role = "Administrator",
                .IsActive = True,
                .LastLogin = DateTime.Now
            }
        End If

        ' في التطبيق الحقيقي، سيتم الاستعلام من قاعدة البيانات
        ' Dim query = "SELECT * FROM Users WHERE Username = @username AND Password = @password AND IsActive = 1"
        ' Dim parameters = New Dictionary(Of String, Object) From {
        '     {"@username", username},
        '     {"@password", HashPassword(password)}
        ' }
        ' Dim users = _databaseService.ExecuteQuery(Of User)(query, parameters)
        ' Return If(users.Count > 0, users(0), Nothing)

        Await Task.Delay(1500) ' محاكاة وقت الاستعلام
        Return Nothing
    End Function

    Private Async Function ShowSuccessMessage() As Task
        ErrorMessage.Text = "تم تسجيل الدخول بنجاح!"
        ErrorMessage.Foreground = Brushes.Green
        ErrorMessage.Visibility = Visibility.Visible
        Await Task.Delay(1000)
    End Function

    Private Sub ShowErrorMessage(message As String)
        ErrorMessage.Text = message
        ErrorMessage.Foreground = Brushes.Red
        ErrorMessage.Visibility = Visibility.Visible
    End Sub

    Private Sub SetUIEnabled(enabled As Boolean)
        UsernameTextBox.IsEnabled = enabled
        PasswordBox.IsEnabled = enabled
        RememberCheckBox.IsEnabled = enabled
        LoginButton.IsEnabled = enabled
    End Sub

    Private Sub SaveUserCredentials(username As String)
        ' حفظ اسم المستخدم في إعدادات التطبيق
        ' يمكن استخدام Registry أو ملف إعدادات
        Try
            My.Settings.RememberedUsername = username
            My.Settings.Save()
        Catch ex As Exception
            ' تجاهل الأخطاء في حفظ الإعدادات
        End Try
    End Sub

    Private Sub OpenMainWindow(user As User)
        ' إنشاء وفتح النافذة الرئيسية
        Dim mainWindow As New MainWindow()
        
        ' تمرير بيانات المستخدم للنافذة الرئيسية
        mainWindow.CurrentUser = user
        mainWindow.Show()
        
        ' إغلاق نافذة تسجيل الدخول
        Me.Close()
    End Sub

    Private Function HashPassword(password As String) As String
        ' تشفير كلمة المرور باستخدام SHA256 أو BCrypt
        ' هذا مثال بسيط - في التطبيق الحقيقي يجب استخدام تشفير أقوى
        Using sha256 As System.Security.Cryptography.SHA256 = System.Security.Cryptography.SHA256.Create()
            Dim hashedBytes As Byte() = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes(password))
            Return Convert.ToBase64String(hashedBytes)
        End Using
    End Function

    Private Sub LoginView_Loaded(sender As Object, e As RoutedEventArgs) Handles Me.Loaded
        ' تحميل اسم المستخدم المحفوظ إذا كان متاحاً
        Try
            If Not String.IsNullOrEmpty(My.Settings.RememberedUsername) Then
                UsernameTextBox.Text = My.Settings.RememberedUsername
                RememberCheckBox.IsChecked = True
                PasswordBox.Focus()
            End If
        Catch ex As Exception
            ' تجاهل الأخطاء في تحميل الإعدادات
        End Try
    End Sub
End Class
