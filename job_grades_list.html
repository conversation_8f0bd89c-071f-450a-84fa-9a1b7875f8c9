<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الدرجات الوظيفية - نظام المحاسبة المتكامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: #f8f9fa;
        }
        
        .navbar {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .card {
            border: none;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
            border-radius: 12px;
        }
        
        .table th {
            background: #343a40;
            color: white;
            text-align: center;
            border: none;
        }
        
        .table td {
            vertical-align: middle;
            text-align: center;
        }
        
        .grade-badge {
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: bold;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }
        
        .grade-special {
            background: linear-gradient(45deg, #dc3545, #c82333);
            color: white;
        }
        
        .grade-1 {
            background: linear-gradient(45deg, #fd7e14, #e55a00);
            color: white;
        }
        
        .grade-2 {
            background: linear-gradient(45deg, #ffc107, #e0a800);
            color: #333;
        }
        
        .grade-3 {
            background: linear-gradient(45deg, #28a745, #1e7e34);
            color: white;
        }
        
        .grade-4 {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
        }
        
        .grade-5 {
            background: linear-gradient(45deg, #6f42c1, #5a2d91);
            color: white;
        }
        
        .grade-6 {
            background: linear-gradient(45deg, #6c757d, #545b62);
            color: white;
        }
        
        .salary-range {
            background: #e8f5e8;
            border: 1px solid #28a745;
            border-radius: 8px;
            padding: 8px 12px;
            font-weight: bold;
            color: #155724;
        }
        
        .search-box {
            border-radius: 25px;
            border: 2px solid #e9ecef;
            padding: 10px 20px;
        }
        
        .search-box:focus {
            border-color: #17a2b8;
            box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.25);
        }
        
        .action-buttons .btn {
            margin: 2px;
        }
        
        .promotion-path {
            display: flex;
            align-items: center;
            gap: 8px;
            justify-content: center;
        }
        
        .promotion-arrow {
            color: #28a745;
            font-size: 1.2rem;
        }
        
        .grade-stats {
            background: linear-gradient(135deg, #17a2b8, #138496);
            color: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="simple_homepage.html">
                <i class="fas fa-calculator me-2"></i>
                نظام المحاسبة المتكامل
            </a>
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="simple_homepage.html">
                    <i class="fas fa-home me-1"></i>الرئيسية
                </a>
                <a class="nav-link" href="employees_page.html">
                    <i class="fas fa-users me-1"></i>الموظفون
                </a>
                <a class="nav-link" href="basic_lists_page.html">
                    <i class="fas fa-list me-1"></i>القوائم الأساسية
                </a>
                <a class="nav-link active" href="job_grades_list.html">
                    <i class="fas fa-layer-group me-1"></i>الدرجات الوظيفية
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- عنوان الصفحة -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2 class="text-info">
                        <i class="fas fa-layer-group me-2"></i>
                        الدرجات الوظيفية
                    </h2>
                    <div>
                        <button class="btn btn-info me-2" onclick="showAddGradeModal()">
                            <i class="fas fa-plus me-1"></i>إضافة درجة
                        </button>
                        <a href="basic_lists_page.html" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right me-1"></i>العودة للقوائم
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات الدرجات -->
        <div class="grade-stats">
            <div class="row text-center">
                <div class="col-md-3">
                    <h3><i class="fas fa-layer-group me-2"></i>10</h3>
                    <p class="mb-0">إجمالي الدرجات</p>
                </div>
                <div class="col-md-3">
                    <h3><i class="fas fa-users me-2"></i>125</h3>
                    <p class="mb-0">إجمالي الموظفين</p>
                </div>
                <div class="col-md-3">
                    <h3><i class="fas fa-money-bill-wave me-2"></i>750,000</h3>
                    <p class="mb-0">متوسط الراتب (د.ع)</p>
                </div>
                <div class="col-md-3">
                    <h3><i class="fas fa-chart-line me-2"></i>15%</h3>
                    <p class="mb-0">نسبة الترقيات السنوية</p>
                </div>
            </div>
        </div>

        <!-- مسار الترقية -->
        <div class="card mb-4">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-route me-2"></i>
                    مسار الترقية الوظيفية
                </h5>
            </div>
            <div class="card-body">
                <div class="promotion-path">
                    <span class="grade-badge grade-6">السادسة</span>
                    <i class="fas fa-arrow-left promotion-arrow"></i>
                    <span class="grade-badge grade-5">الخامسة</span>
                    <i class="fas fa-arrow-left promotion-arrow"></i>
                    <span class="grade-badge grade-4">الرابعة</span>
                    <i class="fas fa-arrow-left promotion-arrow"></i>
                    <span class="grade-badge grade-3">الثالثة</span>
                    <i class="fas fa-arrow-left promotion-arrow"></i>
                    <span class="grade-badge grade-2">الثانية</span>
                    <i class="fas fa-arrow-left promotion-arrow"></i>
                    <span class="grade-badge grade-1">الأولى</span>
                    <i class="fas fa-arrow-left promotion-arrow"></i>
                    <span class="grade-badge grade-special">خاص</span>
                </div>
                <p class="text-center mt-3 text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    الترقية تتم بناءً على سنوات الخدمة والأداء والمؤهلات
                </p>
            </div>
        </div>

        <!-- شريط البحث والفلترة -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-search"></i>
                                    </span>
                                    <input type="text" class="form-control search-box" placeholder="البحث عن درجة..." id="searchInput">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="typeFilter">
                                    <option value="">جميع الأنواع</option>
                                    <option value="عادية">عادية</option>
                                    <option value="خاصة">خاصة</option>
                                    <option value="استثنائية">استثنائية</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="statusFilter">
                                    <option value="">جميع الحالات</option>
                                    <option value="نشط">نشط</option>
                                    <option value="معطل">معطل</option>
                                    <option value="مؤقت">مؤقت</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button class="btn btn-outline-secondary w-100" onclick="clearFilters()">
                                    <i class="fas fa-times me-1"></i>مسح
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- جدول الدرجات الوظيفية -->
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>قائمة الدرجات الوظيفية
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0" id="gradesTable">
                        <thead>
                            <tr>
                                <th>الرمز</th>
                                <th>الدرجة</th>
                                <th>النوع</th>
                                <th>الراتب الأساسي</th>
                                <th>سنوات الخدمة المطلوبة</th>
                                <th>عدد الموظفين</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="gradesTableBody">
                            <tr>
                                <td><strong>GR-SP</strong></td>
                                <td><span class="grade-badge grade-special"><i class="fas fa-crown"></i>خاص</span></td>
                                <td><span class="badge bg-danger">استثنائية</span></td>
                                <td><div class="salary-range">1,500,000 د.ع</div></td>
                                <td><span class="badge bg-info">25+ سنة</span></td>
                                <td><span class="badge bg-success">2</span></td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewGrade('GR-SP')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editGrade('GR-SP')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="الموظفين" onclick="viewGradeEmployees('GR-SP')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>GR-01</strong></td>
                                <td><span class="grade-badge grade-1"><i class="fas fa-medal"></i>الأولى</span></td>
                                <td><span class="badge bg-warning">خاصة</span></td>
                                <td><div class="salary-range">1,200,000 د.ع</div></td>
                                <td><span class="badge bg-info">20+ سنة</span></td>
                                <td><span class="badge bg-success">5</span></td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewGrade('GR-01')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editGrade('GR-01')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="الموظفين" onclick="viewGradeEmployees('GR-01')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>GR-02</strong></td>
                                <td><span class="grade-badge grade-2"><i class="fas fa-star"></i>الثانية</span></td>
                                <td><span class="badge bg-primary">عادية</span></td>
                                <td><div class="salary-range">950,000 د.ع</div></td>
                                <td><span class="badge bg-info">15+ سنة</span></td>
                                <td><span class="badge bg-success">8</span></td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewGrade('GR-02')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editGrade('GR-02')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="الموظفين" onclick="viewGradeEmployees('GR-02')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>GR-03</strong></td>
                                <td><span class="grade-badge grade-3"><i class="fas fa-certificate"></i>الثالثة</span></td>
                                <td><span class="badge bg-primary">عادية</span></td>
                                <td><div class="salary-range">750,000 د.ع</div></td>
                                <td><span class="badge bg-info">12+ سنة</span></td>
                                <td><span class="badge bg-success">12</span></td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewGrade('GR-03')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editGrade('GR-03')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="الموظفين" onclick="viewGradeEmployees('GR-03')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>GR-04</strong></td>
                                <td><span class="grade-badge grade-4"><i class="fas fa-award"></i>الرابعة</span></td>
                                <td><span class="badge bg-primary">عادية</span></td>
                                <td><div class="salary-range">650,000 د.ع</div></td>
                                <td><span class="badge bg-info">8+ سنة</span></td>
                                <td><span class="badge bg-success">18</span></td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewGrade('GR-04')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editGrade('GR-04')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="الموظفين" onclick="viewGradeEmployees('GR-04')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>GR-05</strong></td>
                                <td><span class="grade-badge grade-5"><i class="fas fa-ribbon"></i>الخامسة</span></td>
                                <td><span class="badge bg-primary">عادية</span></td>
                                <td><div class="salary-range">580,000 د.ع</div></td>
                                <td><span class="badge bg-info">5+ سنة</span></td>
                                <td><span class="badge bg-success">25</span></td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewGrade('GR-05')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editGrade('GR-05')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="الموظفين" onclick="viewGradeEmployees('GR-05')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>GR-06</strong></td>
                                <td><span class="grade-badge grade-6"><i class="fas fa-user-graduate"></i>السادسة</span></td>
                                <td><span class="badge bg-primary">عادية</span></td>
                                <td><div class="salary-range">520,000 د.ع</div></td>
                                <td><span class="badge bg-info">0+ سنة</span></td>
                                <td><span class="badge bg-success">35</span></td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewGrade('GR-06')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editGrade('GR-06')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="الموظفين" onclick="viewGradeEmployees('GR-06')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between align-items-center">
                    <span class="text-muted">عرض 7 من أصل 10 درجات وظيفية</span>
                    <div>
                        <button class="btn btn-outline-info btn-sm me-2" onclick="calculatePromotions()">
                            <i class="fas fa-calculator me-1"></i>حساب الترقيات
                        </button>
                        <button class="btn btn-outline-success btn-sm me-2" onclick="exportGrades()">
                            <i class="fas fa-file-excel me-1"></i>تصدير Excel
                        </button>
                        <button class="btn btn-outline-primary btn-sm" onclick="printGrades()">
                            <i class="fas fa-print me-1"></i>طباعة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // وظائف البحث والفلترة
        function filterGrades() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const typeFilter = document.getElementById('typeFilter').value;
            const statusFilter = document.getElementById('statusFilter').value;
            
            const rows = document.querySelectorAll('#gradesTableBody tr');
            
            rows.forEach(row => {
                const grade = row.cells[1].textContent.toLowerCase();
                const type = row.cells[2].textContent;
                const status = row.cells[6].textContent;
                
                const matchesSearch = grade.includes(searchTerm);
                const matchesType = !typeFilter || type.includes(typeFilter);
                const matchesStatus = !statusFilter || status.includes(statusFilter);
                
                if (matchesSearch && matchesType && matchesStatus) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }
        
        function clearFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('typeFilter').value = '';
            document.getElementById('statusFilter').value = '';
            filterGrades();
        }
        
        // ربط أحداث البحث
        document.getElementById('searchInput').addEventListener('input', filterGrades);
        document.getElementById('typeFilter').addEventListener('change', filterGrades);
        document.getElementById('statusFilter').addEventListener('change', filterGrades);
        
        // وظائف الإجراءات
        function viewGrade(code) {
            alert(`عرض تفاصيل الدرجة: ${code}\n\nسيتم عرض:\n• تفاصيل الراتب والمخصصات\n• شروط الترقية\n• المتطلبات والمؤهلات\n• التاريخ والتطوير\n• الإحصائيات والتحليلات`);
        }
        
        function editGrade(code) {
            alert(`تعديل الدرجة: ${code}\n\nيمكن تعديل:\n• الراتب الأساسي\n• سنوات الخدمة المطلوبة\n• شروط الترقية\n• المخصصات الإضافية\n• الحالة والنوع`);
        }
        
        function viewGradeEmployees(code) {
            alert(`موظفو الدرجة: ${code}\n\nسيتم عرض:\n• قائمة الموظفين\n• تواريخ الترقية\n• الأداء والتقييمات\n• المؤهلين للترقية\n• الإحصائيات التفصيلية`);
        }
        
        function showAddGradeModal() {
            alert(`إضافة درجة وظيفية جديدة\n\nالحقول المطلوبة:\n• رمز الدرجة\n• اسم الدرجة\n• النوع (عادية/خاصة/استثنائية)\n• الراتب الأساسي\n• سنوات الخدمة المطلوبة\n• شروط الترقية`);
        }
        
        function calculatePromotions() {
            alert(`🧮 حساب الترقيات\n\nسيتم حساب:\n• الموظفين المؤهلين للترقية\n• التواريخ المتوقعة\n• التكلفة المالية\n• التأثير على الميزانية\n\nالنتيجة: 23 موظف مؤهل للترقية`);
        }
        
        function exportGrades() {
            alert(`📊 تصدير الدرجات الوظيفية\n\nسيتم إنشاء ملف Excel يحتوي على:\n• جميع الدرجات والتفاصيل\n• سلم الرواتب\n• إحصائيات الموظفين\n• مخططات الترقية\n\nحجم الملف المتوقع: 1.5 MB`);
        }
        
        function printGrades() {
            alert(`🖨️ طباعة الدرجات الوظيفية\n\nسيتم إعداد:\n• دليل الدرجات الوظيفية\n• سلم الرواتب الرسمي\n• مسارات الترقية\n• الإحصائيات والمخططات\n\nعدد الصفحات المتوقع: 20 صفحة`);
        }

        console.log('✅ صفحة الدرجات الوظيفية محملة بنجاح');
        console.log('🏆 10 درجات وظيفية متاحة');
        console.log('💰 نظام الرواتب مرتبط بالدرجات');
    </script>
</body>
</html>
