// نظام المحاسبة المتكامل - JavaScript الرئيسي

document.addEventListener('DOMContentLoaded', function() {
    // تهيئة النظام
    initializeSystem();
    
    // تهيئة المكونات
    initializeComponents();
    
    // تهيئة الأحداث
    initializeEvents();
});

// تهيئة النظام
function initializeSystem() {
    // إعداد اتجاه النص
    document.documentElement.setAttribute('dir', 'rtl');
    document.documentElement.setAttribute('lang', 'ar');
    
    // إعداد التوقيت المحلي
    setupLocalTime();
    
    // تحميل الإعدادات المحفوظة
    loadUserPreferences();
}

// تهيئة المكونات
function initializeComponents() {
    // تهيئة التلميحات
    initializeTooltips();
    
    // تهيئة النوافذ المنبثقة
    initializeModals();
    
    // تهيئة الجداول
    initializeTables();
    
    // تهيئة النماذج
    initializeForms();
    
    // تهيئة الرسوم البيانية
    initializeCharts();
}

// تهيئة الأحداث
function initializeEvents() {
    // أحداث النقر
    setupClickEvents();
    
    // أحداث النماذج
    setupFormEvents();
    
    // أحداث لوحة المفاتيح
    setupKeyboardEvents();
    
    // أحداث التمرير
    setupScrollEvents();
}

// إعداد التوقيت المحلي
function setupLocalTime() {
    const timeElements = document.querySelectorAll('[data-time]');
    timeElements.forEach(element => {
        const timestamp = element.getAttribute('data-time');
        if (timestamp) {
            const date = new Date(timestamp);
            element.textContent = formatArabicDate(date);
        }
    });
}

// تنسيق التاريخ بالعربية
function formatArabicDate(date) {
    const months = [
        'كانون الثاني', 'شباط', 'آذار', 'نيسان', 'أيار', 'حزيران',
        'تموز', 'آب', 'أيلول', 'تشرين الأول', 'تشرين الثاني', 'كانون الأول'
    ];
    
    const days = [
        'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'
    ];
    
    const day = days[date.getDay()];
    const dayNum = date.getDate();
    const month = months[date.getMonth()];
    const year = date.getFullYear();
    
    return `${day} ${dayNum} ${month} ${year}`;
}

// تحميل تفضيلات المستخدم
function loadUserPreferences() {
    const preferences = localStorage.getItem('userPreferences');
    if (preferences) {
        const prefs = JSON.parse(preferences);
        applyUserPreferences(prefs);
    }
}

// تطبيق تفضيلات المستخدم
function applyUserPreferences(preferences) {
    if (preferences.theme) {
        document.body.setAttribute('data-theme', preferences.theme);
    }
    
    if (preferences.fontSize) {
        document.body.style.fontSize = preferences.fontSize + 'px';
    }
}

// تهيئة التلميحات
function initializeTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// تهيئة النوافذ المنبثقة
function initializeModals() {
    const modalElements = document.querySelectorAll('.modal');
    modalElements.forEach(modal => {
        modal.addEventListener('shown.bs.modal', function() {
            const firstInput = modal.querySelector('input, select, textarea');
            if (firstInput) {
                firstInput.focus();
            }
        });
    });
}

// تهيئة الجداول
function initializeTables() {
    const tables = document.querySelectorAll('.table-sortable');
    tables.forEach(table => {
        makeSortable(table);
    });
    
    // تهيئة البحث في الجداول
    const searchInputs = document.querySelectorAll('[data-table-search]');
    searchInputs.forEach(input => {
        const tableId = input.getAttribute('data-table-search');
        const table = document.getElementById(tableId);
        if (table) {
            setupTableSearch(input, table);
        }
    });
}

// جعل الجدول قابل للترتيب
function makeSortable(table) {
    const headers = table.querySelectorAll('th[data-sortable]');
    headers.forEach(header => {
        header.style.cursor = 'pointer';
        header.addEventListener('click', function() {
            sortTable(table, this);
        });
    });
}

// ترتيب الجدول
function sortTable(table, header) {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    const columnIndex = Array.from(header.parentNode.children).indexOf(header);
    const isAscending = header.classList.contains('sort-asc');
    
    rows.sort((a, b) => {
        const aValue = a.children[columnIndex].textContent.trim();
        const bValue = b.children[columnIndex].textContent.trim();
        
        if (isNumeric(aValue) && isNumeric(bValue)) {
            return isAscending ? 
                parseFloat(bValue) - parseFloat(aValue) : 
                parseFloat(aValue) - parseFloat(bValue);
        } else {
            return isAscending ? 
                bValue.localeCompare(aValue, 'ar') : 
                aValue.localeCompare(bValue, 'ar');
        }
    });
    
    // إعادة ترتيب الصفوف
    rows.forEach(row => tbody.appendChild(row));
    
    // تحديث أيقونة الترتيب
    table.querySelectorAll('th').forEach(th => {
        th.classList.remove('sort-asc', 'sort-desc');
    });
    
    header.classList.add(isAscending ? 'sort-desc' : 'sort-asc');
}

// التحقق من كون القيمة رقمية
function isNumeric(value) {
    return !isNaN(parseFloat(value)) && isFinite(value);
}

// إعداد البحث في الجدول
function setupTableSearch(input, table) {
    input.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const rows = table.querySelectorAll('tbody tr');
        
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm) ? '' : 'none';
        });
    });
}

// تهيئة النماذج
function initializeForms() {
    // التحقق من صحة النماذج
    const forms = document.querySelectorAll('.needs-validation');
    forms.forEach(form => {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    });
    
    // تنسيق الأرقام
    const numberInputs = document.querySelectorAll('input[type="number"], .number-input');
    numberInputs.forEach(input => {
        input.addEventListener('input', function() {
            formatNumber(this);
        });
    });
    
    // التحقق من الحقول المطلوبة
    const requiredInputs = document.querySelectorAll('input[required], select[required], textarea[required]');
    requiredInputs.forEach(input => {
        input.addEventListener('blur', function() {
            validateField(this);
        });
    });
}

// تنسيق الأرقام
function formatNumber(input) {
    let value = input.value.replace(/[^\d.-]/g, '');
    if (value) {
        const number = parseFloat(value);
        if (!isNaN(number)) {
            input.value = number.toLocaleString('ar-IQ');
        }
    }
}

// التحقق من صحة الحقل
function validateField(field) {
    const value = field.value.trim();
    const isValid = field.checkValidity();
    
    field.classList.remove('is-valid', 'is-invalid');
    field.classList.add(isValid ? 'is-valid' : 'is-invalid');
    
    // عرض رسالة الخطأ
    const feedback = field.parentNode.querySelector('.invalid-feedback');
    if (feedback && !isValid) {
        feedback.style.display = 'block';
    }
}

// إعداد أحداث النقر
function setupClickEvents() {
    // أزرار الحذف
    const deleteButtons = document.querySelectorAll('.btn-delete');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            confirmDelete(this);
        });
    });
    
    // أزرار الطباعة
    const printButtons = document.querySelectorAll('.btn-print');
    printButtons.forEach(button => {
        button.addEventListener('click', function() {
            window.print();
        });
    });
    
    // أزرار التصدير
    const exportButtons = document.querySelectorAll('.btn-export');
    exportButtons.forEach(button => {
        button.addEventListener('click', function() {
            const format = this.getAttribute('data-format');
            exportData(format);
        });
    });
}

// تأكيد الحذف
function confirmDelete(button) {
    const itemName = button.getAttribute('data-item-name') || 'هذا العنصر';
    
    if (confirm(`هل أنت متأكد من حذف ${itemName}؟`)) {
        const form = button.closest('form');
        if (form) {
            form.submit();
        } else {
            const url = button.getAttribute('data-url');
            if (url) {
                window.location.href = url;
            }
        }
    }
}

// تصدير البيانات
function exportData(format) {
    const table = document.querySelector('.table-exportable');
    if (!table) return;
    
    switch (format) {
        case 'excel':
            exportToExcel(table);
            break;
        case 'pdf':
            exportToPDF(table);
            break;
        case 'csv':
            exportToCSV(table);
            break;
    }
}

// تصدير إلى Excel
function exportToExcel(table) {
    // يتطلب مكتبة خارجية مثل SheetJS
    console.log('تصدير إلى Excel');
}

// تصدير إلى PDF
function exportToPDF(table) {
    // يتطلب مكتبة خارجية مثل jsPDF
    console.log('تصدير إلى PDF');
}

// تصدير إلى CSV
function exportToCSV(table) {
    const rows = table.querySelectorAll('tr');
    const csv = [];
    
    rows.forEach(row => {
        const cols = row.querySelectorAll('td, th');
        const rowData = [];
        cols.forEach(col => {
            rowData.push(col.textContent.trim());
        });
        csv.push(rowData.join(','));
    });
    
    const csvContent = csv.join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = 'data.csv';
    link.click();
}

// إعداد أحداث لوحة المفاتيح
function setupKeyboardEvents() {
    document.addEventListener('keydown', function(e) {
        // Ctrl+S للحفظ
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            const saveButton = document.querySelector('.btn-save, .btn-primary[type="submit"]');
            if (saveButton) {
                saveButton.click();
            }
        }
        
        // Escape لإغلاق النوافذ المنبثقة
        if (e.key === 'Escape') {
            const openModal = document.querySelector('.modal.show');
            if (openModal) {
                const modal = bootstrap.Modal.getInstance(openModal);
                modal.hide();
            }
        }
    });
}

// إعداد أحداث التمرير
function setupScrollEvents() {
    let lastScrollTop = 0;
    const navbar = document.querySelector('.navbar');
    
    window.addEventListener('scroll', function() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        
        if (scrollTop > lastScrollTop && scrollTop > 100) {
            // التمرير لأسفل
            navbar.style.transform = 'translateY(-100%)';
        } else {
            // التمرير لأعلى
            navbar.style.transform = 'translateY(0)';
        }
        
        lastScrollTop = scrollTop;
    });
}

// تهيئة الرسوم البيانية
function initializeCharts() {
    const chartElements = document.querySelectorAll('[data-chart]');
    chartElements.forEach(element => {
        const chartType = element.getAttribute('data-chart');
        const chartData = JSON.parse(element.getAttribute('data-chart-data') || '{}');
        createChart(element, chartType, chartData);
    });
}

// إنشاء رسم بياني
function createChart(element, type, data) {
    // يتطلب مكتبة Chart.js
    console.log(`إنشاء رسم بياني من نوع ${type}`);
}

// وظائف مساعدة
const Utils = {
    // عرض رسالة نجاح
    showSuccess: function(message) {
        this.showAlert(message, 'success');
    },
    
    // عرض رسالة خطأ
    showError: function(message) {
        this.showAlert(message, 'danger');
    },
    
    // عرض تنبيه
    showAlert: function(message, type) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        const container = document.querySelector('.container-fluid');
        container.insertBefore(alertDiv, container.firstChild);
        
        // إزالة التنبيه تلقائياً بعد 5 ثوان
        setTimeout(() => {
            alertDiv.remove();
        }, 5000);
    },
    
    // تحويل الأرقام إلى العربية
    toArabicNumbers: function(str) {
        const arabicNumbers = '٠١٢٣٤٥٦٧٨٩';
        return str.replace(/[0-9]/g, function(w) {
            return arabicNumbers[+w];
        });
    },
    
    // تحويل الأرقام إلى الإنجليزية
    toEnglishNumbers: function(str) {
        return str.replace(/[٠-٩]/g, function(w) {
            return '٠١٢٣٤٥٦٧٨٩'.indexOf(w);
        });
    }
};

// تصدير الوظائف للاستخدام العام
window.AccountingSystem = {
    Utils: Utils,
    formatArabicDate: formatArabicDate,
    exportData: exportData
};
