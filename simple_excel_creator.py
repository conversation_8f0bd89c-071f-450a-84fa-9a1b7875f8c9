#!/usr/bin/env python3
"""
إنشاء ملف Excel بسيط للاختبار
"""

try:
    import pandas as pd
    
    # بيانات بسيطة للاختبار
    data = [
        ['12001', 'أحمد محمد علي', 'محاسب أول', 'الثالثة', 750000, 'بكالوريوس', 'الأولى', '07901234567', '<EMAIL>', 'بغداد'],
        ['12002', 'فاطمة حسن محمود', 'كاتبة', 'الخامسة', 580000, 'إعدادية', 'الثانية', '07801234567', '<EMAIL>', 'بغداد'],
        ['12003', 'محمد عبد الله أحمد', 'مدير الشعبة', 'الأولى', 950000, 'ماجستير', 'الأولى', '07701234567', '<EMAIL>', 'بغداد']
    ]
    
    columns = ['الرقم الوظيفي', 'الاسم الكامل', 'المنصب', 'الدرجة الوظيفية', 'الراتب الأساسي', 'الشهادة', 'المرحلة', 'رقم الهاتف', 'البريد الإلكتروني', 'العنوان']
    
    df = pd.DataFrame(data, columns=columns)
    df.to_excel('sample_employees.xlsx', index=False, sheet_name='الموظفون')
    
    print("✅ تم إنشاء ملف sample_employees.xlsx بنجاح!")
    print(f"📊 يحتوي الملف على {len(data)} موظف")
    
except ImportError:
    print("❌ pandas غير مثبت. سيتم إنشاء ملف CSV بدلاً من Excel")
    
    # إنشاء ملف CSV كبديل
    import csv
    
    data = [
        ['الرقم الوظيفي', 'الاسم الكامل', 'المنصب', 'الدرجة الوظيفية', 'الراتب الأساسي', 'الشهادة', 'المرحلة', 'رقم الهاتف', 'البريد الإلكتروني', 'العنوان'],
        ['12001', 'أحمد محمد علي', 'محاسب أول', 'الثالثة', '750000', 'بكالوريوس', 'الأولى', '07901234567', '<EMAIL>', 'بغداد'],
        ['12002', 'فاطمة حسن محمود', 'كاتبة', 'الخامسة', '580000', 'إعدادية', 'الثانية', '07801234567', '<EMAIL>', 'بغداد'],
        ['12003', 'محمد عبد الله أحمد', 'مدير الشعبة', 'الأولى', '950000', 'ماجستير', 'الأولى', '07701234567', '<EMAIL>', 'بغداد']
    ]
    
    with open('sample_employees.csv', 'w', newline='', encoding='utf-8-sig') as file:
        writer = csv.writer(file)
        writer.writerows(data)
    
    print("✅ تم إنشاء ملف sample_employees.csv بنجاح!")
    print(f"📊 يحتوي الملف على {len(data)-1} موظف")

except Exception as e:
    print(f"❌ خطأ في إنشاء الملف: {e}")
