&lt;UserControl x:Class="EmployeesView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="800"
             FlowDirection="RightToLeft"
             Background="{DynamicResource MaterialDesignBackground}">

    &lt;Grid Margin="20">
        &lt;Grid.RowDefinitions>
            &lt;RowDefinition Height="Auto"/>
            &lt;RowDefinition Height="Auto"/>
            &lt;RowDefinition Height="*"/>
        &lt;/Grid.RowDefinitions>

        &lt;!-- شريط الأدوات -->
        &lt;StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,20">
            &lt;Button Style="{StaticResource MaterialDesignFlatButton}"
                    Click="AddEmployee_Click">
                &lt;StackPanel Orientation="Horizontal">
                    &lt;materialDesign:PackIcon Kind="Plus" Width="24" Height="24"/>
                    &lt;TextBlock Text="إضافة موظف" Margin="8,0,0,0"/>
                &lt;/StackPanel>
            &lt;/Button>
            
            &lt;Button Style="{StaticResource MaterialDesignFlatButton}"
                    Margin="10,0"
                    Click="RefreshEmployees_Click">
                &lt;StackPanel Orientation="Horizontal">
                    &lt;materialDesign:PackIcon Kind="Refresh" Width="24" Height="24"/>
                    &lt;TextBlock Text="تحديث" Margin="8,0,0,0"/>
                &lt;/StackPanel>
            &lt;/Button>
        &lt;/StackPanel>

        &lt;!-- شريط البحث -->
        &lt;Grid Grid.Row="1" Margin="0,0,0,20">
            &lt;Grid.ColumnDefinitions>
                &lt;ColumnDefinition Width="*"/>
                &lt;ColumnDefinition Width="Auto"/>
            &lt;/Grid.ColumnDefinitions>

            &lt;TextBox x:Name="SearchBox"
                     Grid.Column="0"
                     Style="{StaticResource MaterialDesignOutlinedTextBox}"
                     materialDesign:HintAssist.Hint="بحث في الموظفين..."
                     TextChanged="SearchBox_TextChanged"/>

            &lt;ComboBox x:Name="FilterComboBox"
                      Grid.Column="1"
                      Margin="10,0,0,0"
                      Width="150"
                      Style="{StaticResource MaterialDesignOutlinedComboBox}"
                      materialDesign:HintAssist.Hint="تصفية حسب..."
                      SelectionChanged="FilterComboBox_SelectionChanged">
                &lt;ComboBoxItem Content="الكل"/>
                &lt;ComboBoxItem Content="نشط"/>
                &lt;ComboBoxItem Content="غير نشط"/>
            &lt;/ComboBox>
        &lt;/Grid>

        &lt;!-- جدول الموظفين -->
        &lt;DataGrid x:Name="EmployeesGrid"
                  Grid.Row="2"
                  AutoGenerateColumns="False"
                  IsReadOnly="True"
                  Style="{StaticResource MaterialDesignDataGrid}"
                  materialDesign:DataGridAssist.CellPadding="13 8 8 8"
                  materialDesign:DataGridAssist.ColumnHeaderPadding="8">
            &lt;DataGrid.Columns>
                &lt;DataGridTextColumn Header="الرقم الوظيفي" 
                                    Binding="{Binding EmployeeId}"
                                    Width="100"/>
                &lt;DataGridTextColumn Header="الاسم" 
                                    Binding="{Binding FullName}"
                                    Width="200"/>
                &lt;DataGridTextColumn Header="رقم الهوية" 
                                    Binding="{Binding NationalId}"
                                    Width="120"/>
                &lt;DataGridTextColumn Header="رقم IBAN" 
                                    Binding="{Binding IBANNumber}"
                                    Width="200"/>
                &lt;DataGridTextColumn Header="القسم" 
                                    Binding="{Binding Department}"
                                    Width="150"/>
                &lt;DataGridTextColumn Header="الحالة" 
                                    Binding="{Binding Status}"
                                    Width="100"/>
                &lt;DataGridTemplateColumn Header="الإجراءات" Width="120">
                    &lt;DataGridTemplateColumn.CellTemplate>
                        &lt;DataTemplate>
                            &lt;StackPanel Orientation="Horizontal">
                                &lt;Button Style="{StaticResource MaterialDesignIconButton}"
                                        Click="EditEmployee_Click">
                                    &lt;materialDesign:PackIcon Kind="Pencil"/>
                                &lt;/Button>
                                &lt;Button Style="{StaticResource MaterialDesignIconButton}"
                                        Margin="8,0,0,0"
                                        Click="DeleteEmployee_Click">
                                    &lt;materialDesign:PackIcon Kind="Delete"/>
                                &lt;/Button>
                            &lt;/StackPanel>
                        &lt;/DataTemplate>
                    &lt;/DataGridTemplateColumn.CellTemplate>
                &lt;/DataGridTemplateColumn>
            &lt;/DataGrid.Columns>
        &lt;/DataGrid>
    &lt;/Grid>
&lt;/UserControl>
