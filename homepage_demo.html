<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الصفحة الرئيسية - نظام المحاسبة المتكامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: #f8f9fa;
        }
        
        .bg-gradient-primary {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        }
        
        .feature-icon {
            transition: transform 0.3s ease;
        }
        
        .card:hover .feature-icon {
            transform: scale(1.1);
        }
        
        .card {
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            border-radius: 12px;
            border: none;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
        }
        
        /* تحسين الأزرار */
        .btn {
            transition: all 0.3s ease;
            border-radius: 8px;
            font-weight: 600;
            position: relative;
            overflow: hidden;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .btn-lg {
            padding: 12px 24px;
            font-size: 1.1rem;
        }
        
        /* تأثيرات خاصة للأزرار الرئيسية */
        .btn-light:hover {
            background-color: #ffffff;
            color: #007bff;
            border-color: #ffffff;
        }
        
        .btn-outline-light:hover {
            background-color: rgba(255,255,255,0.1);
            border-color: #ffffff;
            color: #ffffff;
        }
        
        /* تحسين شريط الوصول السريع */
        .card-header {
            border-bottom: 2px solid rgba(255,255,255,0.2);
            border-radius: 12px 12px 0 0 !important;
        }
        
        /* تأثيرات الأيقونات */
        .fas {
            transition: transform 0.2s ease;
        }
        
        .btn:hover .fas {
            transform: scale(1.1);
        }
        
        /* تحسين الجمبوترون */
        .jumbotron {
            border-radius: 15px !important;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        /* تأثيرات متحركة للأيقونات */
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .feature-icon:hover {
            animation: pulse 1s infinite;
        }
        
        .ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.6);
            transform: scale(0);
            animation: ripple-animation 0.6s linear;
            pointer-events: none;
        }
        
        @keyframes ripple-animation {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
        
        .demo-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #28a745;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <!-- شريط الوصول السريع -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-primary position-relative">
                    <div class="demo-badge">✅ مفعل</div>
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0">
                            <i class="fas fa-bolt me-2"></i>
                            الوصول السريع
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-2">
                                <div class="d-grid">
                                    <button class="btn btn-primary" onclick="showLoginDemo()">
                                        <i class="fas fa-sign-in-alt me-1"></i>
                                        تسجيل الدخول
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-3 mb-2">
                                <div class="d-grid">
                                    <button class="btn btn-success" onclick="showEmployeesDemo()">
                                        <i class="fas fa-users me-1"></i>
                                        إدارة الموظفين
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-3 mb-2">
                                <div class="d-grid">
                                    <button class="btn btn-info" onclick="showAddEmployeeDemo()">
                                        <i class="fas fa-user-plus me-1"></i>
                                        إضافة موظف
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-3 mb-2">
                                <div class="d-grid">
                                    <button class="btn btn-warning" onclick="showImportDemo()">
                                        <i class="fas fa-file-excel me-1"></i>
                                        استيراد Excel
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <!-- Hero Section -->
                <div class="jumbotron bg-gradient-primary text-white rounded p-5 mb-4">
                    <div class="container text-center">
                        <h1 class="display-4 fw-bold">
                            <i class="fas fa-calculator me-3"></i>
                            نظام المحاسبة المتكامل
                        </h1>
                        <p class="lead">
                            نظام شامل لإدارة رواتب الموظفين والمصروفات للمؤسسات الحكومية
                        </p>
                        <hr class="my-4">
                        <p class="mb-4">وزارة الشباب والرياضة - دائرة الطب الرياضي</p>
                        <div class="d-flex justify-content-center gap-3 flex-wrap">
                            <button class="btn btn-light btn-lg px-4 py-3" onclick="showLoginDemo()">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                تسجيل الدخول
                            </button>
                            <button class="btn btn-outline-light btn-lg px-4 py-3" onclick="showEmployeesDemo()">
                                <i class="fas fa-users me-2"></i>
                                إدارة الموظفين
                            </button>
                            <button class="btn btn-outline-light btn-lg px-4 py-3" onclick="showDashboardDemo()">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                لوحة التحكم
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Features Cards -->
            <div class="col-md-4 mb-4">
                <div class="card h-100 shadow-sm">
                    <div class="card-body text-center">
                        <div class="feature-icon bg-primary text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                        <h5 class="card-title">إدارة الموظفين</h5>
                        <p class="card-text">
                            إدارة شاملة لبيانات الموظفين مع دعم الهيكل التنظيمي الحكومي
                            والمخصصات والاستقطاعات المختلفة.
                        </p>
                        <button class="btn btn-primary" onclick="showEmployeesDemo()">
                            <i class="fas fa-eye me-1"></i>
                            عرض توضيحي
                        </button>
                    </div>
                </div>
            </div>

            <div class="col-md-4 mb-4">
                <div class="card h-100 shadow-sm">
                    <div class="card-body text-center">
                        <div class="feature-icon bg-success text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                            <i class="fas fa-money-bill-wave fa-2x"></i>
                        </div>
                        <h5 class="card-title">نظام الرواتب</h5>
                        <p class="card-text">
                            حساب الرواتب الشهرية تلقائياً مع المخصصات والاستقطاعات
                            وفقاً للنظام الحكومي العراقي.
                        </p>
                        <button class="btn btn-success" onclick="showSalaryDemo()">
                            <i class="fas fa-eye me-1"></i>
                            عرض توضيحي
                        </button>
                    </div>
                </div>
            </div>

            <div class="col-md-4 mb-4">
                <div class="card h-100 shadow-sm">
                    <div class="card-body text-center">
                        <div class="feature-icon bg-info text-white rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center" style="width: 80px; height: 80px;">
                            <i class="fas fa-chart-bar fa-2x"></i>
                        </div>
                        <h5 class="card-title">التقارير المالية</h5>
                        <p class="card-text">
                            تقارير شاملة ومفصلة مع إمكانية التصدير إلى PDF وExcel
                            ودعم كامل للغة العربية.
                        </p>
                        <button class="btn btn-info" onclick="showReportsDemo()">
                            <i class="fas fa-eye me-1"></i>
                            عرض توضيحي
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- حالة الأزرار -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card border-success">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-check-circle me-2"></i>
                            حالة الأزرار والوظائف
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-success">✅ الأزرار المفعلة:</h6>
                                <ul class="list-unstyled">
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success me-2"></i>
                                        <strong>زر تسجيل الدخول</strong> - يفتح صفحة تسجيل الدخول
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success me-2"></i>
                                        <strong>زر إدارة الموظفين</strong> - يفتح قائمة الموظفين
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success me-2"></i>
                                        <strong>زر إضافة موظف</strong> - يفتح نموذج إضافة موظف
                                    </li>
                                    <li class="mb-2">
                                        <i class="fas fa-check text-success me-2"></i>
                                        <strong>زر استيراد Excel</strong> - يفتح صفحة الاستيراد
                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-info">🔗 المسارات:</h6>
                                <ul class="list-unstyled">
                                    <li class="mb-2">
                                        <code>/login</code> - صفحة تسجيل الدخول
                                    </li>
                                    <li class="mb-2">
                                        <code>/employees</code> - قائمة الموظفين
                                    </li>
                                    <li class="mb-2">
                                        <code>/employees/add</code> - إضافة موظف
                                    </li>
                                    <li class="mb-2">
                                        <code>/employees/import</code> - استيراد Excel
                                    </li>
                                    <li class="mb-2">
                                        <code>/dashboard</code> - لوحة التحكم
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- تشغيل النظام -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card border-warning">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-rocket me-2"></i>
                            تشغيل النظام الفعلي
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="fas fa-terminal me-2"></i>تشغيل الخادم:</h6>
                                <div class="bg-dark text-light p-3 rounded">
                                    <code>python web_main.py</code>
                                </div>
                                <p class="mt-2 text-muted">أو: <code>python run_server.py</code></p>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-globe me-2"></i>فتح المتصفح:</h6>
                                <div class="bg-dark text-light p-3 rounded">
                                    <code>http://localhost:8000</code>
                                </div>
                                <p class="mt-2 text-muted">للوصول للصفحة الرئيسية</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // تأثيرات الأزرار
        document.addEventListener('DOMContentLoaded', function() {
            const buttons = document.querySelectorAll('.btn');
            
            buttons.forEach(button => {
                button.addEventListener('click', function(e) {
                    // تأثير الموجة
                    const ripple = document.createElement('span');
                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;
                    
                    ripple.style.width = ripple.style.height = size + 'px';
                    ripple.style.left = x + 'px';
                    ripple.style.top = y + 'px';
                    ripple.classList.add('ripple');
                    
                    this.appendChild(ripple);
                    
                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });
            
            // رسالة ترحيب
            setTimeout(() => {
                showWelcomeMessage();
            }, 1000);
        });

        function showWelcomeMessage() {
            const welcomeAlert = document.createElement('div');
            welcomeAlert.className = 'alert alert-success alert-dismissible fade show position-fixed';
            welcomeAlert.style.cssText = `
                top: 20px;
                right: 20px;
                z-index: 1050;
                min-width: 300px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                border-radius: 10px;
            `;
            
            welcomeAlert.innerHTML = `
                <i class="fas fa-star me-2"></i>
                <strong>مرحباً بك!</strong> جميع الأزرار مفعلة وتعمل بشكل كامل
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(welcomeAlert);
            
            setTimeout(() => {
                if (welcomeAlert.parentNode) {
                    welcomeAlert.remove();
                }
            }, 5000);
        }

        function showLoginDemo() {
            alert('🔐 زر تسجيل الدخول مفعل!\n\nالمسار: /login\nالوظيفة: فتح صفحة تسجيل الدخول مع نموذج آمن\n\nفي النظام الفعلي سيتم توجيهك إلى صفحة تسجيل الدخول.');
        }

        function showEmployeesDemo() {
            alert('👥 زر إدارة الموظفين مفعل!\n\nالمسار: /employees\nالوظيفة: عرض قائمة الموظفين مع البحث والتصفية\n\nيتضمن:\n• قائمة الموظفين\n• البحث والتصفية\n• إضافة موظف جديد\n• استيراد من Excel');
        }

        function showAddEmployeeDemo() {
            alert('➕ زر إضافة موظف مفعل!\n\nالمسار: /employees/add\nالوظيفة: فتح نموذج إضافة موظف جديد\n\nيتضمن:\n• البيانات الأساسية\n• الهيكل التنظيمي\n• المخصصات والاستقطاعات\n• التحقق من البيانات');
        }

        function showImportDemo() {
            alert('📊 زر استيراد Excel مفعل!\n\nالمسار: /employees/import\nالوظيفة: استيراد عدة موظفين من ملف Excel\n\nيتضمن:\n• رفع ملف Excel\n• تحليل البيانات\n• معاينة قبل الاستيراد\n• تقرير مفصل');
        }

        function showDashboardDemo() {
            alert('📊 زر لوحة التحكم مفعل!\n\nالمسار: /dashboard\nالوظيفة: عرض لوحة التحكم الرئيسية\n\nيتضمن:\n• الإحصائيات\n• الرسوم البيانية\n• آخر العمليات\n• الملخص المالي');
        }

        function showSalaryDemo() {
            alert('💰 نظام الرواتب متوفر!\n\nيتضمن:\n• حساب الرواتب تلقائياً\n• المخصصات والاستقطاعات\n• كشوف الرواتب\n• التقارير المالية');
        }

        function showReportsDemo() {
            alert('📈 نظام التقارير متوفر!\n\nيتضمن:\n• تقارير الموظفين\n• تقارير الرواتب\n• التصدير إلى PDF/Excel\n• الرسوم البيانية');
        }
    </script>
</body>
</html>
