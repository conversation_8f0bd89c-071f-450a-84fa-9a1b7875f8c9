# نظام المحاسبة المتكامل
## Integrated Accounting System

نظام شامل لإدارة رواتب الموظفين والمصروفات للمؤسسات الحكومية باستخدام Python مع دعم كامل للغة العربية.

---

## 🌟 المميزات الرئيسية

### 💼 إدارة الموظفين
- إدارة شاملة لبيانات الموظفين
- دعم الهيكل التنظيمي الحكومي
- إدارة المخصصات والاستقطاعات
- استيراد وتصدير البيانات من/إلى Excel

### 💰 نظام الرواتب
- حساب الرواتب الشهرية تلقائياً
- دعم المخصصات المتنوعة (منصب، زوجية، أولاد، إلخ)
- الاستقطاعات الحكومية (تقاعد 10%, مساهمة حكومية 15%)
- كشوفات رواتب مفصلة

### 🏦 إدارة الحسابات
- إدارة الحسابات البنكية
- دليل محاسبي متكامل
- نظام الفترات المالية
- تتبع المعاملات المالية

### 📊 التقارير والطباعة
- تقارير شاملة بصيغة PDF وExcel
- دعم كامل للغة العربية في التقارير
- تصفية وفرز متقدم
- طباعة احترافية

### 🔒 الأمان والصلاحيات
- نظام مستخدمين متقدم
- صلاحيات متدرجة (مدير، مستخدم، إلخ)
- تشفير كلمات المرور
- تسجيل العمليات

---

## 🛠️ المتطلبات التقنية

### متطلبات النظام
- **نظام التشغيل:** Windows 7 أو أحدث
- **Python:** 3.10 أو أحدث
- **الذاكرة:** 4 GB RAM (الحد الأدنى)
- **مساحة القرص:** 500 MB

### قواعد البيانات المدعومة
- SQLite (افتراضي)
- SQL Server (اختياري)

---

## 📦 التثبيت والإعداد

### 1. تحميل المشروع
```bash
git clone https://github.com/your-repo/accounting-system.git
cd accounting-system
```

### 2. إنشاء بيئة افتراضية
```bash
python -m venv venv
venv\Scripts\activate  # Windows
```

### 3. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 4. إعداد قاعدة البيانات
```bash
python config/settings.py
```

---

## 🚀 تشغيل النظام

### تطبيق سطح المكتب
```bash
python main.py
```

### تطبيق الويب
```bash
python web_main.py
```
ثم افتح المتصفح على: `http://localhost:8000`

---

## 👤 بيانات الدخول الافتراضية

- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

> ⚠️ **تنبيه:** يرجى تغيير كلمة المرور الافتراضية فور تسجيل الدخول الأول.

---

## 🏗️ هيكل المشروع

```
accounting-system/
├── config/                 # إعدادات النظام
│   └── settings.py
├── database/              # إدارة قاعدة البيانات
│   └── database_manager.py
├── models/                # نماذج البيانات
│   ├── user.py
│   ├── employee.py
│   ├── salary.py
│   ├── allowance.py
│   ├── deduction.py
│   └── bank_account.py
├── desktop_app/           # تطبيق سطح المكتب
│   ├── login_window.py
│   └── main_window.py
├── web_app/              # تطبيق الويب
│   ├── templates/
│   └── static/
├── utils/                # الأدوات المساعدة
│   └── arabic_support.py
├── reports/              # مولد التقارير
├── main.py              # تطبيق سطح المكتب
├── web_main.py          # تطبيق الويب
└── requirements.txt     # المتطلبات
```

---

## 🏛️ معلومات المؤسسة

- **الوزارة:** وزارة الشباب والرياضة
- **الدائرة:** دائرة الطب الرياضي
- **القسم:** قسم إداري
- **الشعبة:** شعبة الحسابات

### الحسابات البنكية
- **حساب تشغيلي:** ***************
- **حساب رواتب:** ***************
- **البنك:** مصرف الرافدين - فرع دور الضباط (69)

---

## 📋 دليل الاستخدام

### إضافة موظف جديد
1. انتقل إلى قائمة "الموظفون"
2. اختر "إضافة موظف جديد"
3. املأ البيانات المطلوبة
4. احفظ البيانات

### معالجة الرواتب الشهرية
1. انتقل إلى قائمة "الرواتب"
2. اختر "معالجة الرواتب"
3. حدد الشهر والسنة
4. اضغط "معالجة"

### إنشاء تقرير
1. انتقل إلى قائمة "التقارير"
2. اختر نوع التقرير المطلوب
3. حدد المعايير والفترة
4. اختر صيغة التصدير (PDF/Excel)

---

## 🔧 الإعدادات المتقدمة

### تغيير قاعدة البيانات إلى SQL Server
1. افتح ملف `config/settings.py`
2. غير `DATABASE_CONFIG['type']` إلى `'sqlserver'`
3. حدث إعدادات الاتصال في `sqlserver_config`

### تخصيص المخصصات والاستقطاعات
1. افتح ملف `config/settings.py`
2. عدل قائمة `SALARY_CONFIG`
3. أعد تشغيل النظام

---

## 🛡️ النسخ الاحتياطي

### نسخ احتياطي تلقائي
- يتم إنشاء نسخة احتياطية يومياً في الساعة 2:00 صباحاً
- تحفظ النسخ في مجلد `backups/`
- يتم الاحتفاظ بآخر 30 نسخة

### نسخ احتياطي يدوي
1. انتقل إلى "الإعدادات"
2. اختر "النسخ الاحتياطي"
3. اضغط "إنشاء نسخة احتياطية"

---

## 🐛 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### خطأ في الاتصال بقاعدة البيانات
```
الحل: تأكد من وجود ملف قاعدة البيانات في مجلد data/
```

#### مشكلة في عرض النصوص العربية
```
الحل: تأكد من تثبيت خط Cairo أو استخدم خط النظام
```

#### بطء في التطبيق
```
الحل: تأكد من وجود فهارس في قاعدة البيانات وتنظيف البيانات القديمة
```

---

## 📞 الدعم والمساعدة

### معلومات الاتصال
- **البريد الإلكتروني:** <EMAIL>
- **الهاتف:** +964-1-XXXXXXX
- **العنوان:** بغداد - العراق

### الإبلاغ عن الأخطاء
يرجى إرسال تقرير مفصل عن الخطأ مع:
- وصف المشكلة
- خطوات إعادة إنتاج الخطأ
- لقطة شاشة (إن أمكن)

---

## 📄 الترخيص

هذا النظام مطور خصيصاً لوزارة الشباب والرياضة - دائرة الطب الرياضي.
جميع الحقوق محفوظة © 2024

---

## 🔄 التحديثات

### الإصدار 1.0.0 (2024)
- إطلاق النسخة الأولى
- دعم كامل للغة العربية
- نظام إدارة الموظفين والرواتب
- تطبيق سطح المكتب والويب

### التحديثات المستقبلية
- [ ] تطبيق الهاتف المحمول
- [ ] تكامل مع أنظمة حكومية أخرى
- [ ] تقارير متقدمة
- [ ] لوحة تحكم تفاعلية

---

**تم تطوير هذا النظام بعناية لخدمة المؤسسات الحكومية العراقية**
