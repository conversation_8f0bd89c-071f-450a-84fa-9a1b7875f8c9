<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>استيراد موظفين من Excel - نظام المحاسبة المتكامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: #f8f9fa;
        }
        
        .navbar {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .card {
            border: none;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
            border-radius: 12px;
        }
        
        .upload-area {
            border: 3px dashed #28a745;
            border-radius: 12px;
            padding: 40px;
            text-align: center;
            background: #f8fff8;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .upload-area:hover {
            border-color: #20c997;
            background: #e8f5e8;
        }
        
        .upload-area.dragover {
            border-color: #007bff;
            background: #e3f2fd;
        }
        
        .step {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-left: 15px;
        }
        
        .step-content {
            flex: 1;
        }
        
        .progress-step {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .progress-step.active {
            background: #e3f2fd;
            border: 2px solid #007bff;
        }
        
        .progress-step.completed {
            background: #e8f5e8;
            border: 2px solid #28a745;
        }
        
        .file-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .preview-table {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .btn-download {
            background: linear-gradient(45deg, #17a2b8, #138496);
            border: none;
            color: white;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="simple_homepage.html">
                <i class="fas fa-calculator me-2"></i>
                نظام المحاسبة المتكامل
            </a>
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="simple_homepage.html">
                    <i class="fas fa-home me-1"></i>الرئيسية
                </a>
                <a class="nav-link" href="employees_page.html">
                    <i class="fas fa-users me-1"></i>الموظفون
                </a>
                <a class="nav-link active" href="import_excel_page.html">
                    <i class="fas fa-file-excel me-1"></i>استيراد Excel
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- عنوان الصفحة -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2 class="text-success">
                        <i class="fas fa-file-excel me-2"></i>
                        استيراد موظفين من Excel
                    </h2>
                    <a href="employees_page.html" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-1"></i>العودة للقائمة
                    </a>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- خطوات العملية -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-list-ol me-2"></i>
                            خطوات الاستيراد
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="progress-step active" id="step1">
                            <div class="step">1</div>
                            <div class="step-content">
                                <h6>تحميل القالب</h6>
                                <small class="text-muted">حمل قالب Excel الجاهز</small>
                            </div>
                        </div>
                        
                        <div class="progress-step" id="step2">
                            <div class="step">2</div>
                            <div class="step-content">
                                <h6>ملء البيانات</h6>
                                <small class="text-muted">أدخل بيانات الموظفين</small>
                            </div>
                        </div>
                        
                        <div class="progress-step" id="step3">
                            <div class="step">3</div>
                            <div class="step-content">
                                <h6>رفع الملف</h6>
                                <small class="text-muted">ارفع الملف المكتمل</small>
                            </div>
                        </div>
                        
                        <div class="progress-step" id="step4">
                            <div class="step">4</div>
                            <div class="step-content">
                                <h6>مراجعة البيانات</h6>
                                <small class="text-muted">تأكد من صحة البيانات</small>
                            </div>
                        </div>
                        
                        <div class="progress-step" id="step5">
                            <div class="step">5</div>
                            <div class="step-content">
                                <h6>تأكيد الاستيراد</h6>
                                <small class="text-muted">حفظ البيانات في النظام</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- المحتوى الرئيسي -->
            <div class="col-md-8">
                <!-- الخطوة 1: تحميل القالب -->
                <div class="card mb-4" id="templateSection">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-download me-2"></i>
                            الخطوة 1: تحميل قالب Excel
                        </h5>
                    </div>
                    <div class="card-body">
                        <p class="mb-3">
                            <i class="fas fa-info-circle text-info me-2"></i>
                            حمل القالب الجاهز وامأله ببيانات الموظفين
                        </p>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-start">
                            <button class="btn btn-download btn-lg me-md-2" onclick="downloadTemplate()">
                                <i class="fas fa-file-excel me-2"></i>
                                تحميل قالب Excel
                            </button>
                            <button class="btn btn-outline-info btn-lg" onclick="showSampleData()">
                                <i class="fas fa-eye me-2"></i>
                                عرض البيانات النموذجية
                            </button>
                        </div>
                        
                        <div class="file-info mt-3">
                            <h6><i class="fas fa-exclamation-triangle text-warning me-2"></i>تعليمات مهمة:</h6>
                            <ul class="mb-0">
                                <li>لا تغير أسماء الأعمدة في الصف الأول</li>
                                <li>تأكد من صحة تنسيق التواريخ (YYYY-MM-DD)</li>
                                <li>الحقول المطلوبة: الرقم الوظيفي، الاسم، المنصب، القسم، الدرجة</li>
                                <li>احفظ الملف بصيغة .xlsx أو .xls</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- الخطوة 2: رفع الملف -->
                <div class="card mb-4" id="uploadSection">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-upload me-2"></i>
                            الخطوة 2: رفع ملف Excel
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="upload-area" id="uploadArea" onclick="document.getElementById('excelFile').click()">
                            <i class="fas fa-cloud-upload-alt fa-3x text-success mb-3"></i>
                            <h5>اسحب وأفلت ملف Excel هنا</h5>
                            <p class="text-muted">أو انقر للاختيار من الجهاز</p>
                            <small class="text-muted">الحد الأقصى: 10MB | الصيغ المدعومة: .xlsx, .xls</small>
                        </div>
                        
                        <input type="file" id="excelFile" accept=".xlsx,.xls" style="display: none;" onchange="handleFileUpload(this)">
                        
                        <div id="fileInfo" style="display: none;" class="file-info mt-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 id="fileName" class="mb-1"></h6>
                                    <small id="fileSize" class="text-muted"></small>
                                </div>
                                <button class="btn btn-outline-danger btn-sm" onclick="removeFile()">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                            <div class="progress mt-2">
                                <div class="progress-bar bg-success" id="uploadProgress" style="width: 0%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الخطوة 3: معاينة البيانات -->
                <div class="card mb-4" id="previewSection" style="display: none;">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-eye me-2"></i>
                            الخطوة 3: معاينة البيانات المستوردة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <span class="badge bg-info fs-6" id="totalRecords">0 سجل</span>
                                <span class="badge bg-success fs-6 ms-2" id="validRecords">0 صحيح</span>
                                <span class="badge bg-danger fs-6 ms-2" id="errorRecords">0 خطأ</span>
                            </div>
                            <div>
                                <button class="btn btn-outline-primary btn-sm me-2" onclick="validateData()">
                                    <i class="fas fa-check-circle me-1"></i>التحقق من البيانات
                                </button>
                                <button class="btn btn-outline-secondary btn-sm" onclick="exportErrors()">
                                    <i class="fas fa-file-export me-1"></i>تصدير الأخطاء
                                </button>
                            </div>
                        </div>
                        
                        <div class="preview-table">
                            <table class="table table-striped table-hover" id="previewTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>الحالة</th>
                                        <th>الرقم الوظيفي</th>
                                        <th>الاسم الكامل</th>
                                        <th>المنصب</th>
                                        <th>القسم</th>
                                        <th>الدرجة</th>
                                        <th>الراتب الأساسي</th>
                                    </tr>
                                </thead>
                                <tbody id="previewTableBody">
                                    <!-- سيتم ملؤها بـ JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- الخطوة 4: تأكيد الاستيراد -->
                <div class="card" id="confirmSection" style="display: none;">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-check-circle me-2"></i>
                            الخطوة 4: تأكيد الاستيراد
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>ملخص العملية:</h6>
                            <ul class="mb-0">
                                <li>إجمالي السجلات: <span id="summaryTotal">0</span></li>
                                <li>السجلات الصحيحة: <span id="summaryValid">0</span></li>
                                <li>السجلات التي بها أخطاء: <span id="summaryErrors">0</span></li>
                            </ul>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                            <button class="btn btn-success btn-lg me-md-2" onclick="confirmImport()">
                                <i class="fas fa-check me-2"></i>
                                تأكيد الاستيراد
                            </button>
                            <button class="btn btn-outline-secondary btn-lg" onclick="resetImport()">
                                <i class="fas fa-undo me-2"></i>
                                إعادة البدء
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let uploadedData = [];
        let currentStep = 1;

        // تحميل القالب
        function downloadTemplate() {
            // محاكاة تحميل قالب Excel
            const templateData = [
                ['الرقم الوظيفي', 'الاسم الكامل', 'المنصب', 'القسم', 'الدرجة الوظيفية', 'تاريخ الميلاد', 'الجنس', 'رقم الهاتف', 'تاريخ التعيين', 'الحالة'],
                ['12006', 'زينب أحمد محمد الكريم', 'محاسبة', 'المحاسبة', 'الرابعة', '1985-03-15', 'أنثى', '07701234567', '2010-01-01', 'نشط'],
                ['12007', 'حسام علي حسين الطيب', 'مساعد إداري', 'الشؤون الإدارية', 'السادسة', '1990-07-22', 'ذكر', '07801234567', '2015-06-01', 'نشط'],
                ['12008', 'نور فاطمة حسن الزهراء', 'طبيبة مقيمة', 'الشؤون الطبية', 'الثالثة', '1988-11-10', 'أنثى', '07901234567', '2012-09-01', 'نشط']
            ];
            
            alert('📥 تم تحميل قالب Excel بنجاح!\n\n' +
                  'القالب يحتوي على:\n' +
                  '• أسماء الأعمدة المطلوبة\n' +
                  '• 3 أمثلة لبيانات الموظفين\n' +
                  '• التنسيق الصحيح للبيانات\n\n' +
                  'املأ القالب وارفعه في الخطوة التالية.');
            
            updateStep(2);
        }

        // عرض البيانات النموذجية
        function showSampleData() {
            alert('📋 البيانات النموذجية:\n\n' +
                  'الأعمدة المطلوبة:\n' +
                  '1. الرقم الوظيفي (مطلوب)\n' +
                  '2. الاسم الكامل (مطلوب)\n' +
                  '3. المنصب (مطلوب)\n' +
                  '4. القسم (مطلوب)\n' +
                  '5. الدرجة الوظيفية (مطلوب)\n' +
                  '6. تاريخ الميلاد\n' +
                  '7. الجنس\n' +
                  '8. رقم الهاتف\n' +
                  '9. تاريخ التعيين\n' +
                  '10. الحالة الوظيفية');
        }

        // التعامل مع رفع الملف
        function handleFileUpload(input) {
            const file = input.files[0];
            if (!file) return;

            // التحقق من نوع الملف
            if (!file.name.match(/\.(xlsx|xls)$/)) {
                alert('❌ يرجى اختيار ملف Excel صحيح (.xlsx أو .xls)');
                return;
            }

            // التحقق من حجم الملف (10MB)
            if (file.size > 10 * 1024 * 1024) {
                alert('❌ حجم الملف كبير جداً. الحد الأقصى 10MB');
                return;
            }

            // عرض معلومات الملف
            document.getElementById('fileName').textContent = file.name;
            document.getElementById('fileSize').textContent = `${(file.size / 1024 / 1024).toFixed(2)} MB`;
            document.getElementById('fileInfo').style.display = 'block';

            // محاكاة رفع الملف
            simulateUpload();
        }

        // محاكاة رفع الملف
        function simulateUpload() {
            const progressBar = document.getElementById('uploadProgress');
            let progress = 0;
            
            const interval = setInterval(() => {
                progress += 10;
                progressBar.style.width = progress + '%';
                
                if (progress >= 100) {
                    clearInterval(interval);
                    setTimeout(() => {
                        processExcelData();
                    }, 500);
                }
            }, 200);
        }

        // معالجة بيانات Excel
        function processExcelData() {
            // بيانات تجريبية
            uploadedData = [
                {id: '12006', name: 'زينب أحمد محمد الكريم', position: 'محاسبة', department: 'المحاسبة', grade: 'الرابعة', salary: 650000, status: 'صحيح'},
                {id: '12007', name: 'حسام علي حسين الطيب', position: 'مساعد إداري', department: 'الشؤون الإدارية', grade: 'السادسة', salary: 520000, status: 'صحيح'},
                {id: '12008', name: 'نور فاطمة حسن الزهراء', position: 'طبيبة مقيمة', department: 'الشؤون الطبية', grade: 'الثالثة', salary: 750000, status: 'صحيح'},
                {id: '', name: 'موظف بدون رقم', position: 'كاتب', department: 'الإدارة', grade: 'الخامسة', salary: 580000, status: 'خطأ'},
                {id: '12010', name: '', position: 'محاسب', department: 'المحاسبة', grade: 'الثالثة', salary: 750000, status: 'خطأ'}
            ];

            displayPreview();
            updateStep(3);
        }

        // عرض معاينة البيانات
        function displayPreview() {
            const tbody = document.getElementById('previewTableBody');
            tbody.innerHTML = '';

            let validCount = 0;
            let errorCount = 0;

            uploadedData.forEach(row => {
                const tr = document.createElement('tr');
                const statusClass = row.status === 'صحيح' ? 'success' : 'danger';
                const statusIcon = row.status === 'صحيح' ? 'check-circle' : 'exclamation-triangle';
                
                if (row.status === 'صحيح') validCount++;
                else errorCount++;

                tr.innerHTML = `
                    <td><span class="badge bg-${statusClass}"><i class="fas fa-${statusIcon} me-1"></i>${row.status}</span></td>
                    <td>${row.id}</td>
                    <td>${row.name}</td>
                    <td>${row.position}</td>
                    <td>${row.department}</td>
                    <td>${row.grade}</td>
                    <td>${row.salary.toLocaleString()} د.ع</td>
                `;
                tbody.appendChild(tr);
            });

            // تحديث الإحصائيات
            document.getElementById('totalRecords').textContent = `${uploadedData.length} سجل`;
            document.getElementById('validRecords').textContent = `${validCount} صحيح`;
            document.getElementById('errorRecords').textContent = `${errorCount} خطأ`;

            document.getElementById('previewSection').style.display = 'block';
            
            if (errorCount === 0) {
                document.getElementById('confirmSection').style.display = 'block';
                updateStep(4);
            }
        }

        // التحقق من البيانات
        function validateData() {
            alert('✅ تم التحقق من البيانات!\n\n' +
                  'النتائج:\n' +
                  '• السجلات الصحيحة: 3\n' +
                  '• السجلات التي بها أخطاء: 2\n\n' +
                  'الأخطاء الموجودة:\n' +
                  '• السجل 4: الرقم الوظيفي مفقود\n' +
                  '• السجل 5: الاسم مفقود\n\n' +
                  'يرجى تصحيح الأخطاء قبل المتابعة.');
        }

        // تصدير الأخطاء
        function exportErrors() {
            alert('📤 تم تصدير ملف الأخطاء!\n\n' +
                  'الملف يحتوي على:\n' +
                  '• السجلات التي بها أخطاء\n' +
                  '• وصف تفصيلي لكل خطأ\n' +
                  '• إرشادات للتصحيح\n\n' +
                  'صحح الأخطاء وأعد رفع الملف.');
        }

        // تأكيد الاستيراد
        function confirmImport() {
            const validRecords = uploadedData.filter(row => row.status === 'صحيح');
            
            alert(`🎉 تم استيراد البيانات بنجاح!\n\n` +
                  `تم إضافة ${validRecords.length} موظف جديد:\n` +
                  validRecords.map(emp => `• ${emp.name} (${emp.id})`).join('\n') +
                  `\n\nسيتم توجيهك إلى قائمة الموظفين...`);
            
            setTimeout(() => {
                window.location.href = 'employees_page.html';
            }, 3000);
        }

        // إعادة البدء
        function resetImport() {
            uploadedData = [];
            document.getElementById('excelFile').value = '';
            document.getElementById('fileInfo').style.display = 'none';
            document.getElementById('previewSection').style.display = 'none';
            document.getElementById('confirmSection').style.display = 'none';
            updateStep(1);
        }

        // تحديث الخطوة الحالية
        function updateStep(step) {
            // إزالة الحالة النشطة من جميع الخطوات
            for (let i = 1; i <= 5; i++) {
                const stepElement = document.getElementById(`step${i}`);
                stepElement.classList.remove('active', 'completed');
                
                if (i < step) {
                    stepElement.classList.add('completed');
                } else if (i === step) {
                    stepElement.classList.add('active');
                }
            }
            currentStep = step;
        }

        // إعداد السحب والإفلات
        const uploadArea = document.getElementById('uploadArea');
        
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                document.getElementById('excelFile').files = files;
                handleFileUpload(document.getElementById('excelFile'));
            }
        });

        // إزالة الملف
        function removeFile() {
            document.getElementById('excelFile').value = '';
            document.getElementById('fileInfo').style.display = 'none';
            document.getElementById('previewSection').style.display = 'none';
            document.getElementById('confirmSection').style.display = 'none';
        }

        console.log('✅ صفحة استيراد Excel محملة بنجاح');
        console.log('📁 السحب والإفلات مفعل');
        console.log('🔍 معاينة البيانات متاحة');
    </script>
</body>
</html>
