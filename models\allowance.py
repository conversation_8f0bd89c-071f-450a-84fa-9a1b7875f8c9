# -*- coding: utf-8 -*-
"""
نموذج المخصصات
Allowance Model
"""

from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from database.database_manager import Base

class Allowance(Base):
    __tablename__ = "allowances"
    
    id = Column(Integer, primary_key=True, index=True)
    employee_id = Column(Integer, ForeignKey("employees.id"), nullable=False)
    allowance_type = Column(String(50), nullable=False)
    amount = Column(Float, nullable=False, default=0.0)
    is_active = Column(Boolean, default=True)
    effective_date = Column(DateTime, server_default=func.now())
    end_date = Column(DateTime)
    notes = Column(String(255))
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    
    # العلاقات
    employee = relationship("Employee", back_populates="allowances")
    
    def __init__(self, employee_id, allowance_type, amount, is_active=True, notes=None):
        self.employee_id = employee_id
        self.allowance_type = allowance_type
        self.amount = amount
        self.is_active = is_active
        self.notes = notes
    
    @classmethod
    def get_allowance_types(cls):
        """الحصول على أنواع المخصصات المتاحة"""
        from config.settings import SALARY_CONFIG
        return SALARY_CONFIG['allowances']
    
    @classmethod
    def get_allowance_type_display(cls, allowance_type):
        """الحصول على اسم المخصص للعرض"""
        type_names = {
            'منصب': 'مخصص منصب',
            'زوجية': 'مخصص زوجية',
            'أولاد': 'مخصص أولاد',
            'هندسية': 'مخصص هندسية',
            'شهادة': 'مخصص شهادة',
            'حرفة': 'مخصص حرفة',
            'خطورة': 'مخصص خطورة',
            'نقل': 'مخصص نقل',
            'جامعية': 'مخصص جامعية'
        }
        return type_names.get(allowance_type, allowance_type)
    
    def get_display_name(self):
        """الحصول على اسم المخصص للعرض"""
        return self.get_allowance_type_display(self.allowance_type)
    
    def deactivate(self):
        """إلغاء تفعيل المخصص"""
        self.is_active = False
        self.end_date = func.now()
    
    def reactivate(self):
        """إعادة تفعيل المخصص"""
        self.is_active = True
        self.end_date = None
    
    def to_dict(self):
        """تحويل إلى قاموس"""
        return {
            'id': self.id,
            'employee_id': self.employee_id,
            'allowance_type': self.allowance_type,
            'allowance_type_display': self.get_display_name(),
            'amount': self.amount,
            'is_active': self.is_active,
            'effective_date': self.effective_date.isoformat() if self.effective_date else None,
            'end_date': self.end_date.isoformat() if self.end_date else None,
            'notes': self.notes,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    def __repr__(self):
        return f"<Allowance(type='{self.allowance_type}', amount={self.amount}, employee_id={self.employee_id})>"
