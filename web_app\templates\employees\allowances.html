{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-plus-circle me-2 text-success"></i>
                مخصصات الموظف
            </h1>
            <a href="/employees/{{ employee.id }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة إلى تفاصيل الموظف
            </a>
        </div>
    </div>
</div>

<!-- معلومات الموظف -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h5 class="mb-1">{{ employee.full_name }}</h5>
                <p class="text-muted mb-0">
                    {{ employee.job_title }} - {{ employee.job_grade }} | 
                    رقم وظيفي: {{ employee.employee_number }}
                </p>
            </div>
            <div class="col-md-4 text-end">
                <span class="h4 text-primary">{{ "{:,.0f}".format(employee.basic_salary) }} د.ع</span>
                <br><small class="text-muted">الراتب الأساسي</small>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- قائمة المخصصات الحالية -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    المخصصات الحالية
                </h5>
            </div>
            <div class="card-body">
                {% if employee.allowances %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>نوع المخصص</th>
                                <th>المبلغ</th>
                                <th>تاريخ البداية</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for allowance in employee.allowances %}
                            <tr>
                                <td>
                                    <strong>{{ allowance.get_display_name() }}</strong>
                                </td>
                                <td>
                                    <span class="text-success fw-bold">{{ "{:,.0f}".format(allowance.amount) }} د.ع</span>
                                </td>
                                <td>
                                    {% if allowance.effective_date %}
                                        {{ allowance.effective_date.strftime('%Y-%m-%d') }}
                                    {% else %}
                                        غير محدد
                                    {% endif %}
                                </td>
                                <td>
                                    {% if allowance.is_active %}
                                        <span class="badge bg-success">نشط</span>
                                    {% else %}
                                        <span class="badge bg-danger">غير نشط</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button type="button" class="btn btn-outline-warning" 
                                                onclick="editAllowance({{ allowance.id }}, '{{ allowance.allowance_type }}', {{ allowance.amount }})">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        {% if allowance.is_active %}
                                        <button type="button" class="btn btn-outline-danger" 
                                                onclick="deactivateAllowance({{ allowance.id }}, '{{ allowance.get_display_name() }}')">
                                            <i class="fas fa-times"></i>
                                        </button>
                                        {% else %}
                                        <button type="button" class="btn btn-outline-success" 
                                                onclick="activateAllowance({{ allowance.id }})">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                        <tfoot class="table-light">
                            <tr>
                                <th>إجمالي المخصصات النشطة:</th>
                                <th class="text-success">{{ "{:,.0f}".format(employee.calculate_total_allowances()) }} د.ع</th>
                                <th colspan="3"></th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-plus-circle fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد مخصصات مضافة</h5>
                    <p class="text-muted">ابدأ بإضافة مخصص جديد للموظف</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- إضافة مخصص جديد -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-plus me-2"></i>
                    إضافة مخصص جديد
                </h5>
            </div>
            <div class="card-body">
                <form method="post" action="/employees/{{ employee.id }}/allowances/add" id="addAllowanceForm">
                    <div class="mb-3">
                        <label for="allowance_type" class="form-label">نوع المخصص</label>
                        <select class="form-select" id="allowance_type" name="allowance_type" required>
                            <option value="">اختر نوع المخصص</option>
                            <option value="منصب">مخصص منصب</option>
                            <option value="زوجية">مخصص زوجية</option>
                            <option value="أولاد">مخصص أولاد</option>
                            <option value="هندسية">مخصص هندسية</option>
                            <option value="شهادة">مخصص شهادة</option>
                            <option value="حرفة">مخصص حرفة</option>
                            <option value="خطورة">مخصص خطورة</option>
                            <option value="نقل">مخصص نقل</option>
                            <option value="جامعية">مخصص جامعية</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="amount" class="form-label">المبلغ</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="amount" name="amount" 
                                   required min="0" step="1000" placeholder="0">
                            <span class="input-group-text">د.ع</span>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="effective_date" class="form-label">تاريخ البداية</label>
                        <input type="date" class="form-control" id="effective_date" name="effective_date">
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="2" 
                                  placeholder="ملاحظات إضافية (اختياري)"></textarea>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-plus me-1"></i>
                            إضافة المخصص
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- ملخص الراتب -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-calculator me-2"></i>
                    ملخص الراتب
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between mb-2">
                    <span>الراتب الأساسي:</span>
                    <span>{{ "{:,.0f}".format(employee.basic_salary) }} د.ع</span>
                </div>
                
                <div class="d-flex justify-content-between mb-2">
                    <span>إجمالي المخصصات:</span>
                    <span class="text-success">{{ "{:,.0f}".format(employee.calculate_total_allowances()) }} د.ع</span>
                </div>
                
                <div class="d-flex justify-content-between mb-2">
                    <span>إجمالي الاستقطاعات:</span>
                    <span class="text-danger">{{ "{:,.0f}".format(employee.calculate_total_deductions()) }} د.ع</span>
                </div>
                
                <hr>
                
                <div class="d-flex justify-content-between">
                    <strong>الراتب الصافي:</strong>
                    <strong class="text-primary">{{ "{:,.0f}".format(employee.calculate_net_salary()) }} د.ع</strong>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نافذة تعديل المخصص -->
<div class="modal fade" id="editAllowanceModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل المخصص</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="post" id="editAllowanceForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_allowance_type" class="form-label">نوع المخصص</label>
                        <input type="text" class="form-control" id="edit_allowance_type" readonly>
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_amount" class="form-label">المبلغ</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="edit_amount" name="amount" 
                                   required min="0" step="1000">
                            <span class="input-group-text">د.ع</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-save me-1"></i>
                        حفظ التغييرات
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نافذة تأكيد إلغاء التفعيل -->
<div class="modal fade" id="deactivateModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد إلغاء التفعيل</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من إلغاء تفعيل <strong id="allowanceName"></strong>؟</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form method="post" id="deactivateForm" style="display: inline;">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-times me-1"></i>
                        إلغاء التفعيل
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.card-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.table th {
    border-top: none;
    font-weight: 600;
    text-align: center;
    vertical-align: middle;
}

.table td {
    vertical-align: middle;
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function editAllowance(allowanceId, allowanceType, amount) {
    document.getElementById('edit_allowance_type').value = allowanceType;
    document.getElementById('edit_amount').value = amount;
    document.getElementById('editAllowanceForm').action = '/employees/{{ employee.id }}/allowances/' + allowanceId + '/edit';
    
    const modal = new bootstrap.Modal(document.getElementById('editAllowanceModal'));
    modal.show();
}

function deactivateAllowance(allowanceId, allowanceName) {
    document.getElementById('allowanceName').textContent = allowanceName;
    document.getElementById('deactivateForm').action = '/employees/{{ employee.id }}/allowances/' + allowanceId + '/deactivate';
    
    const modal = new bootstrap.Modal(document.getElementById('deactivateModal'));
    modal.show();
}

function activateAllowance(allowanceId) {
    if (confirm('هل تريد إعادة تفعيل هذا المخصص؟')) {
        const form = document.createElement('form');
        form.method = 'post';
        form.action = '/employees/{{ employee.id }}/allowances/' + allowanceId + '/activate';
        document.body.appendChild(form);
        form.submit();
    }
}

// تحديث ملخص الراتب عند تغيير المبلغ
document.getElementById('amount').addEventListener('input', function() {
    // يمكن إضافة تحديث فوري لملخص الراتب هنا
});
</script>
{% endblock %}
