# 📋 دليل إدارة الموظفين - نظام المحاسبة المتكامل

## 🎯 نظرة عامة

تم تطوير نظام شامل لإدارة الموظفين في المؤسسات الحكومية العراقية مع دعم كامل للغة العربية ومتطلبات النظام الحكومي.

## ✨ الوظائف المكتملة

### 1️⃣ **قائمة الموظفين** (`/employees`)
- ✅ عرض جميع الموظفين في جدول تفاعلي
- ✅ البحث بالاسم أو الرقم الوظيفي
- ✅ تصفية حسب الدائرة والقسم والدرجة الوظيفية
- ✅ إحصائيات سريعة (العدد الكلي، النشطون، إجمالي الرواتب)
- ✅ أزرار إجراءات سريعة (عرض، تعديل، مخصصات)

### 2️⃣ **إضافة موظف جديد** (`/employees/add`)
- ✅ نموذج شامل للبيانات الأساسية
- ✅ الهيكل التنظيمي (وزارة → دائرة → قسم → شعبة)
- ✅ معاينة الراتب المتوقع
- ✅ التحقق من صحة البيانات
- ✅ خيار "حفظ وإضافة آخر"
- ✅ إضافة الاستقطاعات الافتراضية تلقائياً

### 3️⃣ **استيراد من Excel** (`/employees/import`)
- ✅ رفع ملف Excel (.xlsx, .xls)
- ✅ تحليل وتنظيف البيانات
- ✅ التحقق من الأعمدة المطلوبة
- ✅ تجاهل الموظفين المكررين (اختياري)
- ✅ إضافة الاستقطاعات الافتراضية (اختياري)
- ✅ تقرير مفصل عن نتائج الاستيراد
- ✅ معاينة الملف قبل الاستيراد

### 4️⃣ **تفاصيل الموظف** (`/employees/{id}`)
- ✅ عرض شامل لجميع بيانات الموظف
- ✅ ملخص الراتب (أساسي + مخصصات - استقطاعات)
- ✅ معلومات الاتصال والهيكل التنظيمي
- ✅ حساب العمر وسنوات الخدمة
- ✅ الإجراءات السريعة (تعديل، مخصصات، تقارير)

### 5️⃣ **تعديل الموظف** (`/employees/{id}/edit`)
- ✅ تعديل جميع البيانات الأساسية
- ✅ التحقق من صحة البيانات
- ✅ حفظ التغييرات مع رسائل التأكيد

### 6️⃣ **إدارة المخصصات** (`/employees/{id}/allowances`)
- ✅ عرض المخصصات الحالية
- ✅ إضافة مخصصات جديدة
- ✅ تعديل وإلغاء تفعيل المخصصات
- ✅ ملخص الراتب المحدث فورياً

### 7️⃣ **تصدير البيانات**
- ✅ تصدير إلى Excel (`/employees/export/excel`)
- ✅ تحميل قالب Excel (`/employees/template/excel`)
- ✅ تنسيق احترافي للملفات المصدرة

## 🔧 المخصصات المدعومة

| نوع المخصص | الوصف | المبلغ النموذجي |
|------------|-------|----------------|
| **منصب** | مخصص المنصب الإداري | 100,000 د.ع |
| **زوجية** | مخصص الزوجة | 50,000 د.ع |
| **أولاد** | مخصص الأطفال | 25,000 د.ع لكل طفل |
| **هندسية** | مخصص الشهادة الهندسية | 150,000 د.ع |
| **شهادة** | مخصص الشهادة العلمية | 75,000 د.ع |
| **حرفة** | مخصص الحرفة | 60,000 د.ع |
| **خطورة** | مخصص العمل الخطر | 120,000 د.ع |
| **نقل** | مخصص النقل | 40,000 د.ع |
| **جامعية** | مخصص الشهادة الجامعية | 80,000 د.ع |

## 📉 الاستقطاعات الافتراضية

| نوع الاستقطاع | النسبة/المبلغ | الوصف |
|---------------|---------------|--------|
| **تقاعد** | 10% | من الراتب الأساسي |
| **مساهمة حكومية** | 15% | من الراتب الأساسي |
| **ضريبة الدخل** | متغيرة | حسب الشرائح |
| **حماية اجتماعية** | ثابتة | مبلغ محدد |

## 📊 متطلبات ملف Excel للاستيراد

### الأعمدة المطلوبة:
- **الرقم الوظيفي** (مطلوب) - نص فريد
- **الاسم الكامل** (مطلوب) - نص
- **المنصب** (مطلوب) - نص
- **الدرجة الوظيفية** (مطلوب) - نص
- **الراتب الأساسي** (مطلوب) - رقم

### الأعمدة الاختيارية:
- **الشهادة** - نص
- **المرحلة** - نص
- **رقم الهاتف** - نص
- **البريد الإلكتروني** - نص
- **العنوان** - نص

### مثال على تنسيق الملف:
```
الرقم الوظيفي | الاسم الكامل | المنصب | الدرجة الوظيفية | الراتب الأساسي
12001 | أحمد محمد علي | محاسب أول | الثالثة | 750000
12002 | فاطمة حسن محمود | كاتبة | الخامسة | 580000
```

## 🚀 كيفية الاستخدام

### تشغيل النظام:
```bash
# تطبيق الويب
python web_main.py
# ثم افتح: http://localhost:8000

# أو استخدم الملف المساعد
python start_web.py
```

### الوصول للوظائف:
1. **قائمة الموظفين**: `http://localhost:8000/employees`
2. **إضافة موظف**: `http://localhost:8000/employees/add`
3. **استيراد من Excel**: `http://localhost:8000/employees/import`
4. **تحميل قالب Excel**: `http://localhost:8000/employees/template/excel`

## 📁 الملفات المرفقة

- `sample_employees.csv` - ملف تجريبي للاختبار
- `demo.html` - عرض توضيحي تفاعلي
- `web_app/templates/employees/` - قوالب HTML للواجهات

## 🔒 الأمان والتحقق

- ✅ التحقق من نوع الملفات المرفوعة
- ✅ تنظيف وتحليل البيانات
- ✅ منع الأرقام الوظيفية المكررة
- ✅ التحقق من صحة البيانات المدخلة
- ✅ رسائل خطأ واضحة ومفيدة

## 📈 الإحصائيات والتقارير

- إجمالي عدد الموظفين
- عدد الموظفين النشطين
- إجمالي الرواتب الشهرية
- متوسط الراتب
- توزيع الموظفين حسب الدرجة الوظيفية

## 🎨 الواجهات

- تصميم عربي متجاوب
- دعم كامل للغة العربية
- واجهات سهلة الاستخدام
- رسائل تأكيد وتحذير واضحة
- أيقونات تعبيرية مفهومة

## 🔄 التحديثات المستقبلية

- [ ] إدارة الاستقطاعات المتقدمة
- [ ] تقارير مفصلة
- [ ] نظام الصلاحيات
- [ ] تصدير إلى PDF
- [ ] إشعارات النظام
- [ ] نسخ احتياطية تلقائية

---

**تم تطوير النظام بواسطة:** فريق التطوير
**تاريخ آخر تحديث:** ديسمبر 2024
**الإصدار:** 1.0.0
