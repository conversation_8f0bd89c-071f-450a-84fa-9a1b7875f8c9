# -*- coding: utf-8 -*-
"""
تطبيق سطح المكتب الرئيسي
Main Desktop Application using PyQt5
"""

import sys
import os
from pathlib import Path

# إضافة المسار الحالي إلى Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from PyQt5.QtWidgets import QApplication, QMainWindow, QMessageBox, QSplashScreen
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal
from PyQt5.QtGui import QPixmap, QIcon, QFont

# استيراد الإعدادات والنماذج
from config.settings import UI_CONFIG, create_directories, ORGANIZATION_INFO
from database.database_manager import db_manager
from utils.arabic_support import arabic_support, setup_arabic_application, apply_rtl_style
from desktop_app.login_window import LoginWindow
from desktop_app.main_window import MainWindow

class SplashScreen(QSplashScreen):
    """شاشة البداية"""
    
    def __init__(self):
        # إنشاء صورة افتراضية للشاشة
        pixmap = QPixmap(400, 300)
        pixmap.fill(Qt.white)
        super().__init__(pixmap)
        
        self.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        # إعداد النص
        self.showMessage(
            "جاري تحميل نظام المحاسبة المتكامل...",
            Qt.AlignCenter | Qt.AlignBottom,
            Qt.black
        )

class DatabaseInitThread(QThread):
    """خيط تهيئة قاعدة البيانات"""
    
    finished = pyqtSignal(bool)
    progress = pyqtSignal(str)
    
    def run(self):
        try:
            self.progress.emit("إنشاء المجلدات الأساسية...")
            create_directories()
            
            self.progress.emit("تهيئة قاعدة البيانات...")
            # قاعدة البيانات تتم تهيئتها تلقائياً عند إنشاء db_manager
            
            self.progress.emit("تحميل الإعدادات...")
            # تحميل أي إعدادات إضافية
            
            self.progress.emit("اكتمل التحميل!")
            self.finished.emit(True)
            
        except Exception as e:
            print(f"خطأ في تهيئة قاعدة البيانات: {e}")
            self.finished.emit(False)

class AccountingApplication(QApplication):
    """التطبيق الرئيسي"""
    
    def __init__(self, argv):
        super().__init__(argv)
        
        # إعداد التطبيق
        self.setApplicationName("نظام المحاسبة المتكامل")
        self.setApplicationVersion("1.0.0")
        self.setOrganizationName(ORGANIZATION_INFO['ministry'])
        self.setOrganizationDomain("moys.gov.iq")
        
        # إعداد الخط والاتجاه
        setup_arabic_application()
        
        # تطبيق الأنماط
        self.setStyleSheet(apply_rtl_style())
        
        # المتغيرات
        self.splash = None
        self.login_window = None
        self.main_window = None
        self.current_user = None
        
        # تهيئة التطبيق
        self.initialize_application()
    
    def initialize_application(self):
        """تهيئة التطبيق"""
        # عرض شاشة البداية
        self.show_splash_screen()
        
        # تهيئة قاعدة البيانات في خيط منفصل
        self.init_database()
    
    def show_splash_screen(self):
        """عرض شاشة البداية"""
        self.splash = SplashScreen()
        self.splash.show()
        
        # معالجة الأحداث لضمان ظهور الشاشة
        self.processEvents()
    
    def init_database(self):
        """تهيئة قاعدة البيانات"""
        self.db_thread = DatabaseInitThread()
        self.db_thread.progress.connect(self.update_splash_message)
        self.db_thread.finished.connect(self.on_database_ready)
        self.db_thread.start()
    
    def update_splash_message(self, message):
        """تحديث رسالة شاشة البداية"""
        if self.splash:
            self.splash.showMessage(
                message,
                Qt.AlignCenter | Qt.AlignBottom,
                Qt.black
            )
            self.processEvents()
    
    def on_database_ready(self, success):
        """عند اكتمال تهيئة قاعدة البيانات"""
        if success:
            # إخفاء شاشة البداية بعد ثانيتين
            QTimer.singleShot(2000, self.show_login)
        else:
            self.show_error("فشل في تهيئة قاعدة البيانات")
            self.quit()
    
    def show_login(self):
        """عرض نافذة تسجيل الدخول"""
        if self.splash:
            self.splash.close()
            self.splash = None
        
        self.login_window = LoginWindow()
        self.login_window.login_successful.connect(self.on_login_successful)
        self.login_window.show()
    
    def on_login_successful(self, user):
        """عند نجاح تسجيل الدخول"""
        self.current_user = user
        
        # إغلاق نافذة تسجيل الدخول
        if self.login_window:
            self.login_window.close()
            self.login_window = None
        
        # عرض النافذة الرئيسية
        self.show_main_window()
    
    def show_main_window(self):
        """عرض النافذة الرئيسية"""
        self.main_window = MainWindow(self.current_user)
        self.main_window.logout_requested.connect(self.on_logout_requested)
        self.main_window.show()
    
    def on_logout_requested(self):
        """عند طلب تسجيل الخروج"""
        reply = QMessageBox.question(
            self.main_window,
            "تسجيل الخروج",
            "هل أنت متأكد من تسجيل الخروج؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # إغلاق النافذة الرئيسية
            if self.main_window:
                self.main_window.close()
                self.main_window = None
            
            # إعادة تعيين المستخدم الحالي
            self.current_user = None
            
            # عرض نافذة تسجيل الدخول مرة أخرى
            self.show_login()
    
    def show_error(self, message):
        """عرض رسالة خطأ"""
        msg_box = QMessageBox()
        msg_box.setIcon(QMessageBox.Critical)
        msg_box.setWindowTitle("خطأ")
        msg_box.setText(message)
        msg_box.setStandardButtons(QMessageBox.Ok)
        msg_box.exec_()
    
    def closeEvent(self, event):
        """عند إغلاق التطبيق"""
        # حفظ الإعدادات
        self.save_settings()
        
        # إغلاق اتصالات قاعدة البيانات
        try:
            if hasattr(db_manager, 'engine'):
                db_manager.engine.dispose()
        except:
            pass
        
        event.accept()
    
    def save_settings(self):
        """حفظ إعدادات التطبيق"""
        # يمكن إضافة حفظ الإعدادات هنا
        pass

def main():
    """الدالة الرئيسية"""
    try:
        # إنشاء التطبيق
        app = AccountingApplication(sys.argv)
        
        # تشغيل التطبيق
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"خطأ في تشغيل التطبيق: {e}")
        
        # عرض رسالة خطأ للمستخدم
        error_app = QApplication(sys.argv) if not QApplication.instance() else QApplication.instance()
        
        msg_box = QMessageBox()
        msg_box.setIcon(QMessageBox.Critical)
        msg_box.setWindowTitle("خطأ في التطبيق")
        msg_box.setText(f"حدث خطأ في تشغيل التطبيق:\n{str(e)}")
        msg_box.setDetailedText(str(e))
        msg_box.setStandardButtons(QMessageBox.Ok)
        msg_box.exec_()
        
        sys.exit(1)

if __name__ == "__main__":
    main()
