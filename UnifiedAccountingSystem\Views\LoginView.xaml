<?xml version="1.0" encoding="utf-8"?>
<Window x:Class="LoginView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="تسجيل الدخول - نظام إدارة الرواتب والمحاسبة" 
        Height="650" Width="500"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Medium"
        TextElement.FontSize="14"
        FontFamily="Cairo"
        FlowDirection="RightToLeft">

    <Window.Resources>
        <!-- تعريف الألوان والأنماط -->
        <SolidColorBrush x:Key="PrimaryBrush" Color="#2196F3"/>
        <SolidColorBrush x:Key="AccentBrush" Color="#FF4081"/>
        <SolidColorBrush x:Key="BackgroundBrush" Color="#FAFAFA"/>
        
        <!-- أنماط مخصصة للحقول -->
        <Style x:Key="LoginTextBox" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignFloatingHintTextBox}">
            <Setter Property="Margin" Value="0,10"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="Height" Value="50"/>
        </Style>
        
        <Style x:Key="LoginPasswordBox" TargetType="PasswordBox" BasedOn="{StaticResource MaterialDesignFloatingHintPasswordBox}">
            <Setter Property="Margin" Value="0,10"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="Height" Value="50"/>
        </Style>
    </Window.Resources>

    <Border Background="White" CornerRadius="15" Margin="10">
        <Border.Effect>
            <DropShadowEffect Color="Black" Direction="270" ShadowDepth="5" Opacity="0.3" BlurRadius="15"/>
        </Border.Effect>
        
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="150"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="80"/>
            </Grid.RowDefinitions>

            <!-- رأس النافذة -->
            <Grid Grid.Row="0" Background="{StaticResource PrimaryBrush}">
                <Grid.RowDefinitions>
                    <RowDefinition Height="30"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                
                <!-- أزرار التحكم -->
                <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Left" Margin="10,5">
                    <Button x:Name="CloseButton" Width="20" Height="20" 
                            Style="{StaticResource MaterialDesignIconButton}"
                            Foreground="White" Click="CloseButton_Click">
                        <materialDesign:PackIcon Kind="Close" Width="16" Height="16"/>
                    </Button>
                    <Button x:Name="MinimizeButton" Width="20" Height="20" 
                            Style="{StaticResource MaterialDesignIconButton}"
                            Foreground="White" Click="MinimizeButton_Click" Margin="5,0,0,0">
                        <materialDesign:PackIcon Kind="Minus" Width="16" Height="16"/>
                    </Button>
                </StackPanel>

                <!-- عنوان التطبيق -->
                <StackPanel Grid.Row="1" VerticalAlignment="Center" HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="Calculator" Width="40" Height="40" 
                                           Foreground="White" HorizontalAlignment="Center"/>
                    <TextBlock Text="نظام إدارة الرواتب والمحاسبة" 
                             Foreground="White" FontSize="18" FontWeight="Bold"
                             HorizontalAlignment="Center" Margin="0,10,0,0"/>
                    <TextBlock Text="وزارة الشباب والرياضة - دائرة الطب الرياضي" 
                             Foreground="White" FontSize="12" Opacity="0.8"
                             HorizontalAlignment="Center" Margin="0,5,0,0"/>
                </StackPanel>
            </Grid>

            <!-- منطقة تسجيل الدخول -->
            <Grid Grid.Row="1" Margin="40,30">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- عنوان تسجيل الدخول -->
                <TextBlock Grid.Row="0" Text="تسجيل الدخول" 
                         FontSize="24" FontWeight="Bold" 
                         HorizontalAlignment="Center" Margin="0,0,0,30"
                         Foreground="{StaticResource PrimaryBrush}"/>

                <!-- حقل اسم المستخدم -->
                <TextBox Grid.Row="1" x:Name="UsernameTextBox"
                       Style="{StaticResource LoginTextBox}"
                       materialDesign:HintAssist.Hint="اسم المستخدم"
                       materialDesign:HintAssist.IsFloating="True">
                    <TextBox.Resources>
                        <Style TargetType="Border">
                            <Setter Property="CornerRadius" Value="5"/>
                        </Style>
                    </TextBox.Resources>
                </TextBox>

                <!-- حقل كلمة المرور -->
                <PasswordBox Grid.Row="2" x:Name="PasswordBox"
                           Style="{StaticResource LoginPasswordBox}"
                           materialDesign:HintAssist.Hint="كلمة المرور"
                           materialDesign:HintAssist.IsFloating="True">
                    <PasswordBox.Resources>
                        <Style TargetType="Border">
                            <Setter Property="CornerRadius" Value="5"/>
                        </Style>
                    </PasswordBox.Resources>
                </PasswordBox>

                <!-- خيار تذكر كلمة المرور -->
                <CheckBox Grid.Row="3" x:Name="RememberCheckBox" 
                        Content="تذكر كلمة المرور" Margin="0,15,0,0"
                        Style="{StaticResource MaterialDesignCheckBox}"/>

                <!-- زر تسجيل الدخول -->
                <Button Grid.Row="4" x:Name="LoginButton" 
                      Content="تسجيل الدخول" 
                      Height="45" Margin="0,25,0,0"
                      Style="{StaticResource MaterialDesignRaisedButton}"
                      Background="{StaticResource PrimaryBrush}"
                      BorderBrush="{StaticResource PrimaryBrush}"
                      Click="LoginButton_Click">
                    <Button.Resources>
                        <Style TargetType="Border">
                            <Setter Property="CornerRadius" Value="22"/>
                        </Style>
                    </Button.Resources>
                </Button>

                <!-- رسالة الخطأ -->
                <TextBlock Grid.Row="5" x:Name="ErrorMessage" 
                         Foreground="Red" FontSize="12" 
                         HorizontalAlignment="Center" 
                         Margin="0,15,0,0" Visibility="Collapsed"/>
            </Grid>

            <!-- تذييل النافذة -->
            <Grid Grid.Row="2" Background="#F5F5F5">
                <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                    <TextBlock Text="© 2024 جميع الحقوق محفوظة" 
                             FontSize="10" Foreground="Gray" 
                             HorizontalAlignment="Center"/>
                    <TextBlock Text="نظام المحاسبة المتكامل v1.0" 
                             FontSize="10" Foreground="Gray" 
                             HorizontalAlignment="Center" Margin="0,2,0,0"/>
                </StackPanel>
            </Grid>
        </Grid>
    </Border>
</Window>
