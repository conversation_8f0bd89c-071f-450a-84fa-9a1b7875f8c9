// ملف تكوين القوائم - Lists Configuration
// يحتوي على إعدادات وبيانات جميع القوائم

const LISTS_CONFIG = {
    // قائمة الأقسام
    departments: {
        title: 'إدارة الأقسام',
        color: '#28a745',
        tableId: 'departmentsTableBody',
        searchId: 'searchInput',
        fields: [
            { id: 'deptCode', label: 'رمز القسم', type: 'text', placeholder: 'مثال: DEPT-001', required: true },
            { id: 'deptName', label: 'اسم القسم', type: 'text', placeholder: 'مثال: قسم المحاسبة', required: true },
            { id: 'deptType', label: 'نوع القسم', type: 'select', required: true, options: [
                { value: 'إداري', text: 'إداري' },
                { value: 'طبي', text: 'طبي' },
                { value: 'تقني', text: 'تقني' },
                { value: 'مالي', text: 'مالي' }
            ]},
            { id: 'deptManager', label: 'مدير القسم', type: 'text', placeholder: 'اسم المدير' },
            { id: 'deptDescription', label: 'الوصف', type: 'textarea', placeholder: 'وصف القسم ومهامه...' }
        ],
        columns: [
            { field: 'code', type: 'strong' },
            { field: 'name' },
            { field: 'type', type: 'badge', badgeClass: (val) => val === 'إداري' ? 'bg-primary' : val === 'طبي' ? 'bg-success' : 'bg-info' },
            { field: 'manager' },
            { field: 'employeeCount', type: 'badge', badgeClass: () => 'bg-warning' }
        ],
        actions: [
            { icon: 'eye', color: 'info', title: 'عرض التفاصيل', onclick: 'viewDepartment' },
            { icon: 'edit', color: 'warning', title: 'تعديل', onclick: 'editDepartment' },
            { icon: 'users', color: 'primary', title: 'الموظفين', onclick: 'viewDepartmentEmployees' }
        ]
    },

    // قائمة المحافظات
    provinces: {
        title: 'إدارة المحافظات',
        color: '#17a2b8',
        tableId: 'provincesTableBody',
        searchId: 'searchInput',
        fields: [
            { id: 'provCode', label: 'رمز المحافظة', type: 'text', placeholder: 'مثال: BGD', required: true },
            { id: 'provName', label: 'اسم المحافظة', type: 'text', placeholder: 'مثال: بغداد', required: true },
            { id: 'provRegion', label: 'المنطقة', type: 'select', required: true, options: [
                { value: 'الوسط', text: 'الوسط' },
                { value: 'الشمال', text: 'الشمال' },
                { value: 'الجنوب', text: 'الجنوب' }
            ]},
            { id: 'provCapital', label: 'مركز المحافظة', type: 'text', placeholder: 'المركز الإداري' },
            { id: 'provPopulation', label: 'عدد السكان', type: 'number', placeholder: '0' }
        ],
        columns: [
            { field: 'code', type: 'strong' },
            { field: 'name' },
            { field: 'region', type: 'badge', badgeClass: (val) => val === 'الوسط' ? 'bg-primary' : val === 'الشمال' ? 'bg-success' : 'bg-warning' },
            { field: 'capital' },
            { field: 'population', type: 'badge', badgeClass: () => 'bg-info' },
            { field: 'employeeCount', type: 'badge', badgeClass: () => 'bg-secondary' }
        ],
        actions: [
            { icon: 'eye', color: 'info', title: 'عرض التفاصيل', onclick: 'viewProvince' },
            { icon: 'edit', color: 'warning', title: 'تعديل', onclick: 'editProvince' },
            { icon: 'users', color: 'primary', title: 'الموظفين', onclick: 'viewProvinceEmployees' }
        ]
    },

    // قائمة البنوك
    banks: {
        title: 'إدارة البنوك',
        color: '#ffc107',
        tableId: 'banksTableBody',
        searchId: 'searchInput',
        fields: [
            { id: 'bankCode', label: 'رمز البنك', type: 'text', placeholder: 'مثال: CBI', required: true },
            { id: 'bankName', label: 'اسم البنك', type: 'text', placeholder: 'مثال: البنك المركزي العراقي', required: true },
            { id: 'bankType', label: 'نوع البنك', type: 'select', required: true, options: [
                { value: 'حكومي', text: 'حكومي' },
                { value: 'خاص', text: 'خاص' },
                { value: 'إسلامي', text: 'إسلامي' },
                { value: 'أجنبي', text: 'أجنبي' }
            ]},
            { id: 'bankSwift', label: 'رمز SWIFT', type: 'text', placeholder: 'SWIFT Code' },
            { id: 'bankAddress', label: 'العنوان', type: 'textarea', placeholder: 'عنوان المقر الرئيسي...' }
        ],
        columns: [
            { field: 'code', type: 'strong' },
            { field: 'name' },
            { field: 'type', type: 'badge', badgeClass: (val) => val === 'حكومي' ? 'bg-success' : val === 'خاص' ? 'bg-primary' : 'bg-info' },
            { field: 'swift' },
            { field: 'branches', type: 'badge', badgeClass: () => 'bg-warning' },
            { field: 'status', type: 'badge', badgeClass: (val) => val === 'نشط' ? 'bg-success' : 'bg-danger' }
        ],
        actions: [
            { icon: 'eye', color: 'info', title: 'عرض التفاصيل', onclick: 'viewBank' },
            { icon: 'edit', color: 'warning', title: 'تعديل', onclick: 'editBank' },
            { icon: 'building', color: 'primary', title: 'الفروع', onclick: 'viewBankBranches' }
        ]
    },

    // قائمة الدرجات الوظيفية
    grades: {
        title: 'إدارة الدرجات الوظيفية',
        color: '#6f42c1',
        tableId: 'gradesTableBody',
        searchId: 'searchInput',
        fields: [
            { id: 'gradeCode', label: 'رمز الدرجة', type: 'text', placeholder: 'مثال: GR-01', required: true },
            { id: 'gradeName', label: 'اسم الدرجة', type: 'text', placeholder: 'مثال: الدرجة الأولى', required: true },
            { id: 'gradeLevel', label: 'المستوى', type: 'select', required: true, options: [
                { value: '1', text: 'المستوى الأول' },
                { value: '2', text: 'المستوى الثاني' },
                { value: '3', text: 'المستوى الثالث' },
                { value: '4', text: 'المستوى الرابع' },
                { value: '5', text: 'المستوى الخامس' }
            ]},
            { id: 'gradeSalary', label: 'الراتب الأساسي', type: 'number', placeholder: '0' },
            { id: 'gradeAllowance', label: 'العلاوة', type: 'number', placeholder: '0' }
        ],
        columns: [
            { field: 'code', type: 'strong' },
            { field: 'name' },
            { field: 'level', type: 'badge', badgeClass: (val) => `bg-${val <= 2 ? 'success' : val <= 4 ? 'warning' : 'danger'}` },
            { field: 'salary', type: 'badge', badgeClass: () => 'bg-success' },
            { field: 'allowance', type: 'badge', badgeClass: () => 'bg-info' },
            { field: 'employeeCount', type: 'badge', badgeClass: () => 'bg-secondary' }
        ],
        actions: [
            { icon: 'eye', color: 'info', title: 'عرض التفاصيل', onclick: 'viewGrade' },
            { icon: 'edit', color: 'warning', title: 'تعديل', onclick: 'editGrade' },
            { icon: 'users', color: 'primary', title: 'الموظفين', onclick: 'viewGradeEmployees' }
        ]
    },

    // قائمة المراحل
    stages: {
        title: 'إدارة المراحل',
        color: '#fd7e14',
        tableId: 'stagesTableBody',
        searchId: 'searchInput',
        fields: [
            { id: 'stageCode', label: 'رمز المرحلة', type: 'text', placeholder: 'مثال: STG-01', required: true },
            { id: 'stageName', label: 'اسم المرحلة', type: 'text', placeholder: 'مثال: مرحلة التعيين', required: true },
            { id: 'stageType', label: 'نوع المرحلة', type: 'select', required: true, options: [
                { value: 'تعيين', text: 'تعيين' },
                { value: 'ترقية', text: 'ترقية' },
                { value: 'تدريب', text: 'تدريب' },
                { value: 'تقييم', text: 'تقييم' }
            ]},
            { id: 'stageDuration', label: 'المدة (بالأشهر)', type: 'number', placeholder: '0' },
            { id: 'stageDescription', label: 'الوصف', type: 'textarea', placeholder: 'وصف المرحلة...' }
        ],
        columns: [
            { field: 'code', type: 'strong' },
            { field: 'name' },
            { field: 'type', type: 'badge', badgeClass: (val) => val === 'تعيين' ? 'bg-success' : val === 'ترقية' ? 'bg-primary' : 'bg-info' },
            { field: 'duration', type: 'badge', badgeClass: () => 'bg-warning' },
            { field: 'employeeCount', type: 'badge', badgeClass: () => 'bg-secondary' },
            { field: 'status', type: 'badge', badgeClass: (val) => val === 'نشط' ? 'bg-success' : 'bg-danger' }
        ],
        actions: [
            { icon: 'eye', color: 'info', title: 'عرض التفاصيل', onclick: 'viewStage' },
            { icon: 'edit', color: 'warning', title: 'تعديل', onclick: 'editStage' },
            { icon: 'users', color: 'primary', title: 'الموظفين', onclick: 'viewStageEmployees' }
        ]
    }
};

// وظائف مساعدة للتكوين
function getListConfig(listName) {
    return LISTS_CONFIG[listName] || null;
}

function getAllListNames() {
    return Object.keys(LISTS_CONFIG);
}

// بيانات تجريبية للقوائم
const SAMPLE_DATA = {
    departments: [
        { code: 'DEPT-001', name: 'قسم المحاسبة', type: 'مالي', manager: 'أحمد محمد', employeeCount: 15 },
        { code: 'DEPT-002', name: 'قسم الموارد البشرية', type: 'إداري', manager: 'فاطمة علي', employeeCount: 8 },
        { code: 'DEPT-003', name: 'قسم تقنية المعلومات', type: 'تقني', manager: 'محمد حسن', employeeCount: 12 }
    ],
    provinces: [
        { code: 'BGD', name: 'بغداد', region: 'الوسط', capital: 'بغداد', population: '8000000', employeeCount: 1250 },
        { code: 'BSR', name: 'البصرة', region: 'الجنوب', capital: 'البصرة', population: '2500000', employeeCount: 450 },
        { code: 'ERB', name: 'أربيل', region: 'الشمال', capital: 'أربيل', population: '1800000', employeeCount: 320 }
    ],
    banks: [
        { code: 'CBI', name: 'البنك المركزي العراقي', type: 'حكومي', swift: 'CBIQIQBA', branches: 18, status: 'نشط' },
        { code: 'RFD', name: 'مصرف الرافدين', type: 'حكومي', swift: 'RFDNIQBA', branches: 145, status: 'نشط' },
        { code: 'RSH', name: 'مصرف الرشيد', type: 'حكومي', swift: 'RSHDIQBA', branches: 98, status: 'نشط' }
    ]
};

console.log('✅ ملف تكوين القوائم محمل بنجاح');
console.log(`📋 ${getAllListNames().length} قائمة متاحة:`, getAllListNames());
