# -*- coding: utf-8 -*-
"""
نموذج الحسابات البنكية
Bank Account Model
"""

from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, Text
from sqlalchemy.sql import func
from database.database_manager import Base
from datetime import datetime

class BankAccount(Base):
    __tablename__ = "bank_accounts"
    
    id = Column(Integer, primary_key=True, index=True)
    account_number = Column(String(50), unique=True, index=True, nullable=False)
    account_name = Column(String(100), nullable=False)
    bank_name = Column(String(100), nullable=False)
    branch_name = Column(String(100))
    branch_code = Column(String(10))
    account_type = Column(String(50), nullable=False)  # تشغيلي، رواتب، إلخ
    
    # الرصيد والمعاملات
    current_balance = Column(Float, default=0.0)
    last_transaction_date = Column(DateTime)
    
    # معلومات إضافية
    iban = Column(String(50))  # رقم الحساب الدولي
    swift_code = Column(String(20))  # رمز البنك الدولي
    currency = Column(String(10), default='IQD')  # العملة
    
    # حالة الحساب
    is_active = Column(Boolean, default=True)
    opening_date = Column(DateTime, server_default=func.now())
    closing_date = Column(DateTime)
    
    # ملاحظات
    notes = Column(Text)
    
    # تواريخ النظام
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    
    def __init__(self, account_number, account_name, bank_name, account_type,
                 branch_name=None, branch_code=None, current_balance=0.0, **kwargs):
        self.account_number = account_number
        self.account_name = account_name
        self.bank_name = bank_name
        self.account_type = account_type
        self.branch_name = branch_name
        self.branch_code = branch_code
        self.current_balance = current_balance
        
        # معلومات إضافية
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def deposit(self, amount, description=None):
        """إيداع مبلغ في الحساب"""
        if amount > 0:
            self.current_balance += amount
            self.last_transaction_date = datetime.now()
            
            # تسجيل المعاملة
            self.record_transaction('deposit', amount, description)
            return True
        return False
    
    def withdraw(self, amount, description=None):
        """سحب مبلغ من الحساب"""
        if amount > 0 and self.current_balance >= amount:
            self.current_balance -= amount
            self.last_transaction_date = datetime.now()
            
            # تسجيل المعاملة
            self.record_transaction('withdrawal', amount, description)
            return True
        return False
    
    def transfer_to(self, target_account, amount, description=None):
        """تحويل مبلغ إلى حساب آخر"""
        if self.withdraw(amount, f"تحويل إلى {target_account.account_name}"):
            target_account.deposit(amount, f"تحويل من {self.account_name}")
            return True
        return False
    
    def record_transaction(self, transaction_type, amount, description=None):
        """تسجيل المعاملة المالية"""
        # يمكن إضافة جدول منفصل للمعاملات لاحقاً
        pass
    
    def get_balance_display(self):
        """عرض الرصيد مع العملة"""
        return f"{self.current_balance:,.2f} {self.currency}"
    
    def is_sufficient_balance(self, amount):
        """التحقق من كفاية الرصيد"""
        return self.current_balance >= amount
    
    def close_account(self, reason=None):
        """إغلاق الحساب"""
        self.is_active = False
        self.closing_date = datetime.now()
        if reason:
            self.notes = f"{self.notes or ''}\nسبب الإغلاق: {reason}".strip()
    
    def reopen_account(self):
        """إعادة فتح الحساب"""
        self.is_active = True
        self.closing_date = None
    
    @classmethod
    def get_account_types(cls):
        """الحصول على أنواع الحسابات المتاحة"""
        return [
            'تشغيلي',
            'رواتب',
            'مشاريع',
            'طوارئ',
            'احتياطي'
        ]
    
    @classmethod
    def get_active_accounts(cls):
        """الحصول على الحسابات النشطة"""
        from database.database_manager import db_manager
        
        session = db_manager.get_session()
        try:
            return session.query(cls).filter(cls.is_active == True).all()
        finally:
            session.close()
    
    @classmethod
    def get_by_type(cls, account_type):
        """الحصول على الحسابات حسب النوع"""
        from database.database_manager import db_manager
        
        session = db_manager.get_session()
        try:
            return session.query(cls).filter(
                cls.account_type == account_type,
                cls.is_active == True
            ).all()
        finally:
            session.close()
    
    @classmethod
    def get_total_balance(cls):
        """الحصول على إجمالي الأرصدة"""
        from database.database_manager import db_manager
        
        session = db_manager.get_session()
        try:
            result = session.query(func.sum(cls.current_balance)).filter(
                cls.is_active == True
            ).scalar()
            return result or 0.0
        finally:
            session.close()
    
    def to_dict(self):
        """تحويل إلى قاموس"""
        return {
            'id': self.id,
            'account_number': self.account_number,
            'account_name': self.account_name,
            'bank_name': self.bank_name,
            'branch_name': self.branch_name,
            'branch_code': self.branch_code,
            'account_type': self.account_type,
            'current_balance': self.current_balance,
            'balance_display': self.get_balance_display(),
            'iban': self.iban,
            'swift_code': self.swift_code,
            'currency': self.currency,
            'is_active': self.is_active,
            'opening_date': self.opening_date.isoformat() if self.opening_date else None,
            'closing_date': self.closing_date.isoformat() if self.closing_date else None,
            'last_transaction_date': self.last_transaction_date.isoformat() if self.last_transaction_date else None,
            'notes': self.notes
        }
    
    def __repr__(self):
        return f"<BankAccount(number='{self.account_number}', name='{self.account_name}', balance={self.current_balance})>"
