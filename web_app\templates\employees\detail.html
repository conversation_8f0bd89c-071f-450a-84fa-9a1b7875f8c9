{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-user me-2 text-primary"></i>
                تفاصيل الموظف
            </h1>
            <div>
                <a href="/employees/{{ employee.id }}/edit" class="btn btn-warning me-2">
                    <i class="fas fa-edit me-1"></i>
                    تعديل
                </a>
                <a href="/employees" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right me-1"></i>
                    العودة إلى القائمة
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- البيانات الأساسية -->
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    البيانات الأساسية
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">الرقم الوظيفي:</label>
                        <p class="form-control-plaintext">{{ employee.employee_number }}</p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">الاسم الكامل:</label>
                        <p class="form-control-plaintext">{{ employee.full_name }}</p>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">العنوان الوظيفي:</label>
                        <p class="form-control-plaintext">{{ employee.job_title }}</p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">الدرجة الوظيفية:</label>
                        <p class="form-control-plaintext">
                            <span class="badge bg-info fs-6">{{ employee.job_grade }}</span>
                        </p>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">الشهادة:</label>
                        <p class="form-control-plaintext">{{ employee.qualification or 'غير محدد' }}</p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">المرحلة:</label>
                        <p class="form-control-plaintext">{{ employee.stage or 'غير محدد' }}</p>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">الراتب الأساسي:</label>
                        <p class="form-control-plaintext">
                            <span class="h5 text-success">{{ "{:,.0f}".format(employee.basic_salary) }} دينار عراقي</span>
                        </p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">تاريخ التعيين:</label>
                        <p class="form-control-plaintext">
                            {% if employee.hire_date %}
                                {{ employee.hire_date.strftime('%Y-%m-%d') }}
                            {% else %}
                                غير محدد
                            {% endif %}
                        </p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- الهيكل التنظيمي -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-sitemap me-2"></i>
                    الهيكل التنظيمي
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">الوزارة:</label>
                        <p class="form-control-plaintext">{{ employee.ministry }}</p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">الدائرة:</label>
                        <p class="form-control-plaintext">{{ employee.department }}</p>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">القسم:</label>
                        <p class="form-control-plaintext">{{ employee.section }}</p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">الشعبة:</label>
                        <p class="form-control-plaintext">{{ employee.division }}</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- معلومات الاتصال -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-address-book me-2"></i>
                    معلومات الاتصال
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">رقم الهاتف:</label>
                        <p class="form-control-plaintext">
                            {% if employee.phone %}
                                <a href="tel:{{ employee.phone }}" class="text-decoration-none">
                                    <i class="fas fa-phone me-1"></i>
                                    {{ employee.phone }}
                                </a>
                            {% else %}
                                غير محدد
                            {% endif %}
                        </p>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">البريد الإلكتروني:</label>
                        <p class="form-control-plaintext">
                            {% if employee.email %}
                                <a href="mailto:{{ employee.email }}" class="text-decoration-none">
                                    <i class="fas fa-envelope me-1"></i>
                                    {{ employee.email }}
                                </a>
                            {% else %}
                                غير محدد
                            {% endif %}
                        </p>
                    </div>
                </div>
                
                {% if employee.address %}
                <div class="row">
                    <div class="col-12 mb-3">
                        <label class="form-label fw-bold">العنوان:</label>
                        <p class="form-control-plaintext">{{ employee.address }}</p>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- الملخص والإحصائيات -->
    <div class="col-lg-4">
        <!-- حالة الموظف -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    حالة الموظف
                </h5>
            </div>
            <div class="card-body text-center">
                {% if employee.is_active %}
                    <div class="mb-3">
                        <i class="fas fa-user-check fa-3x text-success"></i>
                    </div>
                    <h5 class="text-success">موظف نشط</h5>
                    <p class="text-muted">الموظف يعمل حالياً في المؤسسة</p>
                {% else %}
                    <div class="mb-3">
                        <i class="fas fa-user-times fa-3x text-danger"></i>
                    </div>
                    <h5 class="text-danger">موظف غير نشط</h5>
                    <p class="text-muted">الموظف لا يعمل حالياً</p>
                {% endif %}
            </div>
        </div>
        
        <!-- معلومات إضافية -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    معلومات إضافية
                </h5>
            </div>
            <div class="card-body">
                {% if employee.birth_date %}
                <div class="d-flex justify-content-between mb-2">
                    <span>العمر:</span>
                    <span>{{ employee.get_age() }} سنة</span>
                </div>
                {% endif %}
                
                {% if employee.hire_date %}
                <div class="d-flex justify-content-between mb-2">
                    <span>سنوات الخدمة:</span>
                    <span>{{ employee.get_service_years() }} سنة</span>
                </div>
                {% endif %}
                
                <div class="d-flex justify-content-between mb-2">
                    <span>إجمالي المخصصات:</span>
                    <span class="text-success">{{ "{:,.0f}".format(employee.calculate_total_allowances()) }} د.ع</span>
                </div>
                
                <div class="d-flex justify-content-between mb-2">
                    <span>إجمالي الاستقطاعات:</span>
                    <span class="text-danger">{{ "{:,.0f}".format(employee.calculate_total_deductions()) }} د.ع</span>
                </div>
                
                <hr>
                
                <div class="d-flex justify-content-between">
                    <strong>الراتب الصافي:</strong>
                    <strong class="text-primary">{{ "{:,.0f}".format(employee.calculate_net_salary()) }} د.ع</strong>
                </div>
            </div>
        </div>
        
        <!-- الإجراءات السريعة -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    الإجراءات السريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="/employees/{{ employee.id }}/allowances" class="btn btn-success">
                        <i class="fas fa-plus-circle me-1"></i>
                        إدارة المخصصات
                    </a>
                    
                    <a href="/employees/{{ employee.id }}/deductions" class="btn btn-warning">
                        <i class="fas fa-minus-circle me-1"></i>
                        إدارة الاستقطاعات
                    </a>
                    
                    <a href="/salaries/employee/{{ employee.id }}" class="btn btn-info">
                        <i class="fas fa-money-bill-wave me-1"></i>
                        تاريخ الرواتب
                    </a>
                    
                    <a href="/employees/{{ employee.id }}/report" class="btn btn-primary">
                        <i class="fas fa-file-pdf me-1"></i>
                        تقرير الموظف
                    </a>
                    
                    <hr>
                    
                    <button type="button" class="btn btn-outline-danger" onclick="confirmDelete()">
                        <i class="fas fa-user-times me-1"></i>
                        إلغاء تفعيل الموظف
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نافذة تأكيد إلغاء التفعيل -->
<div class="modal fade" id="deactivateModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد إلغاء التفعيل</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من إلغاء تفعيل الموظف <strong>{{ employee.full_name }}</strong>؟</p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    سيتم إيقاف جميع العمليات المتعلقة بهذا الموظف!
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form method="post" action="/employees/{{ employee.id }}/delete" style="display: inline;">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-user-times me-1"></i>
                        إلغاء التفعيل
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.card-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
}

.form-control-plaintext {
    padding: 0.375rem 0;
    margin-bottom: 0;
    font-size: 1rem;
    line-height: 1.5;
    color: #212529;
    background-color: transparent;
    border: solid transparent;
    border-width: 1px 0;
}

.form-label {
    margin-bottom: 0.25rem;
    font-size: 0.9rem;
    color: #6c757d;
}

.badge {
    font-size: 0.9rem;
}

.btn-outline-danger:hover {
    background-color: #dc3545;
    border-color: #dc3545;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete() {
    const modal = new bootstrap.Modal(document.getElementById('deactivateModal'));
    modal.show();
}
</script>
{% endblock %}
