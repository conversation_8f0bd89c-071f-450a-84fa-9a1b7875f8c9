<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دليل الأقسام - نظام المحاسبة الحكومية</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        .controls {
            padding: 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .controls-row {
            display: flex;
            gap: 20px;
            align-items: center;
            flex-wrap: wrap;
            justify-content: space-between;
        }

        .search-box {
            flex: 1;
            min-width: 300px;
            position: relative;
        }

        .search-box input {
            width: 100%;
            padding: 15px 50px 15px 20px;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            font-size: 1rem;
            font-family: 'Cairo', sans-serif;
            transition: all 0.3s ease;
        }

        .search-box input:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .search-box i {
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
        }

        .filter-group {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .filter-select {
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-family: 'Cairo', sans-serif;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .filter-select:focus {
            outline: none;
            border-color: #3498db;
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60, #229954);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            padding: 30px;
            background: #f8f9fa;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
        }

        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .departments-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 25px;
            padding: 30px;
        }

        .department-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            border: 1px solid #e9ecef;
            position: relative;
            overflow: hidden;
        }

        .department-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(135deg, #3498db, #2980b9);
        }

        .department-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.15);
        }

        .department-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 20px;
        }

        .department-info h3 {
            color: #2c3e50;
            font-size: 1.3rem;
            margin-bottom: 5px;
        }

        .department-code {
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .department-details {
            margin-bottom: 20px;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px 0;
        }

        .detail-label {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .detail-value {
            font-weight: 600;
            color: #2c3e50;
        }

        .department-actions {
            display: flex;
            gap: 8px;
            justify-content: center;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }

        .action-btn {
            padding: 8px 15px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.85rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .action-btn:hover {
            transform: translateY(-1px);
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            padding: 30px;
            background: #f8f9fa;
        }

        .page-btn {
            padding: 10px 15px;
            border: 1px solid #dee2e6;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .page-btn:hover, .page-btn.active {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }

        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .modal.active {
            opacity: 1;
            visibility: visible;
        }

        .modal-content {
            background: white;
            border-radius: 15px;
            padding: 30px;
            max-width: 800px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            transform: scale(0.9);
            transition: all 0.3s ease;
        }

        .modal.active .modal-content {
            transform: scale(1);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
        }

        .modal-title {
            font-size: 1.5rem;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .close-btn {
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .close-btn:hover {
            background: #c82333;
            transform: scale(1.1);
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-label {
            margin-bottom: 8px;
            font-weight: 600;
            color: #495057;
        }

        .form-input {
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-family: 'Cairo', sans-serif;
            transition: all 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }

        .form-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 25px;
            padding-top: 25px;
            border-top: 1px solid #e9ecef;
        }

        @media (max-width: 768px) {
            .controls-row {
                flex-direction: column;
                align-items: stretch;
            }

            .search-box {
                min-width: auto;
            }

            .filter-group {
                justify-content: center;
            }

            .departments-grid {
                grid-template-columns: 1fr;
                padding: 20px;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 15px;
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1><i class="fas fa-building"></i> دليل الأقسام الإدارية</h1>
            <p>إدارة شاملة لجميع الأقسام والوحدات الإدارية في المؤسسة الحكومية</p>
        </div>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon" style="color: #3498db;">
                    <i class="fas fa-building"></i>
                </div>
                <div class="stat-number" style="color: #3498db;">12</div>
                <div class="stat-label">إجمالي الأقسام</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon" style="color: #27ae60;">
                    <i class="fas fa-users"></i>
                </div>
                <div class="stat-number" style="color: #27ae60;">248</div>
                <div class="stat-label">إجمالي الموظفين</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon" style="color: #f39c12;">
                    <i class="fas fa-user-tie"></i>
                </div>
                <div class="stat-number" style="color: #f39c12;">12</div>
                <div class="stat-label">رؤساء الأقسام</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon" style="color: #e74c3c;">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="stat-number" style="color: #e74c3c;">95%</div>
                <div class="stat-label">معدل الكفاءة</div>
            </div>
        </div>

        <!-- Controls -->
        <div class="controls">
            <div class="controls-row">
                <div class="search-box">
                    <input type="text" id="searchInput" placeholder="البحث في الأقسام..." onkeyup="searchDepartments()">
                    <i class="fas fa-search"></i>
                </div>
                <div class="filter-group">
                    <select class="filter-select" id="typeFilter" onchange="filterDepartments()">
                        <option value="">جميع الأنواع</option>
                        <option value="إداري">إداري</option>
                        <option value="فني">فني</option>
                        <option value="طبي">طبي</option>
                        <option value="مالي">مالي</option>
                    </select>
                    <select class="filter-select" id="statusFilter" onchange="filterDepartments()">
                        <option value="">جميع الحالات</option>
                        <option value="نشط">نشط</option>
                        <option value="معطل">معطل</option>
                        <option value="قيد المراجعة">قيد المراجعة</option>
                    </select>
                    <button class="btn btn-success" onclick="showAddDepartmentModal()">
                        <i class="fas fa-plus"></i> إضافة قسم جديد
                    </button>
                    <button class="btn btn-primary" onclick="exportDepartments()">
                        <i class="fas fa-file-excel"></i> تصدير
                    </button>
                    <button class="btn btn-warning" onclick="printDepartments()">
                        <i class="fas fa-print"></i> طباعة
                    </button>
                </div>
            </div>
        </div>

        <!-- Departments Grid -->
        <div class="departments-grid" id="departmentsGrid">
            <!-- Department cards will be generated here -->
        </div>

        <!-- Pagination -->
        <div class="pagination">
            <button class="page-btn" onclick="changePage('prev')">
                <i class="fas fa-chevron-right"></i>
            </button>
            <button class="page-btn active" onclick="changePage(1)">1</button>
            <button class="page-btn" onclick="changePage(2)">2</button>
            <button class="page-btn" onclick="changePage(3)">3</button>
            <button class="page-btn" onclick="changePage('next')">
                <i class="fas fa-chevron-left"></i>
            </button>
        </div>
    </div>

    <!-- Add/Edit Department Modal -->
    <div class="modal" id="departmentModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title" id="modalTitle">
                    <i class="fas fa-plus-circle"></i>
                    إضافة قسم جديد
                </h2>
                <button class="close-btn" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <form id="departmentForm">
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label">رمز القسم</label>
                        <input type="text" class="form-input" id="deptCode" placeholder="مثال: DEPT001" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">اسم القسم</label>
                        <input type="text" class="form-input" id="deptName" placeholder="مثال: قسم المحاسبة" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">نوع القسم</label>
                        <select class="form-input" id="deptType" required>
                            <option value="">اختر النوع</option>
                            <option value="إداري">إداري</option>
                            <option value="فني">فني</option>
                            <option value="طبي">طبي</option>
                            <option value="مالي">مالي</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">رئيس القسم</label>
                        <input type="text" class="form-input" id="deptHead" placeholder="اسم رئيس القسم">
                    </div>
                    <div class="form-group">
                        <label class="form-label">الهاتف</label>
                        <input type="tel" class="form-input" id="deptPhone" placeholder="07XX XXX XXXX">
                    </div>
                    <div class="form-group">
                        <label class="form-label">البريد الإلكتروني</label>
                        <input type="email" class="form-input" id="deptEmail" placeholder="<EMAIL>">
                    </div>
                    <div class="form-group">
                        <label class="form-label">الموقع</label>
                        <input type="text" class="form-input" id="deptLocation" placeholder="الطابق الأول - الجناح الشرقي">
                    </div>
                    <div class="form-group">
                        <label class="form-label">الحالة</label>
                        <select class="form-input" id="deptStatus" required>
                            <option value="نشط">نشط</option>
                            <option value="معطل">معطل</option>
                            <option value="قيد المراجعة">قيد المراجعة</option>
                        </select>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">وصف القسم</label>
                    <textarea class="form-input form-textarea" id="deptDescription" placeholder="وصف مختصر عن القسم ومهامه..."></textarea>
                </div>

                <div class="form-group">
                    <label class="form-label">المهام والمسؤوليات</label>
                    <textarea class="form-input form-textarea" id="deptResponsibilities" placeholder="قائمة بالمهام والمسؤوليات الرئيسية..."></textarea>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save"></i> حفظ البيانات
                    </button>
                    <button type="button" class="btn btn-danger" onclick="closeModal()">
                        <i class="fas fa-times"></i> إلغاء
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Department Details Modal -->
    <div class="modal" id="detailsModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">
                    <i class="fas fa-info-circle"></i>
                    تفاصيل القسم
                </h2>
                <button class="close-btn" onclick="closeDetailsModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div id="departmentDetails">
                <!-- Details will be populated here -->
            </div>
        </div>
    </div>

    <script>
        // بيانات الأقسام التجريبية
        const departmentsData = [
            {
                code: 'DEPT001',
                name: 'قسم المحاسبة والمالية',
                type: 'مالي',
                head: 'د. أحمد محمد علي',
                phone: '07701234567',
                email: '<EMAIL>',
                location: 'الطابق الثاني - الجناح الشرقي',
                status: 'نشط',
                employees: 25,
                description: 'قسم متخصص في إدارة الشؤون المالية والمحاسبية للمؤسسة',
                responsibilities: 'إعداد الميزانيات، مراجعة الحسابات، إدارة الرواتب، التدقيق المالي',
                budget: '2,500,000,000',
                established: '2010-01-15'
            },
            {
                code: 'DEPT002',
                name: 'قسم الموارد البشرية',
                type: 'إداري',
                head: 'أ. فاطمة حسن الكريم',
                phone: '07702345678',
                email: '<EMAIL>',
                location: 'الطابق الأول - الجناح الغربي',
                status: 'نشط',
                employees: 18,
                description: 'قسم مختص بإدارة شؤون الموظفين والتطوير المهني',
                responsibilities: 'التوظيف، التدريب، تقييم الأداء، إدارة الإجازات',
                budget: '1,800,000,000',
                established: '2010-03-20'
            },
            {
                code: 'DEPT003',
                name: 'قسم تكنولوجيا المعلومات',
                type: 'فني',
                head: 'م. علي أحمد حسين',
                phone: '07703456789',
                email: '<EMAIL>',
                location: 'الطابق الثالث - الجناح الشمالي',
                status: 'نشط',
                employees: 15,
                description: 'قسم متخصص في إدارة وتطوير الأنظمة التقنية',
                responsibilities: 'صيانة الأنظمة، تطوير البرمجيات، الأمن السيبراني، الدعم التقني',
                budget: '3,200,000,000',
                established: '2012-06-10'
            },
            {
                code: 'DEPT004',
                name: 'قسم الشؤون الإدارية',
                type: 'إداري',
                head: 'أ. زينب محمد علي',
                phone: '07704567890',
                email: '<EMAIL>',
                location: 'الطابق الأول - الجناح الشرقي',
                status: 'نشط',
                employees: 22,
                description: 'قسم مسؤول عن الشؤون الإدارية العامة والخدمات',
                responsibilities: 'إدارة المراسلات، الأرشفة، الخدمات العامة، الصيانة',
                budget: '1,500,000,000',
                established: '2010-01-15'
            },
            {
                code: 'DEPT005',
                name: 'قسم الشؤون القانونية',
                type: 'إداري',
                head: 'المحامي حسام عبدالله',
                phone: '07705678901',
                email: '<EMAIL>',
                location: 'الطابق الثاني - الجناح الغربي',
                status: 'نشط',
                employees: 8,
                description: 'قسم متخصص في الشؤون القانونية والاستشارات',
                responsibilities: 'الاستشارات القانونية، صياغة العقود، المتابعة القضائية',
                budget: '800,000,000',
                established: '2011-09-05'
            },
            {
                code: 'DEPT006',
                name: 'قسم التخطيط والمتابعة',
                type: 'إداري',
                head: 'د. سارة أحمد محمد',
                phone: '07706789012',
                email: '<EMAIL>',
                location: 'الطابق الثالث - الجناح الشرقي',
                status: 'نشط',
                employees: 12,
                description: 'قسم مختص بالتخطيط الاستراتيجي ومتابعة التنفيذ',
                responsibilities: 'وضع الخطط، متابعة التنفيذ، إعداد التقارير، التقييم',
                budget: '1,200,000,000',
                established: '2013-02-18'
            },
            {
                code: 'DEPT007',
                name: 'قسم الشؤون الطبية',
                type: 'طبي',
                head: 'د. محمد علي حسن',
                phone: '07707890123',
                email: '<EMAIL>',
                location: 'الطابق الأرضي - الجناح الطبي',
                status: 'نشط',
                employees: 35,
                description: 'قسم مختص بالخدمات الطبية والصحية للموظفين',
                responsibilities: 'الفحوصات الطبية، العلاج، الطوارئ، الوقاية',
                budget: '2,800,000,000',
                established: '2010-05-12'
            },
            {
                code: 'DEPT008',
                name: 'قسم العلاقات العامة',
                type: 'إداري',
                head: 'أ. نور الهدى محمد',
                phone: '07708901234',
                email: '<EMAIL>',
                location: 'الطابق الأول - الجناح الرئيسي',
                status: 'نشط',
                employees: 10,
                description: 'قسم مختص بالعلاقات العامة والإعلام',
                responsibilities: 'العلاقات الإعلامية، التواصل الجماهيري، تنظيم الفعاليات',
                budget: '900,000,000',
                established: '2014-11-08'
            },
            {
                code: 'DEPT009',
                name: 'قسم الأمن والحماية',
                type: 'فني',
                head: 'العقيد أحمد سالم',
                phone: '07709012345',
                email: '<EMAIL>',
                location: 'جميع أنحاء المبنى',
                status: 'نشط',
                employees: 28,
                description: 'قسم مسؤول عن أمن وحماية المؤسسة والموظفين',
                responsibilities: 'الحراسة، مراقبة الدخول والخروج، الأمن الداخلي، الطوارئ',
                budget: '1,600,000,000',
                established: '2010-01-15'
            },
            {
                code: 'DEPT010',
                name: 'قسم المشتريات والمخازن',
                type: 'إداري',
                head: 'أ. خالد محمد علي',
                phone: '07700123456',
                email: '<EMAIL>',
                location: 'الطابق الأرضي - الجناح الخلفي',
                status: 'نشط',
                employees: 16,
                description: 'قسم مختص بالمشتريات وإدارة المخازن',
                responsibilities: 'المناقصات، الشراء، إدارة المخزون، التوزيع',
                budget: '1,400,000,000',
                established: '2010-08-22'
            },
            {
                code: 'DEPT011',
                name: 'قسم التدقيق الداخلي',
                type: 'مالي',
                head: 'أ. رائد عبدالرحمن',
                phone: '07701234560',
                email: '<EMAIL>',
                location: 'الطابق الثالث - الجناح الغربي',
                status: 'نشط',
                employees: 9,
                description: 'قسم مختص بالتدقيق الداخلي ومراقبة الجودة',
                responsibilities: 'التدقيق المالي، مراجعة العمليات، ضمان الجودة، التقييم',
                budget: '700,000,000',
                established: '2015-04-30'
            },
            {
                code: 'DEPT012',
                name: 'قسم البحث والتطوير',
                type: 'فني',
                head: 'د. ليلى أحمد حسين',
                phone: '07702345601',
                email: '<EMAIL>',
                location: 'الطابق الرابع - مختبرات البحث',
                status: 'قيد المراجعة',
                employees: 14,
                description: 'قسم مختص بالبحث العلمي والتطوير التقني',
                responsibilities: 'البحوث التطبيقية، تطوير الحلول، الابتكار، الدراسات',
                budget: '2,100,000,000',
                established: '2018-01-10'
            }
        ];

        let currentPage = 1;
        let itemsPerPage = 6;
        let filteredDepartments = [...departmentsData];
        let editingDepartment = null;

        // تحميل الأقسام عند بدء الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            displayDepartments();
            updateStatistics();
        });

        // عرض الأقسام
        function displayDepartments() {
            const grid = document.getElementById('departmentsGrid');
            const startIndex = (currentPage - 1) * itemsPerPage;
            const endIndex = startIndex + itemsPerPage;
            const departmentsToShow = filteredDepartments.slice(startIndex, endIndex);

            grid.innerHTML = departmentsToShow.map(dept => `
                <div class="department-card">
                    <div class="department-header">
                        <div class="department-info">
                            <h3>${dept.name}</h3>
                            <div class="department-code">${dept.code}</div>
                        </div>
                    </div>

                    <div class="department-details">
                        <div class="detail-row">
                            <span class="detail-label">النوع:</span>
                            <span class="detail-value">${dept.type}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">رئيس القسم:</span>
                            <span class="detail-value">${dept.head}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">عدد الموظفين:</span>
                            <span class="detail-value">${dept.employees} موظف</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">الموقع:</span>
                            <span class="detail-value">${dept.location}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">الحالة:</span>
                            <span class="detail-value">
                                <span style="background: ${getStatusColor(dept.status)}; color: white; padding: 3px 8px; border-radius: 10px; font-size: 0.8rem;">
                                    ${dept.status}
                                </span>
                            </span>
                        </div>
                    </div>

                    <div class="department-actions">
                        <button class="action-btn" style="background: #007bff; color: white;" onclick="viewDepartmentDetails('${dept.code}')">
                            <i class="fas fa-eye"></i> عرض
                        </button>
                        <button class="action-btn" style="background: #ffc107; color: #333;" onclick="editDepartment('${dept.code}')">
                            <i class="fas fa-edit"></i> تعديل
                        </button>
                        <button class="action-btn" style="background: #28a745; color: white;" onclick="viewEmployees('${dept.code}')">
                            <i class="fas fa-users"></i> الموظفين
                        </button>
                        <button class="action-btn" style="background: #dc3545; color: white;" onclick="deleteDepartment('${dept.code}')">
                            <i class="fas fa-trash"></i> حذف
                        </button>
                    </div>
                </div>
            `).join('');

            updatePagination();
        }

        // تحديث الإحصائيات
        function updateStatistics() {
            const totalDepts = departmentsData.length;
            const totalEmployees = departmentsData.reduce((sum, dept) => sum + dept.employees, 0);
            const activeHeads = departmentsData.filter(dept => dept.status === 'نشط').length;

            document.querySelector('.stat-card:nth-child(1) .stat-number').textContent = totalDepts;
            document.querySelector('.stat-card:nth-child(2) .stat-number').textContent = totalEmployees;
            document.querySelector('.stat-card:nth-child(3) .stat-number').textContent = activeHeads;
        }

        // الحصول على لون الحالة
        function getStatusColor(status) {
            switch(status) {
                case 'نشط': return '#28a745';
                case 'معطل': return '#dc3545';
                case 'قيد المراجعة': return '#ffc107';
                default: return '#6c757d';
            }
        }

        // البحث في الأقسام
        function searchDepartments() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const typeFilter = document.getElementById('typeFilter').value;
            const statusFilter = document.getElementById('statusFilter').value;

            filteredDepartments = departmentsData.filter(dept => {
                const matchesSearch = dept.name.toLowerCase().includes(searchTerm) ||
                                    dept.code.toLowerCase().includes(searchTerm) ||
                                    dept.head.toLowerCase().includes(searchTerm);
                const matchesType = !typeFilter || dept.type === typeFilter;
                const matchesStatus = !statusFilter || dept.status === statusFilter;

                return matchesSearch && matchesType && matchesStatus;
            });

            currentPage = 1;
            displayDepartments();
        }

        // فلترة الأقسام
        function filterDepartments() {
            searchDepartments();
        }

        // تحديث الترقيم
        function updatePagination() {
            const totalPages = Math.ceil(filteredDepartments.length / itemsPerPage);
            const pagination = document.querySelector('.pagination');

            pagination.innerHTML = `
                <button class="page-btn" onclick="changePage('prev')" ${currentPage === 1 ? 'disabled' : ''}>
                    <i class="fas fa-chevron-right"></i>
                </button>
                ${Array.from({length: totalPages}, (_, i) => i + 1).map(page => `
                    <button class="page-btn ${page === currentPage ? 'active' : ''}" onclick="changePage(${page})">
                        ${page}
                    </button>
                `).join('')}
                <button class="page-btn" onclick="changePage('next')" ${currentPage === totalPages ? 'disabled' : ''}>
                    <i class="fas fa-chevron-left"></i>
                </button>
            `;
        }

        // تغيير الصفحة
        function changePage(page) {
            const totalPages = Math.ceil(filteredDepartments.length / itemsPerPage);

            if (page === 'prev' && currentPage > 1) {
                currentPage--;
            } else if (page === 'next' && currentPage < totalPages) {
                currentPage++;
            } else if (typeof page === 'number' && page >= 1 && page <= totalPages) {
                currentPage = page;
            }

            displayDepartments();
        }

        // عرض نافذة إضافة قسم
        function showAddDepartmentModal() {
            editingDepartment = null;
            document.getElementById('modalTitle').innerHTML = '<i class="fas fa-plus-circle"></i> إضافة قسم جديد';
            document.getElementById('departmentForm').reset();
            document.getElementById('departmentModal').classList.add('active');
        }

        // تعديل قسم
        function editDepartment(code) {
            const dept = departmentsData.find(d => d.code === code);
            if (!dept) return;

            editingDepartment = dept;
            document.getElementById('modalTitle').innerHTML = '<i class="fas fa-edit"></i> تعديل القسم';

            // ملء النموذج بالبيانات
            document.getElementById('deptCode').value = dept.code;
            document.getElementById('deptName').value = dept.name;
            document.getElementById('deptType').value = dept.type;
            document.getElementById('deptHead').value = dept.head;
            document.getElementById('deptPhone').value = dept.phone;
            document.getElementById('deptEmail').value = dept.email;
            document.getElementById('deptLocation').value = dept.location;
            document.getElementById('deptStatus').value = dept.status;
            document.getElementById('deptDescription').value = dept.description;
            document.getElementById('deptResponsibilities').value = dept.responsibilities;

            document.getElementById('departmentModal').classList.add('active');
        }

        // حذف قسم
        function deleteDepartment(code) {
            const dept = departmentsData.find(d => d.code === code);
            if (!dept) return;

            if (confirm(`هل أنت متأكد من حذف القسم "${dept.name}"؟\n\nسيتم حذف جميع البيانات المرتبطة بهذا القسم نهائياً.`)) {
                const index = departmentsData.findIndex(d => d.code === code);
                departmentsData.splice(index, 1);
                filteredDepartments = [...departmentsData];
                displayDepartments();
                updateStatistics();

                // رسالة تأكيد
                showSuccessMessage('تم حذف القسم بنجاح!');
            }
        }

        // عرض تفاصيل القسم
        function viewDepartmentDetails(code) {
            const dept = departmentsData.find(d => d.code === code);
            if (!dept) return;

            const detailsHTML = `
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 25px; margin-bottom: 25px;">
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px;">
                        <h4 style="color: #2c3e50; margin-bottom: 15px; display: flex; align-items: center;">
                            <i class="fas fa-info-circle" style="margin-left: 10px; color: #3498db;"></i>
                            المعلومات الأساسية
                        </h4>
                        <div style="display: grid; gap: 10px;">
                            <div><strong>رمز القسم:</strong> ${dept.code}</div>
                            <div><strong>اسم القسم:</strong> ${dept.name}</div>
                            <div><strong>نوع القسم:</strong> ${dept.type}</div>
                            <div><strong>تاريخ التأسيس:</strong> ${dept.established}</div>
                            <div><strong>الحالة:</strong>
                                <span style="background: ${getStatusColor(dept.status)}; color: white; padding: 3px 8px; border-radius: 10px; font-size: 0.8rem;">
                                    ${dept.status}
                                </span>
                            </div>
                        </div>
                    </div>

                    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px;">
                        <h4 style="color: #2c3e50; margin-bottom: 15px; display: flex; align-items: center;">
                            <i class="fas fa-user-tie" style="margin-left: 10px; color: #e67e22;"></i>
                            معلومات الإدارة
                        </h4>
                        <div style="display: grid; gap: 10px;">
                            <div><strong>رئيس القسم:</strong> ${dept.head}</div>
                            <div><strong>الهاتف:</strong> ${dept.phone}</div>
                            <div><strong>البريد الإلكتروني:</strong> ${dept.email}</div>
                            <div><strong>الموقع:</strong> ${dept.location}</div>
                            <div><strong>عدد الموظفين:</strong> ${dept.employees} موظف</div>
                        </div>
                    </div>
                </div>

                <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 25px;">
                    <h4 style="color: #2c3e50; margin-bottom: 15px; display: flex; align-items: center;">
                        <i class="fas fa-file-alt" style="margin-left: 10px; color: #27ae60;"></i>
                        وصف القسم
                    </h4>
                    <p style="line-height: 1.6; color: #495057;">${dept.description}</p>
                </div>

                <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 25px;">
                    <h4 style="color: #2c3e50; margin-bottom: 15px; display: flex; align-items: center;">
                        <i class="fas fa-tasks" style="margin-left: 10px; color: #8e44ad;"></i>
                        المهام والمسؤوليات
                    </h4>
                    <p style="line-height: 1.6; color: #495057;">${dept.responsibilities}</p>
                </div>

                <div style="background: #e3f2fd; padding: 20px; border-radius: 10px; border-right: 4px solid #2196f3;">
                    <h4 style="color: #1976d2; margin-bottom: 15px; display: flex; align-items: center;">
                        <i class="fas fa-chart-bar" style="margin-left: 10px;"></i>
                        معلومات مالية
                    </h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                        <div style="text-align: center;">
                            <div style="font-size: 1.5rem; font-weight: bold; color: #1976d2;">${dept.budget}</div>
                            <div style="color: #666; font-size: 0.9rem;">الميزانية السنوية (دينار عراقي)</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 1.5rem; font-weight: bold; color: #1976d2;">${Math.round(parseInt(dept.budget.replace(/,/g, '')) / dept.employees).toLocaleString()}</div>
                            <div style="color: #666; font-size: 0.9rem;">متوسط التكلفة لكل موظف</div>
                        </div>
                    </div>
                </div>

                <div style="display: flex; gap: 15px; justify-content: center; margin-top: 25px; padding-top: 25px; border-top: 1px solid #e9ecef;">
                    <button class="btn btn-warning" onclick="editDepartment('${dept.code}'); closeDetailsModal();">
                        <i class="fas fa-edit"></i> تعديل القسم
                    </button>
                    <button class="btn btn-success" onclick="viewEmployees('${dept.code}')">
                        <i class="fas fa-users"></i> عرض الموظفين
                    </button>
                    <button class="btn btn-primary" onclick="printDepartmentReport('${dept.code}')">
                        <i class="fas fa-print"></i> طباعة التقرير
                    </button>
                </div>
            `;

            document.getElementById('departmentDetails').innerHTML = detailsHTML;
            document.getElementById('detailsModal').classList.add('active');
        }

        // عرض موظفي القسم
        function viewEmployees(code) {
            const dept = departmentsData.find(d => d.code === code);
            if (!dept) return;

            alert(`📋 عرض موظفي ${dept.name}\n\nسيتم فتح صفحة جديدة تحتوي على:\n• قائمة بجميع موظفي القسم (${dept.employees} موظف)\n• تفاصيل كل موظف\n• إمكانية إضافة وتعديل الموظفين\n• تقارير الأداء والحضور\n\nرئيس القسم: ${dept.head}`);
        }

        // طباعة تقرير القسم
        function printDepartmentReport(code) {
            const dept = departmentsData.find(d => d.code === code);
            if (!dept) return;

            alert(`🖨️ طباعة تقرير ${dept.name}\n\nسيتم إعداد تقرير شامل يحتوي على:\n• معلومات القسم الكاملة\n• قائمة الموظفين\n• الميزانية والمصروفات\n• إحصائيات الأداء\n• الهيكل التنظيمي\n\nعدد الصفحات المتوقع: 8-12 صفحة`);
        }

        // حفظ بيانات القسم
        document.getElementById('departmentForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = {
                code: document.getElementById('deptCode').value,
                name: document.getElementById('deptName').value,
                type: document.getElementById('deptType').value,
                head: document.getElementById('deptHead').value,
                phone: document.getElementById('deptPhone').value,
                email: document.getElementById('deptEmail').value,
                location: document.getElementById('deptLocation').value,
                status: document.getElementById('deptStatus').value,
                description: document.getElementById('deptDescription').value,
                responsibilities: document.getElementById('deptResponsibilities').value
            };

            if (editingDepartment) {
                // تعديل قسم موجود
                const index = departmentsData.findIndex(d => d.code === editingDepartment.code);
                departmentsData[index] = {
                    ...departmentsData[index],
                    ...formData,
                    employees: departmentsData[index].employees, // الحفاظ على عدد الموظفين
                    budget: departmentsData[index].budget, // الحفاظ على الميزانية
                    established: departmentsData[index].established // الحفاظ على تاريخ التأسيس
                };
                showSuccessMessage('تم تحديث بيانات القسم بنجاح!');
            } else {
                // إضافة قسم جديد
                const newDept = {
                    ...formData,
                    employees: 0,
                    budget: '0',
                    established: new Date().toISOString().split('T')[0]
                };
                departmentsData.push(newDept);
                showSuccessMessage('تم إضافة القسم الجديد بنجاح!');
            }

            filteredDepartments = [...departmentsData];
            displayDepartments();
            updateStatistics();
            closeModal();
        });

        // إغلاق النوافذ
        function closeModal() {
            document.getElementById('departmentModal').classList.remove('active');
        }

        function closeDetailsModal() {
            document.getElementById('detailsModal').classList.remove('active');
        }

        // إغلاق النوافذ بالنقر خارجها
        document.getElementById('departmentModal').addEventListener('click', function(e) {
            if (e.target === this) closeModal();
        });

        document.getElementById('detailsModal').addEventListener('click', function(e) {
            if (e.target === this) closeDetailsModal();
        });

        // إغلاق النوافذ بمفتاح Escape
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeModal();
                closeDetailsModal();
            }
        });

        // عرض رسالة نجاح
        function showSuccessMessage(message) {
            const successDiv = document.createElement('div');
            successDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #28a745;
                color: white;
                padding: 15px 25px;
                border-radius: 8px;
                box-shadow: 0 5px 15px rgba(0,0,0,0.2);
                z-index: 10000;
                font-family: 'Cairo', sans-serif;
                font-weight: 600;
                display: flex;
                align-items: center;
                gap: 10px;
                animation: slideIn 0.3s ease;
            `;

            successDiv.innerHTML = `<i class="fas fa-check-circle"></i> ${message}`;
            document.body.appendChild(successDiv);

            setTimeout(() => {
                successDiv.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => successDiv.remove(), 300);
            }, 3000);
        }

        // تصدير البيانات
        function exportDepartments() {
            alert(`📊 تصدير بيانات الأقسام\n\nسيتم إنشاء ملف Excel يحتوي على:\n• جميع بيانات الأقسام (${departmentsData.length} قسم)\n• معلومات رؤساء الأقسام\n• إحصائيات الموظفين\n• الميزانيات والمصروفات\n• مخططات بيانية\n\nحجم الملف المتوقع: 2.5 MB`);
        }

        // طباعة التقرير العام
        function printDepartments() {
            alert(`🖨️ طباعة دليل الأقسام\n\nسيتم إعداد تقرير شامل يحتوي على:\n• دليل كامل لجميع الأقسام\n• الهيكل التنظيمي\n• إحصائيات عامة\n• معلومات الاتصال\n• فهرس أبجدي\n\nعدد الصفحات المتوقع: 25-30 صفحة`);
        }

        // إضافة أنيميشن CSS
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }

            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }

            .page-btn:disabled {
                opacity: 0.5;
                cursor: not-allowed;
            }

            .page-btn:disabled:hover {
                transform: none;
                background: white;
                color: #6c757d;
            }
        `;
        document.head.appendChild(style);

        console.log('✅ نظام إدارة الأقسام محمل بنجاح');
        console.log('🏢 12 قسم متاح للإدارة');
        console.log('👥 248 موظف في جميع الأقسام');
        console.log('🔍 البحث والفلترة متاحة');
        console.log('📊 التقارير والإحصائيات جاهزة');
    </script>
</body>
</html>