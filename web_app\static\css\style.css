/* نظام المحاسبة المتكامل - الأنماط المخصصة */

/* الخطوط والنصوص */
body {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: rtl;
    text-align: right;
}

/* الألوان الأساسية */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    
    --primary-gradient: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    --success-gradient: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    --danger-gradient: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
}

/* التخطيط العام */
.container-fluid {
    padding-right: 1.5rem;
    padding-left: 1.5rem;
}

/* شريط التنقل */
.navbar {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.25rem;
}

.nav-link {
    font-weight: 500;
    transition: all 0.3s ease;
}

.nav-link:hover {
    transform: translateY(-1px);
}

/* البطاقات */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.card-header {
    background: var(--primary-gradient);
    color: white;
    border-bottom: none;
    border-radius: 10px 10px 0 0 !important;
    font-weight: 600;
}

/* الأزرار */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.btn-primary {
    background: var(--primary-gradient);
}

.btn-success {
    background: var(--success-gradient);
}

.btn-danger {
    background: var(--danger-gradient);
}

/* الجداول */
.table {
    border-radius: 8px;
    overflow: hidden;
}

.table thead th {
    background: var(--primary-color);
    color: white;
    border: none;
    font-weight: 600;
    text-align: center;
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.1);
    transform: scale(1.01);
}

.table td {
    vertical-align: middle;
    text-align: center;
}

/* النماذج */
.form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
    text-align: right;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

/* التنبيهات */
.alert {
    border: none;
    border-radius: 8px;
    font-weight: 500;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

/* الشارات */
.badge {
    border-radius: 6px;
    font-weight: 500;
}

/* التحميل */
.spinner-border {
    width: 1.5rem;
    height: 1.5rem;
}

/* الأيقونات */
.fas, .far {
    margin-left: 0.5rem;
}

/* الفواصل */
.dropdown-divider {
    margin: 0.5rem 0;
}

/* التذييل */
footer {
    margin-top: auto;
    background: var(--light-color) !important;
    border-top: 1px solid #dee2e6;
}

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .container-fluid {
        padding-right: 1rem;
        padding-left: 1rem;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    .table-responsive {
        border-radius: 8px;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}

/* أنماط مخصصة للوحة التحكم */
.dashboard-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid rgba(0,0,0,0.05);
}

.stat-card {
    text-align: center;
    padding: 1.5rem;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
}

.stat-label {
    font-size: 0.9rem;
    color: var(--secondary-color);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* أنماط الحالة */
.status-active {
    color: var(--success-color);
}

.status-inactive {
    color: var(--danger-color);
}

.status-pending {
    color: var(--warning-color);
}

/* أنماط الطباعة */
@media print {
    .navbar,
    .btn,
    footer {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #dee2e6;
    }
    
    body {
        font-size: 12pt;
    }
    
    .table {
        font-size: 10pt;
    }
}

/* تحسينات إضافية */
.text-gradient {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-custom {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.border-radius-custom {
    border-radius: 15px !important;
}

/* تأثيرات الحركة */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

/* تحسينات الوصولية */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* تخصيص شريط التمرير */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #0056b3;
}
