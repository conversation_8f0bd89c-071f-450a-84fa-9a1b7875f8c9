<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التخصصات - نظام المحاسبة المتكامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: #f8f9fa;
        }
        
        .navbar {
            background: linear-gradient(135deg, #6c757d 0%, #545b62 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .card {
            border: none;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
            border-radius: 12px;
        }
        
        .table th {
            background: #343a40;
            color: white;
            text-align: center;
            border: none;
        }
        
        .table td {
            vertical-align: middle;
            text-align: center;
        }
        
        .spec-category {
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .cat-medical {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
        }
        
        .cat-engineering {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
        }
        
        .cat-business {
            background: linear-gradient(45deg, #f39c12, #e67e22);
            color: white;
        }
        
        .cat-science {
            background: linear-gradient(45deg, #27ae60, #229954);
            color: white;
        }
        
        .cat-arts {
            background: linear-gradient(45deg, #9b59b6, #8e44ad);
            color: white;
        }
        
        .cat-law {
            background: linear-gradient(45deg, #34495e, #2c3e50);
            color: white;
        }
        
        .cat-education {
            background: linear-gradient(45deg, #16a085, #138d75);
            color: white;
        }
        
        .search-box {
            border-radius: 25px;
            border: 2px solid #e9ecef;
            padding: 10px 20px;
        }
        
        .search-box:focus {
            border-color: #6c757d;
            box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.25);
        }
        
        .action-buttons .btn {
            margin: 2px;
        }
        
        .spec-stats {
            background: linear-gradient(135deg, #6c757d, #545b62);
            color: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="simple_homepage.html">
                <i class="fas fa-calculator me-2"></i>
                نظام المحاسبة المتكامل
            </a>
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="simple_homepage.html">
                    <i class="fas fa-home me-1"></i>الرئيسية
                </a>
                <a class="nav-link" href="employees_page.html">
                    <i class="fas fa-users me-1"></i>الموظفون
                </a>
                <a class="nav-link" href="basic_lists_page.html">
                    <i class="fas fa-list me-1"></i>القوائم الأساسية
                </a>
                <a class="nav-link active" href="specializations_list.html">
                    <i class="fas fa-microscope me-1"></i>التخصصات
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- عنوان الصفحة -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2 class="text-secondary">
                        <i class="fas fa-microscope me-2"></i>
                        التخصصات الأكاديمية والمهنية
                    </h2>
                    <div>
                        <button class="btn btn-secondary me-2" onclick="showAddSpecModal()">
                            <i class="fas fa-plus me-1"></i>إضافة تخصص
                        </button>
                        <a href="basic_lists_page.html" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right me-1"></i>العودة للقوائم
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات التخصصات -->
        <div class="spec-stats">
            <div class="row text-center">
                <div class="col-md-2">
                    <h3><i class="fas fa-stethoscope me-2"></i>8</h3>
                    <p class="mb-0">طبية</p>
                </div>
                <div class="col-md-2">
                    <h3><i class="fas fa-cogs me-2"></i>6</h3>
                    <p class="mb-0">هندسية</p>
                </div>
                <div class="col-md-2">
                    <h3><i class="fas fa-chart-line me-2"></i>7</h3>
                    <p class="mb-0">إدارية</p>
                </div>
                <div class="col-md-2">
                    <h3><i class="fas fa-flask me-2"></i>5</h3>
                    <p class="mb-0">علمية</p>
                </div>
                <div class="col-md-2">
                    <h3><i class="fas fa-palette me-2"></i>4</h3>
                    <p class="mb-0">إنسانية</p>
                </div>
                <div class="col-md-2">
                    <h3><i class="fas fa-balance-scale me-2"></i>5</h3>
                    <p class="mb-0">قانونية وتربوية</p>
                </div>
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h4>35</h4>
                        <small>إجمالي التخصصات</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h4>125</h4>
                        <small>إجمالي المتخصصين</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h4>7</h4>
                        <small>فئات رئيسية</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-dark">
                    <div class="card-body text-center">
                        <h4>92%</h4>
                        <small>نسبة التغطية</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- شريط البحث والفلترة -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-search"></i>
                                    </span>
                                    <input type="text" class="form-control search-box" placeholder="البحث عن تخصص..." id="searchInput">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="categoryFilter">
                                    <option value="">جميع الفئات</option>
                                    <option value="طبية">طبية</option>
                                    <option value="هندسية">هندسية</option>
                                    <option value="إدارية">إدارية</option>
                                    <option value="علمية">علمية</option>
                                    <option value="إنسانية">إنسانية</option>
                                    <option value="قانونية">قانونية</option>
                                    <option value="تربوية">تربوية</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="levelFilter">
                                    <option value="">جميع المستويات</option>
                                    <option value="دكتوراه">دكتوراه</option>
                                    <option value="ماجستير">ماجستير</option>
                                    <option value="بكالوريوس">بكالوريوس</option>
                                    <option value="دبلوم">دبلوم</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button class="btn btn-outline-secondary w-100" onclick="clearFilters()">
                                    <i class="fas fa-times me-1"></i>مسح
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- جدول التخصصات -->
        <div class="card">
            <div class="card-header bg-secondary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>قائمة التخصصات
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0" id="specializationsTable">
                        <thead>
                            <tr>
                                <th>الرمز</th>
                                <th>اسم التخصص</th>
                                <th>الفئة</th>
                                <th>المستوى المطلوب</th>
                                <th>الوصف</th>
                                <th>عدد المتخصصين</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="specializationsTableBody">
                            <tr>
                                <td><strong>MED-001</strong></td>
                                <td>طب باطني</td>
                                <td><span class="spec-category cat-medical">طبية</span></td>
                                <td><span class="badge bg-info">دكتوراه</span></td>
                                <td>تخصص في الأمراض الباطنية</td>
                                <td><span class="badge bg-success">3</span></td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewSpec('MED-001')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editSpec('MED-001')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="المتخصصين" onclick="viewSpecialists('MED-001')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>MED-002</strong></td>
                                <td>جراحة عامة</td>
                                <td><span class="spec-category cat-medical">طبية</span></td>
                                <td><span class="badge bg-info">دكتوراه</span></td>
                                <td>تخصص في الجراحة العامة</td>
                                <td><span class="badge bg-success">2</span></td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewSpec('MED-002')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editSpec('MED-002')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="المتخصصين" onclick="viewSpecialists('MED-002')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>ENG-001</strong></td>
                                <td>هندسة مدنية</td>
                                <td><span class="spec-category cat-engineering">هندسية</span></td>
                                <td><span class="badge bg-info">بكالوريوس</span></td>
                                <td>تخصص في الهندسة المدنية</td>
                                <td><span class="badge bg-success">4</span></td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewSpec('ENG-001')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editSpec('ENG-001')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="المتخصصين" onclick="viewSpecialists('ENG-001')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>BUS-001</strong></td>
                                <td>محاسبة</td>
                                <td><span class="spec-category cat-business">إدارية</span></td>
                                <td><span class="badge bg-info">بكالوريوس</span></td>
                                <td>تخصص في المحاسبة والمالية</td>
                                <td><span class="badge bg-success">25</span></td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewSpec('BUS-001')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editSpec('BUS-001')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="المتخصصين" onclick="viewSpecialists('BUS-001')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>BUS-002</strong></td>
                                <td>إدارة أعمال</td>
                                <td><span class="spec-category cat-business">إدارية</span></td>
                                <td><span class="badge bg-info">بكالوريوس</span></td>
                                <td>تخصص في إدارة الأعمال</td>
                                <td><span class="badge bg-success">18</span></td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewSpec('BUS-002')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editSpec('BUS-002')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="المتخصصين" onclick="viewSpecialists('BUS-002')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>SCI-001</strong></td>
                                <td>علوم حاسوب</td>
                                <td><span class="spec-category cat-science">علمية</span></td>
                                <td><span class="badge bg-info">بكالوريوس</span></td>
                                <td>تخصص في علوم الحاسوب</td>
                                <td><span class="badge bg-success">8</span></td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewSpec('SCI-001')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editSpec('SCI-001')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="المتخصصين" onclick="viewSpecialists('SCI-001')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>LAW-001</strong></td>
                                <td>قانون</td>
                                <td><span class="spec-category cat-law">قانونية</span></td>
                                <td><span class="badge bg-info">بكالوريوس</span></td>
                                <td>تخصص في القانون</td>
                                <td><span class="badge bg-success">3</span></td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewSpec('LAW-001')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editSpec('LAW-001')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="المتخصصين" onclick="viewSpecialists('LAW-001')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>EDU-001</strong></td>
                                <td>تربية وتعليم</td>
                                <td><span class="spec-category cat-education">تربوية</span></td>
                                <td><span class="badge bg-info">بكالوريوس</span></td>
                                <td>تخصص في التربية والتعليم</td>
                                <td><span class="badge bg-success">12</span></td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewSpec('EDU-001')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editSpec('EDU-001')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="المتخصصين" onclick="viewSpecialists('EDU-001')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between align-items-center">
                    <span class="text-muted">عرض 8 من أصل 35 تخصص</span>
                    <div>
                        <button class="btn btn-outline-secondary btn-sm me-2" onclick="exportSpecializations()">
                            <i class="fas fa-file-excel me-1"></i>تصدير Excel
                        </button>
                        <button class="btn btn-outline-info btn-sm" onclick="printSpecializations()">
                            <i class="fas fa-print me-1"></i>طباعة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // وظائف البحث والفلترة
        function filterSpecializations() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const categoryFilter = document.getElementById('categoryFilter').value;
            const levelFilter = document.getElementById('levelFilter').value;
            
            const rows = document.querySelectorAll('#specializationsTableBody tr');
            
            rows.forEach(row => {
                const name = row.cells[1].textContent.toLowerCase();
                const category = row.cells[2].textContent;
                const level = row.cells[3].textContent;
                
                const matchesSearch = name.includes(searchTerm);
                const matchesCategory = !categoryFilter || category.includes(categoryFilter);
                const matchesLevel = !levelFilter || level.includes(levelFilter);
                
                if (matchesSearch && matchesCategory && matchesLevel) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }
        
        function clearFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('categoryFilter').value = '';
            document.getElementById('levelFilter').value = '';
            filterSpecializations();
        }
        
        // ربط أحداث البحث
        document.getElementById('searchInput').addEventListener('input', filterSpecializations);
        document.getElementById('categoryFilter').addEventListener('change', filterSpecializations);
        document.getElementById('levelFilter').addEventListener('change', filterSpecializations);
        
        // وظائف الإجراءات
        function viewSpec(code) {
            alert(`عرض تفاصيل التخصص: ${code}\n\nسيتم عرض:\n• الوصف التفصيلي\n• المناهج والمقررات\n• فرص العمل\n• الجامعات المعتمدة\n• إحصائيات المتخصصين`);
        }
        
        function editSpec(code) {
            alert(`تعديل التخصص: ${code}\n\nيمكن تعديل:\n• اسم التخصص\n• الفئة والتصنيف\n• المستوى المطلوب\n• الوصف والمتطلبات\n• الحالة`);
        }
        
        function viewSpecialists(code) {
            alert(`المتخصصين في: ${code}\n\nسيتم عرض:\n• قائمة المتخصصين\n• الجامعات المتخرجين منها\n• سنوات التخرج\n• التقديرات والدرجات\n• الخبرات العملية`);
        }
        
        function showAddSpecModal() {
            const modal = `
                <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999; display: flex; align-items: center; justify-content: center;" onclick="this.remove()">
                    <div style="background: white; border-radius: 12px; padding: 30px; max-width: 700px; width: 90%; max-height: 80%; overflow-y: auto; box-shadow: 0 10px 30px rgba(0,0,0,0.3);" onclick="event.stopPropagation()">
                        <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 20px; border-bottom: 2px solid #6f42c1; padding-bottom: 15px;">
                            <h3 style="color: #6f42c1; margin: 0; display: flex; align-items: center;">
                                <i class="fas fa-plus-circle" style="margin-left: 10px;"></i>
                                إضافة تخصص جديد
                            </h3>
                            <button onclick="closeModal(this)" style="background: #dc3545; color: white; border: none; border-radius: 50%; width: 30px; height: 30px; cursor: pointer; transition: all 0.3s ease; display: flex; align-items: center; justify-content: center;" onmouseover="this.style.background='#c82333'; this.style.transform='scale(1.1)'" onmouseout="this.style.background='#dc3545'; this.style.transform='scale(1)'" title="إغلاق النافذة">×</button>
                        </div>

                        <form id="addSpecForm" style="display: grid; gap: 15px;">
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                                <div>
                                    <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #495057;">رمز التخصص</label>
                                    <input type="text" id="specCode" placeholder="مثال: MED-005" style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'Cairo', sans-serif;" required>
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #495057;">اسم التخصص</label>
                                    <input type="text" id="specName" placeholder="مثال: طب الأطفال" style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'Cairo', sans-serif;" required>
                                </div>
                            </div>

                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                                <div>
                                    <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #495057;">الفئة</label>
                                    <select id="specCategory" style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'Cairo', sans-serif;" required>
                                        <option value="">اختر الفئة</option>
                                        <option value="طبية">طبية</option>
                                        <option value="هندسية">هندسية</option>
                                        <option value="إدارية">إدارية</option>
                                        <option value="علمية">علمية</option>
                                        <option value="إنسانية">إنسانية</option>
                                        <option value="قانونية">قانونية</option>
                                        <option value="تربوية">تربوية</option>
                                    </select>
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #495057;">المستوى المطلوب</label>
                                    <select id="specLevel" style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'Cairo', sans-serif;" required>
                                        <option value="">اختر المستوى</option>
                                        <option value="دكتوراه">دكتوراه</option>
                                        <option value="ماجستير">ماجستير</option>
                                        <option value="بكالوريوس">بكالوريوس</option>
                                        <option value="دبلوم">دبلوم</option>
                                    </select>
                                </div>
                            </div>

                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #495057;">الوصف التفصيلي</label>
                                <textarea id="specDescription" rows="3" placeholder="وصف التخصص ومجالات العمل..." style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'Cairo', sans-serif; resize: vertical;" required></textarea>
                            </div>

                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                                <div>
                                    <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #495057;">مدة الدراسة</label>
                                    <input type="text" id="specDuration" placeholder="مثال: 6 سنوات" style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'Cairo', sans-serif;">
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #495057;">الحالة</label>
                                    <select id="specStatus" style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'Cairo', sans-serif;" required>
                                        <option value="نشط">نشط</option>
                                        <option value="معطل">معطل</option>
                                    </select>
                                </div>
                            </div>

                            <div style="display: flex; gap: 10px; justify-content: center; margin-top: 20px;">
                                <button type="button" onclick="addNewSpecialization()" style="background: #6f42c1; color: white; border: none; padding: 12px 25px; border-radius: 8px; cursor: pointer; display: flex; align-items: center; gap: 5px;">
                                    <i class="fas fa-plus"></i> إضافة التخصص
                                </button>
                                <button type="button" onclick="closeModal(this)" style="background: #6c757d; color: white; border: none; padding: 12px 25px; border-radius: 8px; cursor: pointer; transition: all 0.3s ease; display: flex; align-items: center; gap: 5px;" onmouseover="this.style.background='#dc3545'; this.style.transform='translateY(-2px)'" onmouseout="this.style.background='#6c757d'; this.style.transform='translateY(0)'">
                                    <i class="fas fa-times"></i> إلغاء
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modal);
        }

        // وظيفة إضافة تخصص جديد
        function addNewSpecialization() {
            const code = document.getElementById('specCode').value;
            const name = document.getElementById('specName').value;
            const category = document.getElementById('specCategory').value;
            const level = document.getElementById('specLevel').value;
            const description = document.getElementById('specDescription').value;
            const duration = document.getElementById('specDuration').value || 'غير محدد';
            const status = document.getElementById('specStatus').value;

            // التحقق من صحة البيانات
            if (!code || !name || !category || !level || !description) {
                alert('⚠️ يرجى ملء جميع الحقول المطلوبة');
                return;
            }

            // التحقق من عدم تكرار الرمز
            const existingCodes = Array.from(document.querySelectorAll('#specializationsTableBody tr td:first-child')).map(td => td.textContent.trim());
            if (existingCodes.includes(code)) {
                alert('⚠️ رمز التخصص موجود مسبقاً. يرجى اختيار رمز آخر.');
                return;
            }

            // إنشاء صف جديد في الجدول
            const newRow = createSpecializationRow(code, name, category, level, duration, status);
            document.getElementById('specializationsTableBody').appendChild(newRow);

            // إغلاق النافذة
            const modal = document.querySelector('div[style*="position: fixed"]');
            if (modal) modal.remove();

            // رسالة تأكيد
            showSuccessMessage(`تم إضافة التخصص "${name}" بنجاح!`);
        }

        // إنشاء صف جديد في الجدول
        function createSpecializationRow(code, name, category, level, duration, status) {
            const row = document.createElement('tr');

            // تحديد فئة التصنيف
            const categoryClass = {
                'طبية': 'cat-medical',
                'هندسية': 'cat-engineering',
                'إدارية': 'cat-administrative',
                'علمية': 'cat-scientific',
                'إنسانية': 'cat-humanities',
                'قانونية': 'cat-legal',
                'تربوية': 'cat-educational'
            }[category] || 'cat-other';

            // تحديد لون المستوى
            const levelClass = {
                'دكتوراه': 'bg-danger',
                'ماجستير': 'bg-warning',
                'بكالوريوس': 'bg-primary',
                'دبلوم': 'bg-info'
            }[level] || 'bg-secondary';

            row.innerHTML = `
                <td><strong>${code}</strong></td>
                <td>${name}</td>
                <td><span class="category-badge ${categoryClass}">${category}</span></td>
                <td><span class="badge ${levelClass}">${level}</span></td>
                <td><span class="badge bg-info">${duration}</span></td>
                <td><span class="badge bg-success">0</span></td>
                <td><span class="badge ${status === 'نشط' ? 'bg-success' : 'bg-danger'}">${status}</span></td>
                <td class="action-buttons">
                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewSpec('${code}')">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editSpec('${code}')">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-outline-primary btn-sm" title="المتخصصين" onclick="viewSpecialists('${code}')">
                        <i class="fas fa-users"></i>
                    </button>
                </td>
            `;

            return row;
        }

        // وظيفة إغلاق النافذة
        function closeModal(element) {
            const modal = element.closest('div[style*="position: fixed"]');
            if (modal) modal.remove();
        }

        // رسالة نجاح
        function showSuccessMessage(message) {
            const successModal = `
                <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999; display: flex; align-items: center; justify-content: center;">
                    <div style="background: white; border-radius: 12px; padding: 30px; max-width: 400px; width: 90%; text-align: center; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
                        <div style="color: #6f42c1; font-size: 3rem; margin-bottom: 15px;">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <h4 style="color: #6f42c1; margin-bottom: 15px;">تم بنجاح!</h4>
                        <p style="color: #6c757d; margin-bottom: 20px;">${message}</p>
                        <button onclick="this.closest('div').remove();" style="background: #6f42c1; color: white; border: none; padding: 10px 20px; border-radius: 8px; cursor: pointer;">
                            موافق
                        </button>
                    </div>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', successModal);
        }
        
        function exportSpecializations() {
            alert(`📊 تصدير التخصصات\n\nسيتم إنشاء ملف Excel يحتوي على:\n• جميع التخصصات والتفاصيل\n• التصنيفات والفئات\n• إحصائيات المتخصصين\n• مخططات بيانية\n\nحجم الملف المتوقع: 1.1 MB`);
        }
        
        function printSpecializations() {
            alert(`🖨️ طباعة دليل التخصصات\n\nسيتم إعداد:\n• دليل شامل للتخصصات\n• التصنيفات والفئات\n• إحصائيات مفصلة\n• مخططات بيانية\n\nعدد الصفحات المتوقع: 25 صفحة`);
        }

        console.log('✅ صفحة التخصصات محملة بنجاح');
        console.log('🔬 35 تخصص متاح');
        console.log('📊 7 فئات رئيسية');
    </script>
</body>
</html>
