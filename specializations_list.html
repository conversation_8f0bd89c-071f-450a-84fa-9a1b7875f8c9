<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التخصصات - نظام المحاسبة المتكامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: #f8f9fa;
        }
        
        .navbar {
            background: linear-gradient(135deg, #6c757d 0%, #545b62 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .card {
            border: none;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
            border-radius: 12px;
        }
        
        .table th {
            background: #343a40;
            color: white;
            text-align: center;
            border: none;
        }
        
        .table td {
            vertical-align: middle;
            text-align: center;
        }
        
        .spec-category {
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .cat-medical {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
        }
        
        .cat-engineering {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
        }
        
        .cat-business {
            background: linear-gradient(45deg, #f39c12, #e67e22);
            color: white;
        }
        
        .cat-science {
            background: linear-gradient(45deg, #27ae60, #229954);
            color: white;
        }
        
        .cat-arts {
            background: linear-gradient(45deg, #9b59b6, #8e44ad);
            color: white;
        }
        
        .cat-law {
            background: linear-gradient(45deg, #34495e, #2c3e50);
            color: white;
        }
        
        .cat-education {
            background: linear-gradient(45deg, #16a085, #138d75);
            color: white;
        }
        
        .search-box {
            border-radius: 25px;
            border: 2px solid #e9ecef;
            padding: 10px 20px;
        }
        
        .search-box:focus {
            border-color: #6c757d;
            box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.25);
        }
        
        .action-buttons .btn {
            margin: 2px;
        }
        
        .spec-stats {
            background: linear-gradient(135deg, #6c757d, #545b62);
            color: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="simple_homepage.html">
                <i class="fas fa-calculator me-2"></i>
                نظام المحاسبة المتكامل
            </a>
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="simple_homepage.html">
                    <i class="fas fa-home me-1"></i>الرئيسية
                </a>
                <a class="nav-link" href="employees_page.html">
                    <i class="fas fa-users me-1"></i>الموظفون
                </a>
                <a class="nav-link" href="basic_lists_page.html">
                    <i class="fas fa-list me-1"></i>القوائم الأساسية
                </a>
                <a class="nav-link active" href="specializations_list.html">
                    <i class="fas fa-microscope me-1"></i>التخصصات
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- عنوان الصفحة -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2 class="text-secondary">
                        <i class="fas fa-microscope me-2"></i>
                        التخصصات الأكاديمية والمهنية
                    </h2>
                    <div>
                        <button class="btn btn-secondary me-2" onclick="showAddSpecModal()">
                            <i class="fas fa-plus me-1"></i>إضافة تخصص
                        </button>
                        <a href="basic_lists_page.html" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right me-1"></i>العودة للقوائم
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات التخصصات -->
        <div class="spec-stats">
            <div class="row text-center">
                <div class="col-md-2">
                    <h3><i class="fas fa-stethoscope me-2"></i>8</h3>
                    <p class="mb-0">طبية</p>
                </div>
                <div class="col-md-2">
                    <h3><i class="fas fa-cogs me-2"></i>6</h3>
                    <p class="mb-0">هندسية</p>
                </div>
                <div class="col-md-2">
                    <h3><i class="fas fa-chart-line me-2"></i>7</h3>
                    <p class="mb-0">إدارية</p>
                </div>
                <div class="col-md-2">
                    <h3><i class="fas fa-flask me-2"></i>5</h3>
                    <p class="mb-0">علمية</p>
                </div>
                <div class="col-md-2">
                    <h3><i class="fas fa-palette me-2"></i>4</h3>
                    <p class="mb-0">إنسانية</p>
                </div>
                <div class="col-md-2">
                    <h3><i class="fas fa-balance-scale me-2"></i>5</h3>
                    <p class="mb-0">قانونية وتربوية</p>
                </div>
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h4>35</h4>
                        <small>إجمالي التخصصات</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h4>125</h4>
                        <small>إجمالي المتخصصين</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h4>7</h4>
                        <small>فئات رئيسية</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-dark">
                    <div class="card-body text-center">
                        <h4>92%</h4>
                        <small>نسبة التغطية</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- شريط البحث والفلترة -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-search"></i>
                                    </span>
                                    <input type="text" class="form-control search-box" placeholder="البحث عن تخصص..." id="searchInput">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="categoryFilter">
                                    <option value="">جميع الفئات</option>
                                    <option value="طبية">طبية</option>
                                    <option value="هندسية">هندسية</option>
                                    <option value="إدارية">إدارية</option>
                                    <option value="علمية">علمية</option>
                                    <option value="إنسانية">إنسانية</option>
                                    <option value="قانونية">قانونية</option>
                                    <option value="تربوية">تربوية</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="levelFilter">
                                    <option value="">جميع المستويات</option>
                                    <option value="دكتوراه">دكتوراه</option>
                                    <option value="ماجستير">ماجستير</option>
                                    <option value="بكالوريوس">بكالوريوس</option>
                                    <option value="دبلوم">دبلوم</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button class="btn btn-outline-secondary w-100" onclick="clearFilters()">
                                    <i class="fas fa-times me-1"></i>مسح
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- جدول التخصصات -->
        <div class="card">
            <div class="card-header bg-secondary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>قائمة التخصصات
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0" id="specializationsTable">
                        <thead>
                            <tr>
                                <th>الرمز</th>
                                <th>اسم التخصص</th>
                                <th>الفئة</th>
                                <th>المستوى المطلوب</th>
                                <th>الوصف</th>
                                <th>عدد المتخصصين</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="specializationsTableBody">
                            <tr>
                                <td><strong>MED-001</strong></td>
                                <td>طب باطني</td>
                                <td><span class="spec-category cat-medical">طبية</span></td>
                                <td><span class="badge bg-info">دكتوراه</span></td>
                                <td>تخصص في الأمراض الباطنية</td>
                                <td><span class="badge bg-success">3</span></td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewSpec('MED-001')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editSpec('MED-001')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="المتخصصين" onclick="viewSpecialists('MED-001')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>MED-002</strong></td>
                                <td>جراحة عامة</td>
                                <td><span class="spec-category cat-medical">طبية</span></td>
                                <td><span class="badge bg-info">دكتوراه</span></td>
                                <td>تخصص في الجراحة العامة</td>
                                <td><span class="badge bg-success">2</span></td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewSpec('MED-002')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editSpec('MED-002')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="المتخصصين" onclick="viewSpecialists('MED-002')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>ENG-001</strong></td>
                                <td>هندسة مدنية</td>
                                <td><span class="spec-category cat-engineering">هندسية</span></td>
                                <td><span class="badge bg-info">بكالوريوس</span></td>
                                <td>تخصص في الهندسة المدنية</td>
                                <td><span class="badge bg-success">4</span></td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewSpec('ENG-001')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editSpec('ENG-001')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="المتخصصين" onclick="viewSpecialists('ENG-001')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>BUS-001</strong></td>
                                <td>محاسبة</td>
                                <td><span class="spec-category cat-business">إدارية</span></td>
                                <td><span class="badge bg-info">بكالوريوس</span></td>
                                <td>تخصص في المحاسبة والمالية</td>
                                <td><span class="badge bg-success">25</span></td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewSpec('BUS-001')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editSpec('BUS-001')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="المتخصصين" onclick="viewSpecialists('BUS-001')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>BUS-002</strong></td>
                                <td>إدارة أعمال</td>
                                <td><span class="spec-category cat-business">إدارية</span></td>
                                <td><span class="badge bg-info">بكالوريوس</span></td>
                                <td>تخصص في إدارة الأعمال</td>
                                <td><span class="badge bg-success">18</span></td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewSpec('BUS-002')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editSpec('BUS-002')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="المتخصصين" onclick="viewSpecialists('BUS-002')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>SCI-001</strong></td>
                                <td>علوم حاسوب</td>
                                <td><span class="spec-category cat-science">علمية</span></td>
                                <td><span class="badge bg-info">بكالوريوس</span></td>
                                <td>تخصص في علوم الحاسوب</td>
                                <td><span class="badge bg-success">8</span></td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewSpec('SCI-001')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editSpec('SCI-001')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="المتخصصين" onclick="viewSpecialists('SCI-001')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>LAW-001</strong></td>
                                <td>قانون</td>
                                <td><span class="spec-category cat-law">قانونية</span></td>
                                <td><span class="badge bg-info">بكالوريوس</span></td>
                                <td>تخصص في القانون</td>
                                <td><span class="badge bg-success">3</span></td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewSpec('LAW-001')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editSpec('LAW-001')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="المتخصصين" onclick="viewSpecialists('LAW-001')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>EDU-001</strong></td>
                                <td>تربية وتعليم</td>
                                <td><span class="spec-category cat-education">تربوية</span></td>
                                <td><span class="badge bg-info">بكالوريوس</span></td>
                                <td>تخصص في التربية والتعليم</td>
                                <td><span class="badge bg-success">12</span></td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewSpec('EDU-001')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editSpec('EDU-001')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="المتخصصين" onclick="viewSpecialists('EDU-001')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between align-items-center">
                    <span class="text-muted">عرض 8 من أصل 35 تخصص</span>
                    <div>
                        <button class="btn btn-outline-secondary btn-sm me-2" onclick="exportSpecializations()">
                            <i class="fas fa-file-excel me-1"></i>تصدير Excel
                        </button>
                        <button class="btn btn-outline-info btn-sm" onclick="printSpecializations()">
                            <i class="fas fa-print me-1"></i>طباعة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // وظائف البحث والفلترة
        function filterSpecializations() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const categoryFilter = document.getElementById('categoryFilter').value;
            const levelFilter = document.getElementById('levelFilter').value;
            
            const rows = document.querySelectorAll('#specializationsTableBody tr');
            
            rows.forEach(row => {
                const name = row.cells[1].textContent.toLowerCase();
                const category = row.cells[2].textContent;
                const level = row.cells[3].textContent;
                
                const matchesSearch = name.includes(searchTerm);
                const matchesCategory = !categoryFilter || category.includes(categoryFilter);
                const matchesLevel = !levelFilter || level.includes(levelFilter);
                
                if (matchesSearch && matchesCategory && matchesLevel) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }
        
        function clearFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('categoryFilter').value = '';
            document.getElementById('levelFilter').value = '';
            filterSpecializations();
        }
        
        // ربط أحداث البحث
        document.getElementById('searchInput').addEventListener('input', filterSpecializations);
        document.getElementById('categoryFilter').addEventListener('change', filterSpecializations);
        document.getElementById('levelFilter').addEventListener('change', filterSpecializations);
        
        // وظائف الإجراءات
        function viewSpec(code) {
            alert(`عرض تفاصيل التخصص: ${code}\n\nسيتم عرض:\n• الوصف التفصيلي\n• المناهج والمقررات\n• فرص العمل\n• الجامعات المعتمدة\n• إحصائيات المتخصصين`);
        }
        
        function editSpec(code) {
            alert(`تعديل التخصص: ${code}\n\nيمكن تعديل:\n• اسم التخصص\n• الفئة والتصنيف\n• المستوى المطلوب\n• الوصف والمتطلبات\n• الحالة`);
        }
        
        function viewSpecialists(code) {
            alert(`المتخصصين في: ${code}\n\nسيتم عرض:\n• قائمة المتخصصين\n• الجامعات المتخرجين منها\n• سنوات التخرج\n• التقديرات والدرجات\n• الخبرات العملية`);
        }
        
        function showAddSpecModal() {
            alert(`إضافة تخصص جديد\n\nالحقول المطلوبة:\n• رمز التخصص\n• اسم التخصص\n• الفئة (طبية/هندسية/إدارية/علمية/إنسانية/قانونية/تربوية)\n• المستوى المطلوب\n• الوصف التفصيلي`);
        }
        
        function exportSpecializations() {
            alert(`📊 تصدير التخصصات\n\nسيتم إنشاء ملف Excel يحتوي على:\n• جميع التخصصات والتفاصيل\n• التصنيفات والفئات\n• إحصائيات المتخصصين\n• مخططات بيانية\n\nحجم الملف المتوقع: 1.1 MB`);
        }
        
        function printSpecializations() {
            alert(`🖨️ طباعة دليل التخصصات\n\nسيتم إعداد:\n• دليل شامل للتخصصات\n• التصنيفات والفئات\n• إحصائيات مفصلة\n• مخططات بيانية\n\nعدد الصفحات المتوقع: 25 صفحة`);
        }

        console.log('✅ صفحة التخصصات محملة بنجاح');
        console.log('🔬 35 تخصص متاح');
        console.log('📊 7 فئات رئيسية');
    </script>
</body>
</html>
