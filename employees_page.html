<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الموظفين - نظام المحاسبة المتكامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: #f8f9fa;
        }
        
        .navbar {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .card {
            border: none;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
            border-radius: 12px;
        }
        
        .table th {
            background: #343a40;
            color: white;
            text-align: center;
            border: none;
        }
        
        .table td {
            vertical-align: middle;
            text-align: center;
        }
        
        .badge {
            font-size: 0.8rem;
        }
        
        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }
        
        .stats-card {
            transition: transform 0.2s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
        }
        
        .search-box {
            border-radius: 25px;
            border: 2px solid #e9ecef;
            padding: 10px 20px;
        }
        
        .search-box:focus {
            border-color: #28a745;
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
        }
        
        .action-buttons .btn {
            margin: 2px;
        }
        
        .employee-photo {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="simple_homepage.html">
                <i class="fas fa-calculator me-2"></i>
                نظام المحاسبة المتكامل
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="simple_homepage.html">
                            <i class="fas fa-home me-1"></i>الرئيسية
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="employees_page.html">
                            <i class="fas fa-users me-1"></i>الموظفون
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="login_page.html">
                            <i class="fas fa-sign-in-alt me-1"></i>تسجيل الدخول
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- عنوان الصفحة -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2 class="text-success">
                        <i class="fas fa-users me-2"></i>
                        إدارة الموظفين
                    </h2>
                    <div>
                        <a href="add_employee_page.html" class="btn btn-primary me-2">
                            <i class="fas fa-plus me-1"></i>إضافة موظف
                        </a>
                        <a href="import_excel_page.html" class="btn btn-success">
                            <i class="fas fa-file-excel me-1"></i>استيراد Excel
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-users fa-2x mb-2"></i>
                        <h3>25</h3>
                        <p class="mb-0">إجمالي الموظفين</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-user-check fa-2x mb-2"></i>
                        <h3>23</h3>
                        <p class="mb-0">الموظفون النشطون</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-money-bill-wave fa-2x mb-2"></i>
                        <h3>15,750,000</h3>
                        <p class="mb-0">إجمالي الرواتب (د.ع)</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-chart-line fa-2x mb-2"></i>
                        <h3>630,000</h3>
                        <p class="mb-0">متوسط الراتب (د.ع)</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- شريط البحث والفلترة -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-search"></i>
                                    </span>
                                    <input type="text" class="form-control search-box" placeholder="البحث عن موظف..." id="searchInput">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="departmentFilter">
                                    <option value="">جميع الأقسام</option>
                                    <option value="محاسبة">المحاسبة</option>
                                    <option value="إدارية">الشؤون الإدارية</option>
                                    <option value="طبية">الشؤون الطبية</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="statusFilter">
                                    <option value="">جميع الحالات</option>
                                    <option value="نشط">نشط</option>
                                    <option value="معطل">معطل</option>
                                    <option value="إجازة">في إجازة</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button class="btn btn-outline-secondary w-100" onclick="clearFilters()">
                                    <i class="fas fa-times me-1"></i>مسح
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- جدول الموظفين -->
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>قائمة الموظفين
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0" id="employeesTable">
                        <thead>
                            <tr>
                                <th>الصورة</th>
                                <th>الرقم الوظيفي</th>
                                <th>الاسم الكامل</th>
                                <th>المنصب</th>
                                <th>القسم</th>
                                <th>الدرجة</th>
                                <th>الراتب الأساسي</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="employeesTableBody">
                            <tr>
                                <td>
                                    <img src="https://via.placeholder.com/40x40/007bff/ffffff?text=أ" class="employee-photo" alt="صورة الموظف">
                                </td>
                                <td><strong>12001</strong></td>
                                <td>أحمد محمد علي الحسيني</td>
                                <td>محاسب أول</td>
                                <td>المحاسبة</td>
                                <td><span class="badge bg-info">الثالثة</span></td>
                                <td><strong>750,000 د.ع</strong></td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewEmployee(12001)">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editEmployee(12001)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-success btn-sm" title="المخصصات" onclick="manageAllowances(12001)">
                                        <i class="fas fa-plus-circle"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="كشف الراتب" onclick="generatePayslip(12001)">
                                        <i class="fas fa-file-pdf"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <img src="https://via.placeholder.com/40x40/28a745/ffffff?text=ف" class="employee-photo" alt="صورة الموظف">
                                </td>
                                <td><strong>12002</strong></td>
                                <td>فاطمة حسن محمود الزهراء</td>
                                <td>كاتبة</td>
                                <td>الشؤون الإدارية</td>
                                <td><span class="badge bg-info">الخامسة</span></td>
                                <td><strong>580,000 د.ع</strong></td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewEmployee(12002)">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editEmployee(12002)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-success btn-sm" title="المخصصات" onclick="manageAllowances(12002)">
                                        <i class="fas fa-plus-circle"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="كشف الراتب" onclick="generatePayslip(12002)">
                                        <i class="fas fa-file-pdf"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <img src="https://via.placeholder.com/40x40/dc3545/ffffff?text=م" class="employee-photo" alt="صورة الموظف">
                                </td>
                                <td><strong>12003</strong></td>
                                <td>محمد عبد الله أحمد الكريم</td>
                                <td>مدير الشعبة</td>
                                <td>الشؤون الطبية</td>
                                <td><span class="badge bg-info">الأولى</span></td>
                                <td><strong>950,000 د.ع</strong></td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewEmployee(12003)">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editEmployee(12003)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-success btn-sm" title="المخصصات" onclick="manageAllowances(12003)">
                                        <i class="fas fa-plus-circle"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="كشف الراتب" onclick="generatePayslip(12003)">
                                        <i class="fas fa-file-pdf"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <img src="https://via.placeholder.com/40x40/6f42c1/ffffff?text=س" class="employee-photo" alt="صورة الموظف">
                                </td>
                                <td><strong>12004</strong></td>
                                <td>سارة علي حسن الجميل</td>
                                <td>طبيبة</td>
                                <td>الشؤون الطبية</td>
                                <td><span class="badge bg-info">الثانية</span></td>
                                <td><strong>850,000 د.ع</strong></td>
                                <td><span class="badge bg-warning">في إجازة</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewEmployee(12004)">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editEmployee(12004)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-success btn-sm" title="المخصصات" onclick="manageAllowances(12004)">
                                        <i class="fas fa-plus-circle"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="كشف الراتب" onclick="generatePayslip(12004)">
                                        <i class="fas fa-file-pdf"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <img src="https://via.placeholder.com/40x40/fd7e14/ffffff?text=ع" class="employee-photo" alt="صورة الموظف">
                                </td>
                                <td><strong>12005</strong></td>
                                <td>علي حسين محمد الطيب</td>
                                <td>مساعد إداري</td>
                                <td>الشؤون الإدارية</td>
                                <td><span class="badge bg-info">السادسة</span></td>
                                <td><strong>520,000 د.ع</strong></td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewEmployee(12005)">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editEmployee(12005)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-success btn-sm" title="المخصصات" onclick="manageAllowances(12005)">
                                        <i class="fas fa-plus-circle"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="كشف الراتب" onclick="generatePayslip(12005)">
                                        <i class="fas fa-file-pdf"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between align-items-center">
                    <span class="text-muted">عرض 5 من أصل 25 موظف</span>
                    <nav>
                        <ul class="pagination pagination-sm mb-0">
                            <li class="page-item disabled">
                                <span class="page-link">السابق</span>
                            </li>
                            <li class="page-item active">
                                <span class="page-link">1</span>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">2</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">3</a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="#">التالي</a>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // وظائف البحث والفلترة
        function filterEmployees() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const departmentFilter = document.getElementById('departmentFilter').value;
            const statusFilter = document.getElementById('statusFilter').value;
            
            const rows = document.querySelectorAll('#employeesTableBody tr');
            
            rows.forEach(row => {
                const name = row.cells[2].textContent.toLowerCase();
                const department = row.cells[4].textContent;
                const status = row.cells[7].textContent;
                
                const matchesSearch = name.includes(searchTerm);
                const matchesDepartment = !departmentFilter || department.includes(departmentFilter);
                const matchesStatus = !statusFilter || status.includes(statusFilter);
                
                if (matchesSearch && matchesDepartment && matchesStatus) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }
        
        function clearFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('departmentFilter').value = '';
            document.getElementById('statusFilter').value = '';
            filterEmployees();
        }
        
        // ربط أحداث البحث
        document.getElementById('searchInput').addEventListener('input', filterEmployees);
        document.getElementById('departmentFilter').addEventListener('change', filterEmployees);
        document.getElementById('statusFilter').addEventListener('change', filterEmployees);
        
        // وظائف الإجراءات
        function viewEmployee(id) {
            alert(`عرض تفاصيل الموظف رقم: ${id}\n\nسيتم فتح نافذة تفاصيل الموظف مع:\n• البيانات الشخصية\n• تاريخ التوظيف\n• المخصصات والاستقطاعات\n• سجل الرواتب`);
        }
        
        function editEmployee(id) {
            alert(`تعديل بيانات الموظف رقم: ${id}\n\nسيتم فتح نموذج التعديل مع:\n• البيانات الأساسية\n• معلومات الوظيفة\n• بيانات الاتصال\n• الحالة الوظيفية`);
        }
        
        function manageAllowances(id) {
            alert(`إدارة مخصصات الموظف رقم: ${id}\n\nيتضمن:\n• مخصصات النقل\n• مخصصات السكن\n• مخصصات الطعام\n• المكافآت والحوافز\n• الاستقطاعات`);
        }
        
        function generatePayslip(id) {
            alert(`إنشاء كشف راتب للموظف رقم: ${id}\n\nسيتم إنشاء:\n• كشف راتب PDF\n• تفاصيل المخصصات\n• تفاصيل الاستقطاعات\n• الراتب الصافي`);
        }
        
        // تم نقل هذه الوظائف إلى صفحات منفصلة
        // showAddEmployeeForm() -> add_employee_page.html
        // showImportForm() -> import_excel_page.html
        
        console.log('✅ صفحة إدارة الموظفين محملة بنجاح');
        console.log('🔍 البحث والفلترة متاحة');
        console.log('⚡ جميع الوظائف مفعلة');
    </script>
</body>
</html>
