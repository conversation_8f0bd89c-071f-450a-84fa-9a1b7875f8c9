# -*- coding: utf-8 -*-
"""
مدير قاعدة البيانات
Database Manager for Accounting System
"""

import sqlite3
import os
from datetime import datetime
from pathlib import Path
from config.settings import DATABASE_CONFIG, BASE_DIR

class DatabaseManager:
    def __init__(self):
        self.db_path = DATABASE_CONFIG['sqlite_path']
        self.ensure_database_exists()
        self.create_tables()
    
    def ensure_database_exists(self):
        """التأكد من وجود قاعدة البيانات وإنشاؤها إذا لم تكن موجودة"""
        db_dir = Path(self.db_path).parent
        db_dir.mkdir(parents=True, exist_ok=True)
    
    def get_connection(self):
        """الحصول على اتصال بقاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def create_tables(self):
        """إنشاء جداول قاعدة البيانات"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # جدول المستخدمين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                full_name TEXT NOT NULL,
                role TEXT NOT NULL,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP
            )
        ''')
        
        # جدول الموظفين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS employees (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_number TEXT UNIQUE NOT NULL,
                full_name TEXT NOT NULL,
                job_title TEXT NOT NULL,
                job_grade TEXT NOT NULL,
                qualification TEXT,
                stage TEXT,
                basic_salary REAL NOT NULL,
                ministry TEXT,
                department TEXT,
                section TEXT,
                division TEXT,
                hire_date DATE,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول المخصصات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS allowances (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                allowance_type TEXT NOT NULL,
                amount REAL NOT NULL,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (employee_id) REFERENCES employees (id)
            )
        ''')
        
        # جدول الاستقطاعات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS deductions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                deduction_type TEXT NOT NULL,
                amount REAL NOT NULL,
                percentage REAL,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (employee_id) REFERENCES employees (id)
            )
        ''')
        
        # جدول الرواتب الشهرية
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS monthly_salaries (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                month INTEGER NOT NULL,
                year INTEGER NOT NULL,
                basic_salary REAL NOT NULL,
                total_allowances REAL DEFAULT 0,
                total_deductions REAL DEFAULT 0,
                net_salary REAL NOT NULL,
                is_processed BOOLEAN DEFAULT 0,
                processed_at TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (employee_id) REFERENCES employees (id),
                UNIQUE(employee_id, month, year)
            )
        ''')
        
        # جدول دليل الحسابات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS chart_of_accounts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                account_code TEXT UNIQUE NOT NULL,
                account_name TEXT NOT NULL,
                account_type TEXT NOT NULL,
                parent_account_id INTEGER,
                chapter TEXT,
                material TEXT,
                type_detail TEXT,
                details TEXT,
                description TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (parent_account_id) REFERENCES chart_of_accounts (id)
            )
        ''')
        
        # جدول الحسابات البنكية
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS bank_accounts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                account_number TEXT UNIQUE NOT NULL,
                account_name TEXT NOT NULL,
                bank_name TEXT NOT NULL,
                branch_name TEXT,
                branch_code TEXT,
                account_type TEXT NOT NULL,
                current_balance REAL DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول الفترات المالية
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS fiscal_periods (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                period_name TEXT NOT NULL,
                start_date DATE NOT NULL,
                end_date DATE NOT NULL,
                is_closed BOOLEAN DEFAULT 0,
                closed_at TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
        
        # إدراج البيانات الأولية
        self.insert_initial_data()
    
    def insert_initial_data(self):
        """إدراج البيانات الأولية"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # إدراج مستخدم افتراضي
        cursor.execute('''
            INSERT OR IGNORE INTO users (username, password, full_name, role)
            VALUES (?, ?, ?, ?)
        ''', ('admin', 'admin123', 'مدير النظام', 'admin'))
        
        # إدراج الحسابات البنكية الافتراضية
        bank_accounts = [
            ('***************', 'حساب تشغيلي', 'مصرف الرافدين', 'فرع دور الضباط', '69', 'تشغيلي'),
            ('***************', 'حساب رواتب', 'مصرف الرافدين', 'فرع دور الضباط', '69', 'رواتب')
        ]
        
        for account in bank_accounts:
            cursor.execute('''
                INSERT OR IGNORE INTO bank_accounts 
                (account_number, account_name, bank_name, branch_name, branch_code, account_type)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', account)
        
        conn.commit()
        conn.close()
    
    def execute_query(self, query, params=None):
        """تنفيذ استعلام قاعدة البيانات"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            if query.strip().upper().startswith('SELECT'):
                result = cursor.fetchall()
            else:
                conn.commit()
                result = cursor.rowcount
            
            return result
        except Exception as e:
            conn.rollback()
            raise e
        finally:
            conn.close()
    
    def backup_database(self, backup_path=None):
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        if not backup_path:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = BASE_DIR / "backups" / f"backup_{timestamp}.db"
        
        import shutil
        shutil.copy2(self.db_path, backup_path)
        return backup_path

# إنشاء مثيل مشترك من مدير قاعدة البيانات
db_manager = DatabaseManager()
