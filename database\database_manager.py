# -*- coding: utf-8 -*-
"""
مدير قاعدة البيانات المتقدم
Advanced Database Manager for Accounting System
"""

import sqlite3
import os
from datetime import datetime
from pathlib import Path
from sqlalchemy import create_engine, MetaData, Table, Column, Integer, String, Float, Boolean, DateTime, Date, Text, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
from config.settings import DATABASE_CONFIG, get_database_url

Base = declarative_base()

class DatabaseManager:
    def __init__(self):
        self.database_url = get_database_url()
        self.engine = create_engine(self.database_url, echo=False)
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
        self.ensure_database_exists()
        self.create_tables()
    
    def ensure_database_exists(self):
        """التأكد من وجود قاعدة البيانات وإنشاؤها إذا لم تكن موجودة"""
        if DATABASE_CONFIG['type'] == 'sqlite':
            db_dir = Path(DATABASE_CONFIG['sqlite_path']).parent
            db_dir.mkdir(parents=True, exist_ok=True)
    
    def get_session(self):
        """الحصول على جلسة قاعدة البيانات"""
        return self.SessionLocal()
    
    def create_tables(self):
        """إنشاء جداول قاعدة البيانات"""
        Base.metadata.create_all(bind=self.engine)
        self.insert_initial_data()
    
    def insert_initial_data(self):
        """إدراج البيانات الأولية"""
        session = self.get_session()
        try:
            # التحقق من وجود مستخدم افتراضي
            from models.user import User
            admin_user = session.query(User).filter(User.username == 'admin').first()
            if not admin_user:
                from passlib.context import CryptContext
                pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
                hashed_password = pwd_context.hash("admin123")
                
                admin_user = User(
                    username='admin',
                    password=hashed_password,
                    full_name='مدير النظام',
                    role='admin',
                    is_active=True
                )
                session.add(admin_user)
            
            # إدراج الحسابات البنكية الافتراضية
            from models.bank_account import BankAccount
            from config.settings import BANK_ACCOUNTS
            
            for account_key, account_data in BANK_ACCOUNTS.items():
                existing_account = session.query(BankAccount).filter(
                    BankAccount.account_number == account_data['number']
                ).first()
                
                if not existing_account:
                    bank_account = BankAccount(
                        account_number=account_data['number'],
                        account_name=account_data['type'],
                        bank_name=account_data['bank'],
                        branch_name=account_data['branch'],
                        branch_code=account_data['branch_code'],
                        account_type=account_data['type'],
                        current_balance=0.0,
                        is_active=True
                    )
                    session.add(bank_account)
            
            session.commit()
        except Exception as e:
            session.rollback()
            print(f"خطأ في إدراج البيانات الأولية: {e}")
        finally:
            session.close()
    
    def backup_database(self, backup_path=None):
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        if DATABASE_CONFIG['type'] == 'sqlite':
            if not backup_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_path = Path(DATABASE_CONFIG['sqlite_path']).parent.parent / "backups" / f"backup_{timestamp}.db"
            
            import shutil
            shutil.copy2(DATABASE_CONFIG['sqlite_path'], backup_path)
            return backup_path
        else:
            # للـ SQL Server يمكن استخدام أوامر SQL للنسخ الاحتياطي
            pass
    
    def restore_database(self, backup_path):
        """استعادة قاعدة البيانات من نسخة احتياطية"""
        if DATABASE_CONFIG['type'] == 'sqlite':
            import shutil
            shutil.copy2(backup_path, DATABASE_CONFIG['sqlite_path'])
            return True
        return False
    
    def execute_raw_query(self, query, params=None):
        """تنفيذ استعلام SQL خام"""
        session = self.get_session()
        try:
            result = session.execute(query, params or {})
            if query.strip().upper().startswith('SELECT'):
                return result.fetchall()
            else:
                session.commit()
                return result.rowcount
        except Exception as e:
            session.rollback()
            raise e
        finally:
            session.close()
    
    def get_table_info(self, table_name):
        """الحصول على معلومات الجدول"""
        metadata = MetaData()
        metadata.reflect(bind=self.engine)
        if table_name in metadata.tables:
            return metadata.tables[table_name]
        return None
    
    def export_table_to_excel(self, table_name, file_path):
        """تصدير جدول إلى Excel"""
        import pandas as pd
        
        session = self.get_session()
        try:
            # الحصول على البيانات
            query = f"SELECT * FROM {table_name}"
            df = pd.read_sql(query, self.engine)
            
            # تصدير إلى Excel
            with pd.ExcelWriter(file_path, engine='xlsxwriter') as writer:
                df.to_excel(writer, sheet_name=table_name, index=False)
                
                # تنسيق الورقة
                workbook = writer.book
                worksheet = writer.sheets[table_name]
                
                # تنسيق الرأس
                header_format = workbook.add_format({
                    'bold': True,
                    'text_wrap': True,
                    'valign': 'top',
                    'fg_color': '#4472C4',
                    'font_color': 'white',
                    'border': 1
                })
                
                # تطبيق التنسيق على الرأس
                for col_num, value in enumerate(df.columns.values):
                    worksheet.write(0, col_num, value, header_format)
                    worksheet.set_column(col_num, col_num, 15)
            
            return True
        except Exception as e:
            print(f"خطأ في تصدير الجدول: {e}")
            return False
        finally:
            session.close()
    
    def import_from_excel(self, table_name, file_path, sheet_name=None):
        """استيراد البيانات من Excel"""
        import pandas as pd
        
        try:
            # قراءة البيانات من Excel
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            
            # تنظيف البيانات
            df = df.dropna(how='all')  # حذف الصفوف الفارغة
            df = df.fillna('')  # ملء القيم الفارغة
            
            # إدراج البيانات في قاعدة البيانات
            df.to_sql(table_name, self.engine, if_exists='append', index=False)
            
            return True, len(df)
        except Exception as e:
            print(f"خطأ في استيراد البيانات: {e}")
            return False, 0
    
    def get_database_statistics(self):
        """الحصول على إحصائيات قاعدة البيانات"""
        session = self.get_session()
        try:
            stats = {}
            
            # إحصائيات الموظفين
            from models.employee import Employee
            stats['total_employees'] = session.query(Employee).count()
            stats['active_employees'] = session.query(Employee).filter(Employee.is_active == True).count()
            
            # إحصائيات المستخدمين
            from models.user import User
            stats['total_users'] = session.query(User).count()
            stats['active_users'] = session.query(User).filter(User.is_active == True).count()
            
            # إحصائيات الرواتب
            from models.salary import MonthlySalary
            current_month = datetime.now().month
            current_year = datetime.now().year
            stats['current_month_salaries'] = session.query(MonthlySalary).filter(
                MonthlySalary.month == current_month,
                MonthlySalary.year == current_year
            ).count()
            
            return stats
        except Exception as e:
            print(f"خطأ في جلب الإحصائيات: {e}")
            return {}
        finally:
            session.close()

# إنشاء مثيل مشترك من مدير قاعدة البيانات
db_manager = DatabaseManager()
