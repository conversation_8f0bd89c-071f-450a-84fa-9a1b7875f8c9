<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام المحاسبة المتكامل - عرض توضيحي</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }
        
        .hero {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 4rem 0;
            margin-bottom: 2rem;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .feature-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: white;
            font-size: 2rem;
        }
        
        .login-demo {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin: 2rem 0;
        }
        
        .system-status {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin: 1rem 0;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-left: 8px;
        }
        
        .status-online { background-color: #28a745; }
        .status-offline { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
        
        .demo-button {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(23, 162, 184, 0.4);
            color: white;
        }
    </style>
</head>
<body>
    <!-- Hero Section -->
    <div class="hero text-center">
        <div class="container">
            <h1 class="display-3 fw-bold mb-4">
                <i class="fas fa-calculator me-3"></i>
                نظام المحاسبة المتكامل
            </h1>
            <p class="lead mb-4">نظام شامل لإدارة رواتب الموظفين والمصروفات للمؤسسات الحكومية</p>
            <h4 class="mb-3">وزارة الشباب والرياضة - دائرة الطب الرياضي</h4>
            <button class="btn btn-light btn-lg" onclick="showLoginDemo()">
                <i class="fas fa-sign-in-alt me-2"></i>
                عرض توضيحي لتسجيل الدخول
            </button>
        </div>
    </div>

    <div class="container">
        <!-- System Status -->
        <div class="system-status">
            <h5 class="mb-3">
                <i class="fas fa-server me-2"></i>
                حالة النظام
            </h5>
            <div class="row">
                <div class="col-md-4">
                    <span class="status-indicator status-online"></span>
                    <strong>قاعدة البيانات:</strong> متصلة
                </div>
                <div class="col-md-4">
                    <span class="status-indicator status-online"></span>
                    <strong>الخادم:</strong> يعمل بشكل طبيعي
                </div>
                <div class="col-md-4">
                    <span class="status-indicator status-warning"></span>
                    <strong>النسخ الاحتياطي:</strong> جاري الإعداد
                </div>
            </div>
        </div>

        <!-- Login Demo -->
        <div class="login-demo text-center" id="loginDemo" style="display: none;">
            <h4 class="mb-3">
                <i class="fas fa-key me-2"></i>
                بيانات الدخول الافتراضية
            </h4>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-2">
                        <strong>اسم المستخدم:</strong>
                        <code class="text-white bg-dark px-2 py-1 rounded">admin</code>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-2">
                        <strong>كلمة المرور:</strong>
                        <code class="text-white bg-dark px-2 py-1 rounded">admin123</code>
                    </div>
                </div>
            </div>
            <p class="mt-3 mb-0">
                <i class="fas fa-info-circle me-1"></i>
                يرجى تغيير كلمة المرور فور تسجيل الدخول الأول
            </p>
        </div>

        <!-- Features -->
        <div class="row mb-5">
            <div class="col-md-4 mb-4">
                <div class="card h-100 text-center">
                    <div class="card-body">
                        <div class="feature-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <h5 class="card-title">إدارة الموظفين</h5>
                        <p class="card-text">
                            إدارة شاملة لبيانات الموظفين مع دعم الهيكل التنظيمي الحكومي
                            والمخصصات والاستقطاعات المختلفة.
                        </p>
                        <ul class="list-unstyled text-start">
                            <li><i class="fas fa-check text-success me-2"></i>البيانات الشخصية</li>
                            <li><i class="fas fa-check text-success me-2"></i>المخصصات (منصب، زوجية، أولاد)</li>
                            <li><i class="fas fa-check text-success me-2"></i>الاستقطاعات (تقاعد، ضرائب)</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="col-md-4 mb-4">
                <div class="card h-100 text-center">
                    <div class="card-body">
                        <div class="feature-icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <h5 class="card-title">نظام الرواتب</h5>
                        <p class="card-text">
                            حساب الرواتب الشهرية تلقائياً مع المخصصات والاستقطاعات
                            وفقاً للنظام الحكومي العراقي.
                        </p>
                        <ul class="list-unstyled text-start">
                            <li><i class="fas fa-check text-success me-2"></i>حساب تلقائي للرواتب</li>
                            <li><i class="fas fa-check text-success me-2"></i>كشوفات رواتب مفصلة</li>
                            <li><i class="fas fa-check text-success me-2"></i>تقارير شهرية وسنوية</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="col-md-4 mb-4">
                <div class="card h-100 text-center">
                    <div class="card-body">
                        <div class="feature-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <h5 class="card-title">التقارير المالية</h5>
                        <p class="card-text">
                            تقارير شاملة ومفصلة مع إمكانية التصدير إلى PDF وExcel
                            ودعم كامل للغة العربية.
                        </p>
                        <ul class="list-unstyled text-start">
                            <li><i class="fas fa-check text-success me-2"></i>تصدير PDF وExcel</li>
                            <li><i class="fas fa-check text-success me-2"></i>تقارير قابلة للتخصيص</li>
                            <li><i class="fas fa-check text-success me-2"></i>دعم كامل للعربية</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Organization Info -->
        <div class="row mb-5">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-building me-2"></i>
                            معلومات المؤسسة
                        </h5>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <strong>الوزارة:</strong> وزارة الشباب والرياضة
                            </li>
                            <li class="mb-2">
                                <strong>الدائرة:</strong> دائرة الطب الرياضي
                            </li>
                            <li class="mb-2">
                                <strong>القسم:</strong> قسم إداري
                            </li>
                            <li class="mb-2">
                                <strong>الشعبة:</strong> شعبة الحسابات
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-university me-2"></i>
                            الحسابات البنكية
                        </h5>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <strong>حساب تشغيلي:</strong> 069100011822001
                            </li>
                            <li class="mb-2">
                                <strong>حساب رواتب:</strong> 069100011822002
                            </li>
                            <li class="mb-2">
                                <strong>البنك:</strong> مصرف الرافدين
                            </li>
                            <li class="mb-2">
                                <strong>الفرع:</strong> فرع دور الضباط (69)
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Demo Actions -->
        <div class="text-center mb-5">
            <h4 class="mb-4">تجربة النظام</h4>
            <div class="row">
                <div class="col-md-4 mb-3">
                    <button class="demo-button w-100" onclick="showDemo('desktop')">
                        <i class="fas fa-desktop me-2"></i>
                        تطبيق سطح المكتب
                    </button>
                </div>
                <div class="col-md-4 mb-3">
                    <button class="demo-button w-100" onclick="showDemo('web')">
                        <i class="fas fa-globe me-2"></i>
                        تطبيق الويب
                    </button>
                </div>
                <div class="col-md-4 mb-3">
                    <button class="demo-button w-100" onclick="showDemo('docs')">
                        <i class="fas fa-book me-2"></i>
                        دليل المستخدم
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white text-center py-4">
        <div class="container">
            <p class="mb-2">© 2024 وزارة الشباب والرياضة - دائرة الطب الرياضي - شعبة الحسابات</p>
            <p class="mb-0">
                <small>نظام المحاسبة المتكامل v1.0 - تم التطوير بعناية لخدمة المؤسسات الحكومية العراقية</small>
            </p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function showLoginDemo() {
            const demo = document.getElementById('loginDemo');
            if (demo.style.display === 'none') {
                demo.style.display = 'block';
                demo.scrollIntoView({ behavior: 'smooth' });
            } else {
                demo.style.display = 'none';
            }
        }
        
        function showDemo(type) {
            let message = '';
            switch(type) {
                case 'desktop':
                    message = '🖥️ تطبيق سطح المكتب\n\nلتشغيل التطبيق:\n1. تشغيل: python main.py\n2. تسجيل الدخول بالبيانات المعروضة\n3. استكشاف الواجهات المختلفة';
                    break;
                case 'web':
                    message = '🌐 تطبيق الويب\n\nلتشغيل التطبيق:\n1. تشغيل: python web_main.py\n2. فتح: http://localhost:8000\n3. تسجيل الدخول والاستكشاف';
                    break;
                case 'docs':
                    message = '📚 دليل المستخدم\n\nيمكنك مراجعة:\n• ملف README.md للدليل الشامل\n• ملف QUICK_START.md للبدء السريع\n• التعليقات في الكود للتفاصيل التقنية';
                    break;
            }
            alert(message);
        }
        
        // تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            // تحريك البطاقات عند التمرير
            const cards = document.querySelectorAll('.card');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            });
            
            cards.forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(card);
            });
        });
    </script>
</body>
</html>
