<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام المحاسبة المتكامل - عرض توضيحي</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }
        
        .hero {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 4rem 0;
            margin-bottom: 2rem;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .feature-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: white;
            font-size: 2rem;
        }
        
        .login-demo {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin: 2rem 0;
        }
        
        .system-status {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin: 1rem 0;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-left: 8px;
        }
        
        .status-online { background-color: #28a745; }
        .status-offline { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
        
        .demo-button {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            color: white;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .demo-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(23, 162, 184, 0.4);
            color: white;
        }
    </style>
</head>
<body>
    <!-- Hero Section -->
    <div class="hero text-center">
        <div class="container">
            <h1 class="display-3 fw-bold mb-4">
                <i class="fas fa-calculator me-3"></i>
                نظام المحاسبة المتكامل
            </h1>
            <p class="lead mb-4">نظام شامل لإدارة رواتب الموظفين والمصروفات للمؤسسات الحكومية</p>
            <h4 class="mb-3">وزارة الشباب والرياضة - دائرة الطب الرياضي</h4>
            <button class="btn btn-light btn-lg" onclick="showLoginDemo()">
                <i class="fas fa-sign-in-alt me-2"></i>
                عرض توضيحي لتسجيل الدخول
            </button>
        </div>
    </div>

    <div class="container">
        <!-- System Status -->
        <div class="system-status">
            <h5 class="mb-3">
                <i class="fas fa-server me-2"></i>
                حالة النظام
            </h5>
            <div class="row">
                <div class="col-md-4">
                    <span class="status-indicator status-online"></span>
                    <strong>قاعدة البيانات:</strong> متصلة
                </div>
                <div class="col-md-4">
                    <span class="status-indicator status-online"></span>
                    <strong>الخادم:</strong> يعمل بشكل طبيعي
                </div>
                <div class="col-md-4">
                    <span class="status-indicator status-warning"></span>
                    <strong>النسخ الاحتياطي:</strong> جاري الإعداد
                </div>
            </div>
        </div>

        <!-- Login Demo -->
        <div class="login-demo text-center" id="loginDemo" style="display: none;">
            <h4 class="mb-3">
                <i class="fas fa-key me-2"></i>
                بيانات الدخول الافتراضية
            </h4>
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-2">
                        <strong>اسم المستخدم:</strong>
                        <code class="text-white bg-dark px-2 py-1 rounded">admin</code>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-2">
                        <strong>كلمة المرور:</strong>
                        <code class="text-white bg-dark px-2 py-1 rounded">admin123</code>
                    </div>
                </div>
            </div>
            <p class="mt-3 mb-0">
                <i class="fas fa-info-circle me-1"></i>
                يرجى تغيير كلمة المرور فور تسجيل الدخول الأول
            </p>
        </div>

        <!-- Features -->
        <div class="row mb-5">
            <div class="col-md-4 mb-4">
                <div class="card h-100 text-center">
                    <div class="card-body">
                        <div class="feature-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <h5 class="card-title">إدارة الموظفين</h5>
                        <p class="card-text">
                            إدارة شاملة لبيانات الموظفين مع دعم الهيكل التنظيمي الحكومي
                            والمخصصات والاستقطاعات المختلفة.
                        </p>
                        <ul class="list-unstyled text-start">
                            <li><i class="fas fa-check text-success me-2"></i>البيانات الشخصية</li>
                            <li><i class="fas fa-check text-success me-2"></i>المخصصات (منصب، زوجية، أولاد)</li>
                            <li><i class="fas fa-check text-success me-2"></i>الاستقطاعات (تقاعد، ضرائب)</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="col-md-4 mb-4">
                <div class="card h-100 text-center">
                    <div class="card-body">
                        <div class="feature-icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <h5 class="card-title">نظام الرواتب</h5>
                        <p class="card-text">
                            حساب الرواتب الشهرية تلقائياً مع المخصصات والاستقطاعات
                            وفقاً للنظام الحكومي العراقي.
                        </p>
                        <ul class="list-unstyled text-start">
                            <li><i class="fas fa-check text-success me-2"></i>حساب تلقائي للرواتب</li>
                            <li><i class="fas fa-check text-success me-2"></i>كشوفات رواتب مفصلة</li>
                            <li><i class="fas fa-check text-success me-2"></i>تقارير شهرية وسنوية</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="col-md-4 mb-4">
                <div class="card h-100 text-center">
                    <div class="card-body">
                        <div class="feature-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <h5 class="card-title">التقارير المالية</h5>
                        <p class="card-text">
                            تقارير شاملة ومفصلة مع إمكانية التصدير إلى PDF وExcel
                            ودعم كامل للغة العربية.
                        </p>
                        <ul class="list-unstyled text-start">
                            <li><i class="fas fa-check text-success me-2"></i>تصدير PDF وExcel</li>
                            <li><i class="fas fa-check text-success me-2"></i>تقارير قابلة للتخصيص</li>
                            <li><i class="fas fa-check text-success me-2"></i>دعم كامل للعربية</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Organization Info -->
        <div class="row mb-5">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-building me-2"></i>
                            معلومات المؤسسة
                        </h5>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <strong>الوزارة:</strong> وزارة الشباب والرياضة
                            </li>
                            <li class="mb-2">
                                <strong>الدائرة:</strong> دائرة الطب الرياضي
                            </li>
                            <li class="mb-2">
                                <strong>القسم:</strong> قسم إداري
                            </li>
                            <li class="mb-2">
                                <strong>الشعبة:</strong> شعبة الحسابات
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-university me-2"></i>
                            الحسابات البنكية
                        </h5>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <strong>حساب تشغيلي:</strong> 069100011822001
                            </li>
                            <li class="mb-2">
                                <strong>حساب رواتب:</strong> 069100011822002
                            </li>
                            <li class="mb-2">
                                <strong>البنك:</strong> مصرف الرافدين
                            </li>
                            <li class="mb-2">
                                <strong>الفرع:</strong> فرع دور الضباط (69)
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Employee Management Demo -->
        <div class="card mb-5">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-users me-2"></i>
                    عرض توضيحي - إدارة الموظفين
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="fw-bold mb-3">الوظائف المتاحة:</h6>
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                <strong>قائمة الموظفين:</strong> عرض جميع الموظفين مع البحث والتصفية
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                <strong>إضافة موظف:</strong> إضافة موظف جديد مع جميع البيانات
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                <strong>استيراد من Excel:</strong> رفع ملف Excel واستيراد عدة موظفين
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                <strong>تفاصيل الموظف:</strong> عرض تفاصيل كاملة للموظف
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                <strong>تعديل البيانات:</strong> تعديل معلومات الموظف
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                <strong>إدارة المخصصات:</strong> إضافة وتعديل مخصصات الموظف
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                <strong>إدارة الاستقطاعات:</strong> إضافة وتعديل الاستقطاعات
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-check text-success me-2"></i>
                                <strong>تصدير إلى Excel:</strong> تحميل بيانات الموظفين كملف Excel
                            </li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="fw-bold mb-3">المخصصات المدعومة:</h6>
                        <div class="row">
                            <div class="col-6">
                                <ul class="list-unstyled">
                                    <li><span class="badge bg-primary me-1">منصب</span></li>
                                    <li><span class="badge bg-primary me-1">زوجية</span></li>
                                    <li><span class="badge bg-primary me-1">أولاد</span></li>
                                    <li><span class="badge bg-primary me-1">هندسية</span></li>
                                    <li><span class="badge bg-primary me-1">شهادة</span></li>
                                </ul>
                            </div>
                            <div class="col-6">
                                <ul class="list-unstyled">
                                    <li><span class="badge bg-primary me-1">حرفة</span></li>
                                    <li><span class="badge bg-primary me-1">خطورة</span></li>
                                    <li><span class="badge bg-primary me-1">نقل</span></li>
                                    <li><span class="badge bg-primary me-1">جامعية</span></li>
                                </ul>
                            </div>
                        </div>

                        <h6 class="fw-bold mb-3 mt-3">الاستقطاعات الافتراضية:</h6>
                        <ul class="list-unstyled">
                            <li><span class="badge bg-danger me-1">تقاعد 10%</span></li>
                            <li><span class="badge bg-danger me-1">مساهمة حكومية 15%</span></li>
                            <li><span class="badge bg-warning me-1">ضريبة الدخل</span></li>
                            <li><span class="badge bg-warning me-1">حماية اجتماعية</span></li>
                        </ul>
                    </div>
                </div>

                <div class="text-center mt-4">
                    <button class="btn btn-success btn-lg me-2" onclick="showEmployeeDemo()">
                        <i class="fas fa-users me-2"></i>
                        عرض واجهة إدارة الموظفين
                    </button>
                    <button class="btn btn-outline-success me-2" onclick="showImportDemo()">
                        <i class="fas fa-file-excel me-2"></i>
                        عرض واجهة الاستيراد
                    </button>
                    <button class="btn btn-outline-info" onclick="showSampleData()">
                        <i class="fas fa-database me-2"></i>
                        عرض بيانات تجريبية
                    </button>
                </div>
            </div>
        </div>

        <!-- Demo Actions -->
        <div class="text-center mb-5">
            <h4 class="mb-4">تجربة النظام</h4>
            <div class="row">
                <div class="col-md-4 mb-3">
                    <button class="demo-button w-100" onclick="showDemo('desktop')">
                        <i class="fas fa-desktop me-2"></i>
                        تطبيق سطح المكتب
                    </button>
                </div>
                <div class="col-md-4 mb-3">
                    <button class="demo-button w-100" onclick="showDemo('web')">
                        <i class="fas fa-globe me-2"></i>
                        تطبيق الويب
                    </button>
                </div>
                <div class="col-md-4 mb-3">
                    <button class="demo-button w-100" onclick="showDemo('docs')">
                        <i class="fas fa-book me-2"></i>
                        دليل المستخدم
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white text-center py-4">
        <div class="container">
            <p class="mb-2">© 2024 وزارة الشباب والرياضة - دائرة الطب الرياضي - شعبة الحسابات</p>
            <p class="mb-0">
                <small>نظام المحاسبة المتكامل v1.0 - تم التطوير بعناية لخدمة المؤسسات الحكومية العراقية</small>
            </p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function showLoginDemo() {
            const demo = document.getElementById('loginDemo');
            if (demo.style.display === 'none') {
                demo.style.display = 'block';
                demo.scrollIntoView({ behavior: 'smooth' });
            } else {
                demo.style.display = 'none';
            }
        }
        
        function showDemo(type) {
            let message = '';
            switch(type) {
                case 'desktop':
                    message = '🖥️ تطبيق سطح المكتب\n\nلتشغيل التطبيق:\n1. تشغيل: python main.py\n2. تسجيل الدخول بالبيانات المعروضة\n3. استكشاف الواجهات المختلفة';
                    break;
                case 'web':
                    message = '🌐 تطبيق الويب\n\nلتشغيل التطبيق:\n1. تشغيل: python web_main.py\n2. فتح: http://localhost:8000\n3. تسجيل الدخول والاستكشاف';
                    break;
                case 'docs':
                    message = '📚 دليل المستخدم\n\nيمكنك مراجعة:\n• ملف README.md للدليل الشامل\n• ملف QUICK_START.md للبدء السريع\n• التعليقات في الكود للتفاصيل التقنية';
                    break;
            }
            alert(message);
        }

        function showEmployeeDemo() {
            // إنشاء نافذة منبثقة لعرض واجهة إدارة الموظفين
            const demoWindow = window.open('', 'employeeDemo', 'width=1200,height=800,scrollbars=yes');

            demoWindow.document.write(`
                <!DOCTYPE html>
                <html lang="ar" dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>عرض توضيحي - إدارة الموظفين</title>
                    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
                    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
                    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
                    <style>
                        body { font-family: 'Cairo', sans-serif; background: #f8f9fa; }
                        .demo-header { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 2rem 0; }
                        .card { border: none; box-shadow: 0 4px 6px rgba(0,0,0,0.1); margin-bottom: 1rem; }
                        .table th { background: #343a40; color: white; text-align: center; }
                        .badge { font-size: 0.8rem; }
                        .btn-sm { padding: 0.25rem 0.5rem; font-size: 0.875rem; }
                    </style>
                </head>
                <body>
                    <div class="demo-header text-center">
                        <div class="container">
                            <h1><i class="fas fa-users me-2"></i>إدارة الموظفين</h1>
                            <p class="mb-0">عرض توضيحي للواجهات والوظائف</p>
                        </div>
                    </div>

                    <div class="container mt-4">
                        <!-- إحصائيات سريعة -->
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body text-center">
                                        <h3>25</h3>
                                        <p class="mb-0">إجمالي الموظفين</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <h3>23</h3>
                                        <p class="mb-0">الموظفون النشطون</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body text-center">
                                        <h3>15,750,000</h3>
                                        <p class="mb-0">إجمالي الرواتب</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-warning text-white">
                                    <div class="card-body text-center">
                                        <h3>630,000</h3>
                                        <p class="mb-0">متوسط الراتب</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- جدول الموظفين التجريبي -->
                        <div class="card">
                            <div class="card-header">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0"><i class="fas fa-list me-2"></i>قائمة الموظفين</h5>
                                    <button class="btn btn-primary btn-sm">
                                        <i class="fas fa-plus me-1"></i>إضافة موظف
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>الرقم الوظيفي</th>
                                                <th>الاسم</th>
                                                <th>المنصب</th>
                                                <th>الدرجة</th>
                                                <th>الراتب الأساسي</th>
                                                <th>الحالة</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td><strong>12001</strong></td>
                                                <td>أحمد محمد علي</td>
                                                <td>محاسب أول</td>
                                                <td><span class="badge bg-info">الثالثة</span></td>
                                                <td><strong>750,000 د.ع</strong></td>
                                                <td><span class="badge bg-success">نشط</span></td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <button class="btn btn-outline-info" title="عرض"><i class="fas fa-eye"></i></button>
                                                        <button class="btn btn-outline-warning" title="تعديل"><i class="fas fa-edit"></i></button>
                                                        <button class="btn btn-outline-success" title="مخصصات"><i class="fas fa-plus-circle"></i></button>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><strong>12002</strong></td>
                                                <td>فاطمة حسن محمود</td>
                                                <td>كاتبة</td>
                                                <td><span class="badge bg-info">الخامسة</span></td>
                                                <td><strong>580,000 د.ع</strong></td>
                                                <td><span class="badge bg-success">نشط</span></td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <button class="btn btn-outline-info" title="عرض"><i class="fas fa-eye"></i></button>
                                                        <button class="btn btn-outline-warning" title="تعديل"><i class="fas fa-edit"></i></button>
                                                        <button class="btn btn-outline-success" title="مخصصات"><i class="fas fa-plus-circle"></i></button>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td><strong>12003</strong></td>
                                                <td>محمد عبد الله أحمد</td>
                                                <td>مدير الشعبة</td>
                                                <td><span class="badge bg-info">الأولى</span></td>
                                                <td><strong>950,000 د.ع</strong></td>
                                                <td><span class="badge bg-success">نشط</span></td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <button class="btn btn-outline-info" title="عرض"><i class="fas fa-eye"></i></button>
                                                        <button class="btn btn-outline-warning" title="تعديل"><i class="fas fa-edit"></i></button>
                                                        <button class="btn btn-outline-success" title="مخصصات"><i class="fas fa-plus-circle"></i></button>
                                                    </div>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <div class="text-center mt-4">
                            <p class="text-muted">هذا عرض توضيحي للواجهات. للاستخدام الفعلي، يرجى تشغيل النظام.</p>
                            <button class="btn btn-secondary" onclick="window.close()">إغلاق</button>
                        </div>
                    </div>
                </body>
                </html>
            `);
        }

        function showImportDemo() {
            // إنشاء نافذة منبثقة لعرض واجهة استيراد Excel
            const demoWindow = window.open('', 'importDemo', 'width=1200,height=800,scrollbars=yes');

            demoWindow.document.write(`
                <!DOCTYPE html>
                <html lang="ar" dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>عرض توضيحي - استيراد الموظفين من Excel</title>
                    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
                    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
                    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
                    <style>
                        body { font-family: 'Cairo', sans-serif; background: #f8f9fa; }
                        .demo-header { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 2rem 0; }
                        .card { border: none; box-shadow: 0 4px 6px rgba(0,0,0,0.1); margin-bottom: 1rem; }
                        .table th { background: #343a40; color: white; text-align: center; }
                        .badge { font-size: 0.8rem; }
                        .btn-sm { padding: 0.25rem 0.5rem; font-size: 0.875rem; }
                        .alert { border: none; border-radius: 10px; }
                    </style>
                </head>
                <body>
                    <div class="demo-header text-center">
                        <div class="container">
                            <h1><i class="fas fa-file-excel me-2"></i>استيراد الموظفين من Excel</h1>
                            <p class="mb-0">عرض توضيحي لواجهة الاستيراد والتعليمات</p>
                        </div>
                    </div>

                    <div class="container mt-4">
                        <div class="row">
                            <!-- تعليمات الاستيراد -->
                            <div class="col-lg-8">
                                <div class="card">
                                    <div class="card-header bg-info text-white">
                                        <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>تعليمات الاستيراد</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="alert alert-info">
                                            <h6 class="alert-heading"><i class="fas fa-lightbulb me-2"></i>متطلبات ملف Excel:</h6>
                                            <ul class="mb-0">
                                                <li>يجب أن يكون الملف بصيغة .xlsx أو .xls</li>
                                                <li>الصف الأول يجب أن يحتوي على أسماء الأعمدة</li>
                                                <li>الأعمدة المطلوبة: الرقم الوظيفي، الاسم الكامل، المنصب، الدرجة الوظيفية، الراتب الأساسي</li>
                                            </ul>
                                        </div>

                                        <h6 class="mt-4 mb-3"><i class="fas fa-table me-2"></i>تنسيق الأعمدة المطلوب:</h6>
                                        <div class="table-responsive">
                                            <table class="table table-bordered table-sm">
                                                <thead class="table-dark">
                                                    <tr>
                                                        <th>اسم العمود</th>
                                                        <th>مطلوب</th>
                                                        <th>مثال</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td><strong>الرقم الوظيفي</strong></td>
                                                        <td><span class="badge bg-danger">مطلوب</span></td>
                                                        <td>12001</td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong>الاسم الكامل</strong></td>
                                                        <td><span class="badge bg-danger">مطلوب</span></td>
                                                        <td>أحمد محمد علي</td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong>المنصب</strong></td>
                                                        <td><span class="badge bg-danger">مطلوب</span></td>
                                                        <td>محاسب أول</td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong>الدرجة الوظيفية</strong></td>
                                                        <td><span class="badge bg-danger">مطلوب</span></td>
                                                        <td>الثالثة</td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong>الراتب الأساسي</strong></td>
                                                        <td><span class="badge bg-danger">مطلوب</span></td>
                                                        <td>750000</td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong>الشهادة</strong></td>
                                                        <td><span class="badge bg-secondary">اختياري</span></td>
                                                        <td>بكالوريوس</td>
                                                    </tr>
                                                    <tr>
                                                        <td><strong>رقم الهاتف</strong></td>
                                                        <td><span class="badge bg-secondary">اختياري</span></td>
                                                        <td>07901234567</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>

                                <!-- نموذج رفع الملف -->
                                <div class="card">
                                    <div class="card-header bg-success text-white">
                                        <h5 class="mb-0"><i class="fas fa-upload me-2"></i>رفع ملف Excel</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-4">
                                            <label class="form-label"><i class="fas fa-file-excel me-1"></i>اختر ملف Excel</label>
                                            <input type="file" class="form-control" accept=".xlsx,.xls" disabled>
                                            <div class="form-text">الحد الأقصى لحجم الملف: 10 MB | الصيغ المدعومة: .xlsx, .xls</div>
                                        </div>

                                        <div class="mb-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" checked disabled>
                                                <label class="form-check-label">تجاهل الموظفين المكررين</label>
                                            </div>
                                        </div>

                                        <div class="mb-4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" checked disabled>
                                                <label class="form-check-label">إضافة الاستقطاعات الافتراضية</label>
                                            </div>
                                        </div>

                                        <button class="btn btn-success btn-lg me-2" disabled>
                                            <i class="fas fa-upload me-1"></i>بدء الاستيراد
                                        </button>
                                        <button class="btn btn-outline-info btn-lg" disabled>
                                            <i class="fas fa-eye me-1"></i>معاينة الملف
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- الأدوات المساعدة -->
                            <div class="col-lg-4">
                                <div class="card">
                                    <div class="card-header bg-primary text-white">
                                        <h5 class="mb-0"><i class="fas fa-download me-2"></i>قالب Excel</h5>
                                    </div>
                                    <div class="card-body">
                                        <p>احصل على قالب Excel جاهز مع جميع الأعمدة المطلوبة.</p>
                                        <button class="btn btn-primary w-100" onclick="alert('سيتم تحميل القالب في النظام الفعلي')">
                                            <i class="fas fa-download me-1"></i>تحميل القالب
                                        </button>
                                    </div>
                                </div>

                                <div class="card">
                                    <div class="card-header bg-warning text-white">
                                        <h5 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>نصائح مهمة</h5>
                                    </div>
                                    <div class="card-body">
                                        <ul class="list-unstyled">
                                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>تأكد من صحة البيانات قبل الاستيراد</li>
                                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>احفظ نسخة احتياطية قبل الاستيراد</li>
                                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>تجنب الأرقام الوظيفية المكررة</li>
                                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>استخدم القالب المتوفر لضمان التوافق</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="text-center mt-4">
                            <p class="text-muted">هذا عرض توضيحي للواجهات. للاستخدام الفعلي، يرجى تشغيل النظام.</p>
                            <button class="btn btn-secondary" onclick="window.close()">إغلاق</button>
                        </div>
                    </div>
                </body>
                </html>
            `);
        }

        function showSampleData() {
            alert(`📊 بيانات تجريبية للنظام:

👥 الموظفون:
• أحمد محمد علي - محاسب أول - 750,000 د.ع
• فاطمة حسن محمود - كاتبة - 580,000 د.ع
• محمد عبد الله أحمد - مدير الشعبة - 950,000 د.ع

💰 المخصصات:
• مخصص منصب: 100,000 د.ع
• مخصص زوجية: 50,000 د.ع
• مخصص أولاد: 25,000 د.ع لكل طفل

📉 الاستقطاعات:
• تقاعد: 10% من الراتب الأساسي
• مساهمة حكومية: 15% من الراتب الأساسي
• ضريبة الدخل: حسب الشرائح

🏦 الحسابات البنكية:
• حساب تشغيلي: 069100011822001
• حساب رواتب: 069100011822002

📁 ملف Excel التجريبي:
• يحتوي على 25 موظف
• جميع الأعمدة المطلوبة والاختيارية
• بيانات صحيحة ومنسقة
• جاهز للاستيراد المباشر`);
        }
        
        // تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            // تحريك البطاقات عند التمرير
            const cards = document.querySelectorAll('.card');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            });
            
            cards.forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(card);
            });
        });
    </script>
</body>
</html>
