// ملف الوظائف المشتركة لجميع القوائم
// Common Functions for All Lists

// وظائف النوافذ المنبثقة
function closeModal(element) {
    const modal = element.closest('div[style*="position: fixed"]');
    if (modal) modal.remove();
}

function showSuccessMessage(message, color = '#28a745') {
    const successModal = `
        <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999; display: flex; align-items: center; justify-content: center;">
            <div style="background: white; border-radius: 12px; padding: 30px; max-width: 400px; width: 90%; text-align: center; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
                <div style="color: ${color}; font-size: 3rem; margin-bottom: 15px;">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h4 style="color: ${color}; margin-bottom: 15px;">تم بنجاح!</h4>
                <p style="color: #6c757d; margin-bottom: 20px;">${message}</p>
                <button onclick="this.closest('div').remove();" style="background: ${color}; color: white; border: none; padding: 10px 20px; border-radius: 8px; cursor: pointer;">
                    موافق
                </button>
            </div>
        </div>
    `;
    document.body.insertAdjacentHTML('beforeend', successModal);
}

function showErrorMessage(message) {
    const errorModal = `
        <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999; display: flex; align-items: center; justify-content: center;">
            <div style="background: white; border-radius: 12px; padding: 30px; max-width: 400px; width: 90%; text-align: center; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
                <div style="color: #dc3545; font-size: 3rem; margin-bottom: 15px;">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <h4 style="color: #dc3545; margin-bottom: 15px;">خطأ!</h4>
                <p style="color: #6c757d; margin-bottom: 20px;">${message}</p>
                <button onclick="this.closest('div').remove();" style="background: #dc3545; color: white; border: none; padding: 10px 20px; border-radius: 8px; cursor: pointer;">
                    موافق
                </button>
            </div>
        </div>
    `;
    document.body.insertAdjacentHTML('beforeend', errorModal);
}

// وظائف التصدير والطباعة
function exportToExcel(tableName, fileName) {
    const table = document.getElementById(tableName);
    if (!table) {
        showErrorMessage('لم يتم العثور على الجدول المطلوب');
        return;
    }
    
    // محاكاة تصدير Excel
    showSuccessMessage(`تم تصدير ${fileName} بنجاح!\nالملف محفوظ في مجلد التحميلات.`);
}

function printTable(tableName, title) {
    const table = document.getElementById(tableName);
    if (!table) {
        showErrorMessage('لم يتم العثور على الجدول المطلوب');
        return;
    }
    
    // محاكاة الطباعة
    showSuccessMessage(`تم إرسال ${title} للطباعة بنجاح!`);
}

// وظائف البحث المحسنة
function enhancedSearch(searchInputId, tableBodyId, columns = []) {
    const searchTerm = document.getElementById(searchInputId).value.toLowerCase();
    const rows = document.querySelectorAll(`#${tableBodyId} tr`);
    let visibleCount = 0;
    
    rows.forEach(row => {
        let matchFound = false;
        
        if (columns.length === 0) {
            // البحث في جميع الأعمدة
            const allText = row.textContent.toLowerCase();
            matchFound = allText.includes(searchTerm);
        } else {
            // البحث في أعمدة محددة
            columns.forEach(colIndex => {
                if (row.cells[colIndex] && row.cells[colIndex].textContent.toLowerCase().includes(searchTerm)) {
                    matchFound = true;
                }
            });
        }
        
        if (matchFound) {
            row.style.display = '';
            visibleCount++;
            // تمييز النص المطابق
            highlightSearchResults(row, searchTerm);
        } else {
            row.style.display = 'none';
        }
    });
    
    updateSearchResultsCount(visibleCount, searchInputId);
}

function highlightSearchResults(row, searchTerm) {
    if (!searchTerm) return;
    
    Array.from(row.cells).forEach(cell => {
        const originalText = cell.getAttribute('data-original') || cell.textContent;
        cell.setAttribute('data-original', originalText);
        
        if (searchTerm) {
            const regex = new RegExp(`(${searchTerm})`, 'gi');
            const highlightedText = originalText.replace(regex, '<mark class="bg-warning">$1</mark>');
            cell.innerHTML = highlightedText;
        } else {
            cell.innerHTML = originalText;
        }
    });
}

function updateSearchResultsCount(count, searchInputId) {
    let countElement = document.getElementById('resultsCount');
    if (!countElement) {
        countElement = document.createElement('small');
        countElement.id = 'resultsCount';
        countElement.className = 'text-muted ms-2 fw-bold';
        countElement.style.transition = 'all 0.3s ease';
        document.getElementById(searchInputId).parentNode.appendChild(countElement);
    }
    
    const totalRows = document.querySelectorAll(`#${searchInputId.replace('search', '').toLowerCase()}TableBody tr`).length;
    if (count === totalRows) {
        countElement.textContent = `(جميع النتائج: ${count})`;
        countElement.className = 'text-success ms-2 fw-bold';
    } else if (count === 0) {
        countElement.textContent = '(لا توجد نتائج)';
        countElement.className = 'text-danger ms-2 fw-bold';
    } else {
        countElement.textContent = `(${count} من ${totalRows} نتيجة)`;
        countElement.className = 'text-primary ms-2 fw-bold';
    }
}

// وظائف التحقق من صحة البيانات
function validateForm(formData, requiredFields) {
    for (let field of requiredFields) {
        if (!formData[field] || formData[field].trim() === '') {
            showErrorMessage(`يرجى ملء الحقل المطلوب: ${field}`);
            return false;
        }
    }
    return true;
}

function checkDuplicateCode(code, tableBodyId) {
    const existingCodes = Array.from(document.querySelectorAll(`#${tableBodyId} tr td:first-child`))
        .map(td => td.textContent.trim());
    return existingCodes.includes(code);
}

// وظائف إنشاء النماذج
function createFormModal(title, fields, onSubmit, color = '#007bff') {
    const fieldsHtml = fields.map(field => {
        if (field.type === 'select') {
            const options = field.options.map(opt => 
                `<option value="${opt.value}">${opt.text}</option>`
            ).join('');
            return `
                <div>
                    <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #495057;">${field.label}</label>
                    <select id="${field.id}" style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'Cairo', sans-serif;" ${field.required ? 'required' : ''}>
                        <option value="">اختر ${field.label}</option>
                        ${options}
                    </select>
                </div>
            `;
        } else if (field.type === 'textarea') {
            return `
                <div>
                    <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #495057;">${field.label}</label>
                    <textarea id="${field.id}" rows="3" placeholder="${field.placeholder || ''}" style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'Cairo', sans-serif; resize: vertical;" ${field.required ? 'required' : ''}></textarea>
                </div>
            `;
        } else {
            return `
                <div>
                    <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #495057;">${field.label}</label>
                    <input type="${field.type || 'text'}" id="${field.id}" placeholder="${field.placeholder || ''}" style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'Cairo', sans-serif;" ${field.required ? 'required' : ''}>
                </div>
            `;
        }
    }).join('');
    
    const modal = `
        <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999; display: flex; align-items: center; justify-content: center;" onclick="this.remove()">
            <div style="background: white; border-radius: 12px; padding: 30px; max-width: 700px; width: 90%; max-height: 80%; overflow-y: auto; box-shadow: 0 10px 30px rgba(0,0,0,0.3);" onclick="event.stopPropagation()">
                <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 20px; border-bottom: 2px solid ${color}; padding-bottom: 15px;">
                    <h3 style="color: ${color}; margin: 0; display: flex; align-items: center;">
                        <i class="fas fa-plus-circle" style="margin-left: 10px;"></i>
                        ${title}
                    </h3>
                    <button onclick="closeModal(this)" style="background: #dc3545; color: white; border: none; border-radius: 50%; width: 30px; height: 30px; cursor: pointer; transition: all 0.3s ease; display: flex; align-items: center; justify-content: center;" onmouseover="this.style.background='#c82333'; this.style.transform='scale(1.1)'" onmouseout="this.style.background='#dc3545'; this.style.transform='scale(1)'" title="إغلاق النافذة">×</button>
                </div>

                <form style="display: grid; gap: 15px;">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                        ${fieldsHtml}
                    </div>

                    <div style="display: flex; gap: 10px; justify-content: center; margin-top: 20px;">
                        <button type="button" onclick="${onSubmit}" style="background: ${color}; color: white; border: none; padding: 12px 25px; border-radius: 8px; cursor: pointer; display: flex; align-items: center; gap: 5px;">
                            <i class="fas fa-plus"></i> إضافة
                        </button>
                        <button type="button" onclick="closeModal(this)" style="background: #6c757d; color: white; border: none; padding: 12px 25px; border-radius: 8px; cursor: pointer; transition: all 0.3s ease; display: flex; align-items: center; gap: 5px;" onmouseover="this.style.background='#dc3545'; this.style.transform='translateY(-2px)'" onmouseout="this.style.background='#6c757d'; this.style.transform='translateY(0)'">
                            <i class="fas fa-times"></i> إلغاء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', modal);
}

// وظائف إنشاء الصفوف
function createTableRow(data, columns, actions) {
    const row = document.createElement('tr');
    
    const cellsHtml = columns.map(col => {
        let cellContent = data[col.field];
        
        if (col.type === 'badge') {
            const badgeClass = col.badgeClass ? col.badgeClass(cellContent) : 'bg-primary';
            cellContent = `<span class="badge ${badgeClass}">${cellContent}</span>`;
        } else if (col.type === 'strong') {
            cellContent = `<strong>${cellContent}</strong>`;
        }
        
        return `<td>${cellContent}</td>`;
    }).join('');
    
    const actionsHtml = actions.map(action => 
        `<button class="btn btn-outline-${action.color} btn-sm" title="${action.title}" onclick="${action.onclick}">
            <i class="fas fa-${action.icon}"></i>
        </button>`
    ).join('');
    
    row.innerHTML = cellsHtml + `<td class="action-buttons">${actionsHtml}</td>`;
    
    return row;
}

console.log('✅ ملف الوظائف المشتركة محمل بنجاح');
