// ملف تفعيل جميع القوائم - Lists Activator
// يحتوي على الوظائف الرئيسية لتفعيل جميع القوائم

class ListManager {
    constructor(listName) {
        this.listName = listName;
        this.config = getListConfig(listName);
        if (!this.config) {
            console.error(`تكوين القائمة غير موجود: ${listName}`);
            return;
        }
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadSampleData();
        console.log(`✅ تم تفعيل قائمة ${this.config.title}`);
    }

    setupEventListeners() {
        // ربط أحداث البحث
        const searchInput = document.getElementById(this.config.searchId);
        if (searchInput) {
            searchInput.addEventListener('input', () => {
                enhancedSearch(this.config.searchId, this.config.tableId, [0, 1, 2]);
            });
        }

        // ربط أحداث الفلترة
        const filters = document.querySelectorAll('select[id*="Filter"]');
        filters.forEach(filter => {
            filter.addEventListener('change', () => this.applyFilters());
        });
    }

    loadSampleData() {
        const sampleData = SAMPLE_DATA[this.listName];
        if (sampleData && sampleData.length > 0) {
            const tableBody = document.getElementById(this.config.tableId);
            if (tableBody) {
                sampleData.forEach(item => {
                    const row = createTableRow(item, this.config.columns, this.config.actions.map(action => ({
                        ...action,
                        onclick: `${action.onclick}('${item.code}')`
                    })));
                    tableBody.appendChild(row);
                });
            }
        }
    }

    showAddModal() {
        createFormModal(
            `إضافة ${this.config.title.replace('إدارة ', '')} جديد`,
            this.config.fields,
            `addNew${this.listName.charAt(0).toUpperCase() + this.listName.slice(1)}()`,
            this.config.color
        );
    }

    addNewItem() {
        const formData = {};
        let isValid = true;

        // جمع البيانات من النموذج
        this.config.fields.forEach(field => {
            const element = document.getElementById(field.id);
            if (element) {
                formData[field.id] = element.value;
                if (field.required && !element.value.trim()) {
                    isValid = false;
                }
            }
        });

        if (!isValid) {
            showErrorMessage('يرجى ملء جميع الحقول المطلوبة');
            return;
        }

        // التحقق من عدم تكرار الرمز
        const codeField = this.config.fields[0]; // أول حقل عادة هو الرمز
        if (checkDuplicateCode(formData[codeField.id], this.config.tableId)) {
            showErrorMessage('الرمز موجود مسبقاً. يرجى اختيار رمز آخر.');
            return;
        }

        // إنشاء البيانات للصف الجديد
        const newItemData = this.mapFormDataToTableData(formData);
        
        // إضافة الصف للجدول
        const newRow = createTableRow(newItemData, this.config.columns, this.config.actions.map(action => ({
            ...action,
            onclick: `${action.onclick}('${newItemData.code}')`
        })));
        
        document.getElementById(this.config.tableId).appendChild(newRow);

        // إغلاق النافذة وإظهار رسالة نجاح
        const modal = document.querySelector('div[style*="position: fixed"]');
        if (modal) modal.remove();
        
        showSuccessMessage(`تم إضافة ${newItemData.name} بنجاح!`, this.config.color);
    }

    mapFormDataToTableData(formData) {
        // تحويل بيانات النموذج إلى بيانات الجدول
        const mapped = {};
        
        // تعيين القيم الأساسية
        this.config.fields.forEach((field, index) => {
            const columnField = this.config.columns[index]?.field;
            if (columnField) {
                mapped[columnField] = formData[field.id];
            }
        });

        // إضافة قيم افتراضية
        if (!mapped.employeeCount) mapped.employeeCount = 0;
        if (!mapped.status) mapped.status = 'نشط';
        if (!mapped.branches) mapped.branches = 1;

        return mapped;
    }

    applyFilters() {
        const rows = document.querySelectorAll(`#${this.config.tableId} tr`);
        const filters = {};
        
        // جمع قيم الفلاتر
        document.querySelectorAll('select[id*="Filter"]').forEach(filter => {
            if (filter.value) {
                filters[filter.id] = filter.value;
            }
        });

        // تطبيق الفلاتر
        rows.forEach(row => {
            let shouldShow = true;
            
            Object.entries(filters).forEach(([filterId, filterValue]) => {
                const columnIndex = this.getColumnIndexForFilter(filterId);
                if (columnIndex >= 0 && row.cells[columnIndex]) {
                    const cellText = row.cells[columnIndex].textContent.trim();
                    if (!cellText.includes(filterValue)) {
                        shouldShow = false;
                    }
                }
            });

            row.style.display = shouldShow ? '' : 'none';
        });
    }

    getColumnIndexForFilter(filterId) {
        // تحديد فهرس العمود بناءً على معرف الفلتر
        const filterMap = {
            'typeFilter': 2,
            'regionFilter': 2,
            'levelFilter': 2,
            'statusFilter': -1 // آخر عمود عادة
        };
        
        return filterMap[filterId] || -1;
    }

    exportData() {
        exportToExcel(this.config.tableId.replace('Body', ''), `${this.config.title}.xlsx`);
    }

    printData() {
        printTable(this.config.tableId.replace('Body', ''), this.config.title);
    }
}

// وظائف عامة للعرض والتعديل
function viewItem(listName, code) {
    const config = getListConfig(listName);
    if (!config) return;
    
    showSuccessMessage(`عرض تفاصيل ${config.title.replace('إدارة ', '')}: ${code}`, config.color);
}

function editItem(listName, code) {
    const config = getListConfig(listName);
    if (!config) return;
    
    showSuccessMessage(`تعديل ${config.title.replace('إدارة ', '')}: ${code}`, config.color);
}

function viewItemEmployees(listName, code) {
    const config = getListConfig(listName);
    if (!config) return;
    
    showSuccessMessage(`عرض موظفي ${config.title.replace('إدارة ', '')}: ${code}`, config.color);
}

// وظائف محددة لكل قائمة
function showAddDepartmentModal() { window.departmentManager?.showAddModal(); }
function addNewDepartments() { window.departmentManager?.addNewItem(); }
function viewDepartment(code) { viewItem('departments', code); }
function editDepartment(code) { editItem('departments', code); }
function viewDepartmentEmployees(code) { viewItemEmployees('departments', code); }

function showAddProvinceModal() { window.provinceManager?.showAddModal(); }
function addNewProvinces() { window.provinceManager?.addNewItem(); }
function viewProvince(code) { viewItem('provinces', code); }
function editProvince(code) { editItem('provinces', code); }
function viewProvinceEmployees(code) { viewItemEmployees('provinces', code); }

function showAddBankModal() { window.bankManager?.showAddModal(); }
function addNewBanks() { window.bankManager?.addNewItem(); }
function viewBank(code) { viewItem('banks', code); }
function editBank(code) { editItem('banks', code); }
function viewBankBranches(code) { viewItemEmployees('banks', code); }

function showAddGradeModal() { window.gradeManager?.showAddModal(); }
function addNewGrades() { window.gradeManager?.addNewItem(); }
function viewGrade(code) { viewItem('grades', code); }
function editGrade(code) { editItem('grades', code); }
function viewGradeEmployees(code) { viewItemEmployees('grades', code); }

function showAddStageModal() { window.stageManager?.showAddModal(); }
function addNewStages() { window.stageManager?.addNewItem(); }
function viewStage(code) { viewItem('stages', code); }
function editStage(code) { editItem('stages', code); }
function viewStageEmployees(code) { viewItemEmployees('stages', code); }

// تفعيل القوائم عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تحديد نوع الصفحة وتفعيل المدير المناسب
    const currentPage = window.location.pathname.split('/').pop().replace('.html', '');
    
    if (currentPage.includes('departments')) {
        window.departmentManager = new ListManager('departments');
    } else if (currentPage.includes('provinces')) {
        window.provinceManager = new ListManager('provinces');
    } else if (currentPage.includes('banks')) {
        window.bankManager = new ListManager('banks');
    } else if (currentPage.includes('grades')) {
        window.gradeManager = new ListManager('grades');
    } else if (currentPage.includes('stages')) {
        window.stageManager = new ListManager('stages');
    }
    
    console.log(`🚀 تم تفعيل مدير القائمة للصفحة: ${currentPage}`);
});

console.log('✅ ملف تفعيل القوائم محمل بنجاح');
