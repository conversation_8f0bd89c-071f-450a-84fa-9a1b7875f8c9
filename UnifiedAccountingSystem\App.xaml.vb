Imports System.Windows
Imports System.Globalization
Imports System.Threading
Imports System.Configuration
Imports UnifiedAccountingSystem.Views

Class Application

    Protected Overrides Sub OnStartup(e As StartupEventArgs)
        MyBase.OnStartup(e)

        ' تعيين الثقافة العربية
        SetArabicCulture()

        ' تهيئة التطبيق
        InitializeApplication()

        ' عرض نافذة تسجيل الدخول
        ShowLoginWindow()
    End Sub

    Private Sub SetArabicCulture()
        ' تعيين الثقافة العربية العراقية
        Dim culture As New CultureInfo("ar-IQ")
        Thread.CurrentThread.CurrentCulture = culture
        Thread.CurrentThread.CurrentUICulture = culture

        ' تعيين اتجاه النص من اليمين إلى اليسار
        FrameworkElement.FlowDirectionProperty.OverrideMetadata(
            GetType(FrameworkElement),
            New FrameworkPropertyMetadata(FlowDirection.RightToLeft))
    End Sub

    Private Sub InitializeApplication()
        ' تهيئة مجلدات التطبيق
        CreateApplicationDirectories()

        ' تهيئة نظام التسجيل
        InitializeLogging()

        ' تحميل الإعدادات
        LoadApplicationSettings()
    End Sub

    Private Sub CreateApplicationDirectories()
        Try
            ' إنشاء المجلدات المطلوبة
            Dim directories() As String = {
                "Data",
                "Reports",
                "Exports",
                "Backups",
                "Logs",
                "Temp"
            }

            For Each directory In directories
                If Not IO.Directory.Exists(directory) Then
                    IO.Directory.CreateDirectory(directory)
                End If
            Next

        Catch ex As Exception
            MessageBox.Show($"خطأ في إنشاء مجلدات التطبيق: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error)
        End Try
    End Sub

    Private Sub InitializeLogging()
        Try
            ' تهيئة نظام التسجيل
            Dim logFile As String = $"Logs\Application_{DateTime.Now:yyyyMMdd}.log"
            System.Diagnostics.Trace.WriteLine($"تم بدء تشغيل التطبيق في: {DateTime.Now}")

        Catch ex As Exception
            ' تجاهل أخطاء التسجيل
        End Try
    End Sub

    Private Sub LoadApplicationSettings()
        Try
            ' تحميل إعدادات التطبيق
            Dim appName = ConfigurationManager.AppSettings("ApplicationName")
            Dim version = ConfigurationManager.AppSettings("ApplicationVersion")

            System.Diagnostics.Trace.WriteLine($"تطبيق: {appName} - الإصدار: {version}")

        Catch ex As Exception
            System.Diagnostics.Trace.WriteLine($"خطأ في تحميل الإعدادات: {ex.Message}")
        End Try
    End Sub

    Private Sub ShowLoginWindow()
        ' إنشاء وعرض نافذة تسجيل الدخول
        Dim loginWindow As New LoginView()
        loginWindow.Show()
    End Sub

    Protected Overrides Sub OnExit(e As ExitEventArgs)
        ' تنظيف الموارد عند إغلاق التطبيق
        CleanupApplication()
        MyBase.OnExit(e)
    End Sub

    Private Sub CleanupApplication()
        Try
            ' حفظ الإعدادات
            My.Settings.Save()

            ' تسجيل إغلاق التطبيق
            System.Diagnostics.Trace.WriteLine($"تم إغلاق التطبيق في: {DateTime.Now}")

            ' تنظيف الملفات المؤقتة
            CleanupTempFiles()

        Catch ex As Exception
            ' تجاهل الأخطاء عند الإغلاق
        End Try
    End Sub

    Private Sub CleanupTempFiles()
        Try
            Dim tempDir As String = "Temp"
            If IO.Directory.Exists(tempDir) Then
                For Each file In IO.Directory.GetFiles(tempDir)
                    Try
                        IO.File.Delete(file)
                    Catch
                        ' تجاهل أخطاء حذف الملفات المؤقتة
                    End Try
                Next
            End If
        Catch
            ' تجاهل الأخطاء
        End Try
    End Sub
End Class
