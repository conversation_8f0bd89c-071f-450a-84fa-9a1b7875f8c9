Imports System.Windows

Class Application

    Protected Overrides Sub OnStartup(e As StartupEventArgs)
        MyBase.OnStartup(e)
        
        ' تعيين الثقافة للغة العربية
        Thread.CurrentThread.CurrentUICulture = New Globalization.CultureInfo("ar-IQ")
        Thread.CurrentThread.CurrentCulture = New Globalization.CultureInfo("ar-IQ")
        
        ' تعيين اتجاه الواجهة من اليمين إلى اليسار
        FrameworkElement.FlowDirectionProperty.OverrideMetadata(
            GetType(FrameworkElement),
            New FrameworkPropertyMetadata(FlowDirection.RightToLeft))
    End Sub

End Class
