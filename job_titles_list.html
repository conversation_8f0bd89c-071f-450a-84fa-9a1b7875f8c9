<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>العناوين الوظيفية - نظام المحاسبة المتكامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: #f8f9fa;
        }
        
        .navbar {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .card {
            border: none;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
            border-radius: 12px;
        }
        
        .table th {
            background: #343a40;
            color: white;
            text-align: center;
            border: none;
        }
        
        .table td {
            vertical-align: middle;
            text-align: center;
        }
        
        .category-badge {
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .cat-medical {
            background: #e3f2fd;
            color: #1976d2;
            border: 1px solid #bbdefb;
        }
        
        .cat-accounting {
            background: #f3e5f5;
            color: #7b1fa2;
            border: 1px solid #e1bee7;
        }
        
        .cat-administrative {
            background: #fff3e0;
            color: #f57c00;
            border: 1px solid #ffcc02;
        }
        
        .cat-technical {
            background: #e8f5e8;
            color: #388e3c;
            border: 1px solid #c8e6c9;
        }
        
        .cat-legal {
            background: #fce4ec;
            color: #c2185b;
            border: 1px solid #f8bbd9;
        }
        
        .search-box {
            border-radius: 25px;
            border: 2px solid #e9ecef;
            padding: 10px 20px;
        }
        
        .search-box:focus {
            border-color: #28a745;
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
        }
        
        .action-buttons .btn {
            margin: 2px;
        }
        
        .title-hierarchy {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .hierarchy-level {
            width: 20px;
            height: 3px;
            background: #dee2e6;
            border-radius: 2px;
        }
        
        .hierarchy-level.active {
            background: #28a745;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="simple_homepage.html">
                <i class="fas fa-calculator me-2"></i>
                نظام المحاسبة المتكامل
            </a>
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="simple_homepage.html">
                    <i class="fas fa-home me-1"></i>الرئيسية
                </a>
                <a class="nav-link" href="employees_page.html">
                    <i class="fas fa-users me-1"></i>الموظفون
                </a>
                <a class="nav-link" href="basic_lists_page.html">
                    <i class="fas fa-list me-1"></i>القوائم الأساسية
                </a>
                <a class="nav-link active" href="job_titles_list.html">
                    <i class="fas fa-tags me-1"></i>العناوين الوظيفية
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- عنوان الصفحة -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2 class="text-success">
                        <i class="fas fa-tags me-2"></i>
                        العناوين الوظيفية
                    </h2>
                    <div>
                        <button class="btn btn-success me-2" onclick="showAddTitleModal()">
                            <i class="fas fa-plus me-1"></i>إضافة عنوان
                        </button>
                        <a href="basic_lists_page.html" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right me-1"></i>العودة للقوائم
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات التصنيفات -->
        <div class="row mb-4">
            <div class="col-md-2">
                <div class="card text-center" style="border-left: 4px solid #1976d2;">
                    <div class="card-body">
                        <h4 class="text-primary">5</h4>
                        <small>طبية</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center" style="border-left: 4px solid #7b1fa2;">
                    <div class="card-body">
                        <h4 class="text-purple">4</h4>
                        <small>محاسبية</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center" style="border-left: 4px solid #f57c00;">
                    <div class="card-body">
                        <h4 class="text-warning">6</h4>
                        <small>إدارية</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center" style="border-left: 4px solid #388e3c;">
                    <div class="card-body">
                        <h4 class="text-success">2</h4>
                        <small>تقنية</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center" style="border-left: 4px solid #c2185b;">
                    <div class="card-body">
                        <h4 class="text-danger">1</h4>
                        <small>قانونية</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card text-center bg-primary text-white">
                    <div class="card-body">
                        <h4>18</h4>
                        <small>المجموع</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- شريط البحث والفلترة -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-search"></i>
                                    </span>
                                    <input type="text" class="form-control search-box" placeholder="البحث عن عنوان وظيفي..." id="searchInput">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="categoryFilter">
                                    <option value="">جميع التصنيفات</option>
                                    <option value="طبية">طبية</option>
                                    <option value="محاسبية">محاسبية</option>
                                    <option value="إدارية">إدارية</option>
                                    <option value="تقنية">تقنية</option>
                                    <option value="قانونية">قانونية</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="statusFilter">
                                    <option value="">جميع الحالات</option>
                                    <option value="نشط">نشط</option>
                                    <option value="معطل">معطل</option>
                                    <option value="مؤقت">مؤقت</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button class="btn btn-outline-secondary w-100" onclick="clearFilters()">
                                    <i class="fas fa-times me-1"></i>مسح
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- جدول العناوين الوظيفية -->
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>قائمة العناوين الوظيفية
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0" id="titlesTable">
                        <thead>
                            <tr>
                                <th>الرمز</th>
                                <th>العنوان الوظيفي</th>
                                <th>التصنيف</th>
                                <th>المستوى</th>
                                <th>الوصف</th>
                                <th>عدد الموظفين</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="titlesTableBody">
                            <tr>
                                <td><strong>JT001</strong></td>
                                <td>طبيب اختصاص أول</td>
                                <td><span class="category-badge cat-medical">طبية</span></td>
                                <td>
                                    <div class="title-hierarchy">
                                        <div class="hierarchy-level active"></div>
                                        <div class="hierarchy-level active"></div>
                                        <div class="hierarchy-level active"></div>
                                        <div class="hierarchy-level active"></div>
                                        <div class="hierarchy-level"></div>
                                    </div>
                                </td>
                                <td>طبيب متخصص في مجال معين</td>
                                <td><span class="badge bg-info">2</span></td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewTitle('JT001')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editTitle('JT001')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="الموظفين" onclick="viewTitleEmployees('JT001')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>JT002</strong></td>
                                <td>طبيب عام</td>
                                <td><span class="category-badge cat-medical">طبية</span></td>
                                <td>
                                    <div class="title-hierarchy">
                                        <div class="hierarchy-level active"></div>
                                        <div class="hierarchy-level active"></div>
                                        <div class="hierarchy-level active"></div>
                                        <div class="hierarchy-level"></div>
                                        <div class="hierarchy-level"></div>
                                    </div>
                                </td>
                                <td>طبيب عام متخرج حديثاً</td>
                                <td><span class="badge bg-info">4</span></td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewTitle('JT002')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editTitle('JT002')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="الموظفين" onclick="viewTitleEmployees('JT002')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>JT003</strong></td>
                                <td>محاسب قانوني</td>
                                <td><span class="category-badge cat-accounting">محاسبية</span></td>
                                <td>
                                    <div class="title-hierarchy">
                                        <div class="hierarchy-level active"></div>
                                        <div class="hierarchy-level active"></div>
                                        <div class="hierarchy-level active"></div>
                                        <div class="hierarchy-level active"></div>
                                        <div class="hierarchy-level"></div>
                                    </div>
                                </td>
                                <td>محاسب مرخص ومعتمد</td>
                                <td><span class="badge bg-info">1</span></td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewTitle('JT003')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editTitle('JT003')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="الموظفين" onclick="viewTitleEmployees('JT003')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>JT004</strong></td>
                                <td>محاسب أول</td>
                                <td><span class="category-badge cat-accounting">محاسبية</span></td>
                                <td>
                                    <div class="title-hierarchy">
                                        <div class="hierarchy-level active"></div>
                                        <div class="hierarchy-level active"></div>
                                        <div class="hierarchy-level active"></div>
                                        <div class="hierarchy-level"></div>
                                        <div class="hierarchy-level"></div>
                                    </div>
                                </td>
                                <td>محاسب بخبرة متقدمة</td>
                                <td><span class="badge bg-info">3</span></td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewTitle('JT004')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editTitle('JT004')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="الموظفين" onclick="viewTitleEmployees('JT004')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>JT005</strong></td>
                                <td>مدير إداري</td>
                                <td><span class="category-badge cat-administrative">إدارية</span></td>
                                <td>
                                    <div class="title-hierarchy">
                                        <div class="hierarchy-level active"></div>
                                        <div class="hierarchy-level active"></div>
                                        <div class="hierarchy-level active"></div>
                                        <div class="hierarchy-level active"></div>
                                        <div class="hierarchy-level active"></div>
                                    </div>
                                </td>
                                <td>مسؤول عن الشؤون الإدارية</td>
                                <td><span class="badge bg-info">1</span></td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewTitle('JT005')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editTitle('JT005')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="الموظفين" onclick="viewTitleEmployees('JT005')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>JT006</strong></td>
                                <td>كاتب أول</td>
                                <td><span class="category-badge cat-administrative">إدارية</span></td>
                                <td>
                                    <div class="title-hierarchy">
                                        <div class="hierarchy-level active"></div>
                                        <div class="hierarchy-level active"></div>
                                        <div class="hierarchy-level"></div>
                                        <div class="hierarchy-level"></div>
                                        <div class="hierarchy-level"></div>
                                    </div>
                                </td>
                                <td>كاتب بخبرة في الأعمال الإدارية</td>
                                <td><span class="badge bg-info">4</span></td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewTitle('JT006')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editTitle('JT006')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="الموظفين" onclick="viewTitleEmployees('JT006')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>JT007</strong></td>
                                <td>مطور أنظمة</td>
                                <td><span class="category-badge cat-technical">تقنية</span></td>
                                <td>
                                    <div class="title-hierarchy">
                                        <div class="hierarchy-level active"></div>
                                        <div class="hierarchy-level active"></div>
                                        <div class="hierarchy-level active"></div>
                                        <div class="hierarchy-level"></div>
                                        <div class="hierarchy-level"></div>
                                    </div>
                                </td>
                                <td>مختص في تطوير البرمجيات</td>
                                <td><span class="badge bg-info">1</span></td>
                                <td><span class="badge bg-warning">مؤقت</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewTitle('JT007')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editTitle('JT007')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="الموظفين" onclick="viewTitleEmployees('JT007')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>JT008</strong></td>
                                <td>مستشار قانوني</td>
                                <td><span class="category-badge cat-legal">قانونية</span></td>
                                <td>
                                    <div class="title-hierarchy">
                                        <div class="hierarchy-level active"></div>
                                        <div class="hierarchy-level active"></div>
                                        <div class="hierarchy-level active"></div>
                                        <div class="hierarchy-level active"></div>
                                        <div class="hierarchy-level"></div>
                                    </div>
                                </td>
                                <td>مختص في الشؤون القانونية</td>
                                <td><span class="badge bg-info">1</span></td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewTitle('JT008')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editTitle('JT008')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="الموظفين" onclick="viewTitleEmployees('JT008')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between align-items-center">
                    <span class="text-muted">عرض 8 من أصل 18 عنوان وظيفي</span>
                    <div>
                        <button class="btn btn-outline-success btn-sm me-2" onclick="exportTitles()">
                            <i class="fas fa-file-excel me-1"></i>تصدير Excel
                        </button>
                        <button class="btn btn-outline-info btn-sm" onclick="printTitles()">
                            <i class="fas fa-print me-1"></i>طباعة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // وظائف البحث والفلترة
        function filterTitles() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const categoryFilter = document.getElementById('categoryFilter').value;
            const statusFilter = document.getElementById('statusFilter').value;
            
            const rows = document.querySelectorAll('#titlesTableBody tr');
            
            rows.forEach(row => {
                const title = row.cells[1].textContent.toLowerCase();
                const category = row.cells[2].textContent;
                const status = row.cells[6].textContent;
                
                const matchesSearch = title.includes(searchTerm);
                const matchesCategory = !categoryFilter || category.includes(categoryFilter);
                const matchesStatus = !statusFilter || status.includes(statusFilter);
                
                if (matchesSearch && matchesCategory && matchesStatus) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }
        
        function clearFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('categoryFilter').value = '';
            document.getElementById('statusFilter').value = '';
            filterTitles();
        }
        
        // ربط أحداث البحث
        document.getElementById('searchInput').addEventListener('input', filterTitles);
        document.getElementById('categoryFilter').addEventListener('change', filterTitles);
        document.getElementById('statusFilter').addEventListener('change', filterTitles);
        
        // وظائف الإجراءات
        function viewTitle(code) {
            alert(`عرض تفاصيل العنوان الوظيفي: ${code}\n\nسيتم عرض:\n• الوصف التفصيلي\n• المتطلبات والمؤهلات\n• المهام والمسؤوليات\n• مسار التطوير المهني\n• العلاقة مع العناوين الأخرى`);
        }
        
        function editTitle(code) {
            alert(`تعديل العنوان الوظيفي: ${code}\n\nيمكن تعديل:\n• اسم العنوان\n• التصنيف\n• المستوى الهرمي\n• الوصف\n• المتطلبات\n• الحالة`);
        }
        
        function viewTitleEmployees(code) {
            alert(`موظفو العنوان الوظيفي: ${code}\n\nسيتم عرض:\n• قائمة الموظفين\n• تفاصيل الخبرات\n• تقييمات الأداء\n• خطط التطوير\n• الترقيات المحتملة`);
        }
        
        function showAddTitleModal() {
            alert(`إضافة عنوان وظيفي جديد\n\nالحقول المطلوبة:\n• رمز العنوان\n• اسم العنوان\n• التصنيف\n• المستوى الهرمي\n• الوصف التفصيلي\n• المتطلبات\n• الحالة`);
        }
        
        function exportTitles() {
            alert(`📊 تصدير العناوين الوظيفية\n\nسيتم إنشاء ملف Excel يحتوي على:\n• جميع العناوين الوظيفية\n• التصنيفات والمستويات\n• الإحصائيات\n• المخططات الهرمية\n\nحجم الملف المتوقع: 800 KB`);
        }
        
        function printTitles() {
            alert(`🖨️ طباعة العناوين الوظيفية\n\nسيتم إعداد:\n• دليل العناوين الوظيفية\n• التصنيفات والهرمية\n• الوصف التفصيلي\n• المخططات البيانية\n\nعدد الصفحات المتوقع: 12 صفحة`);
        }

        console.log('✅ صفحة العناوين الوظيفية محملة بنجاح');
        console.log('🏷️ 18 عنوان وظيفي متاح');
        console.log('📊 5 تصنيفات مختلفة');
    </script>
</body>
</html>
