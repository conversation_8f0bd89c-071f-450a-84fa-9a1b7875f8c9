Namespace Models
    Public Class User
        Public Property UserId As Integer
        Public Property Username As String
        Public Property Password As String
        Public Property FullName As String
        Public Property UserType As UserType
        Public Property DepartmentId As Integer
        Public Property IsActive As Boolean
        Public Property LastLoginDate As DateTime?
        Public Property CreatedDate As DateTime
        Public Property ModifiedDate As DateTime?
    End Class

    Public Enum UserType
        Administrator
        User
    End Enum
End Namespace
