Namespace Models
    Public Class User
        Public Property UserId As Integer
        Public Property Username As String
        Public Property Password As String
        Public Property FullName As String
        Public Property Email As String
        Public Property Role As String
        Public Property UserType As UserType
        Public Property DepartmentId As Integer
        Public Property Department As String
        Public Property Position As String
        Public Property PhoneNumber As String
        Public Property IsActive As Boolean
        Public Property LastLoginDate As DateTime?
        Public Property CreatedDate As DateTime
        Public Property ModifiedDate As DateTime?
        Public Property FailedLoginAttempts As Integer
        Public Property IsLocked As Boolean
        Public Property LockoutEndTime As DateTime?

        ' خصائص إضافية للأمان
        Public Property PasswordLastChanged As DateTime?
        Public Property MustChangePassword As Boolean
        Public Property TwoFactorEnabled As Boolean

        ' معلومات الجلسة
        Public Property SessionId As String
        Public Property LastActivity As DateTime?
        Public Property IPAddress As String

        ' الصلاحيات
        Public Property Permissions As List(Of String)

        Public Sub New()
            Permissions = New List(Of String)
            CreatedDate = DateTime.Now
            IsActive = True
            FailedLoginAttempts = 0
            IsLocked = False
            MustChangePassword = False
            TwoFactorEnabled = False
        End Sub

        ' دوال مساعدة
        Public Function HasPermission(permission As String) As Boolean
            Return Permissions.Contains(permission) OrElse Role = "Administrator"
        End Function

        Public Function IsAccountLocked() As Boolean
            Return IsLocked AndAlso (LockoutEndTime Is Nothing OrElse LockoutEndTime > DateTime.Now)
        End Function

        Public Sub RecordFailedLogin()
            FailedLoginAttempts += 1
            If FailedLoginAttempts >= 5 Then
                IsLocked = True
                LockoutEndTime = DateTime.Now.AddMinutes(30) ' قفل لمدة 30 دقيقة
            End If
        End Sub

        Public Sub RecordSuccessfulLogin()
            FailedLoginAttempts = 0
            IsLocked = False
            LockoutEndTime = Nothing
            LastLoginDate = DateTime.Now
            LastActivity = DateTime.Now
        End Sub
    End Class

    Public Enum UserType
        Administrator
        Manager
        Accountant
        HRSpecialist
        DataEntry
        Viewer
    End Enum

    ' تعداد الصلاحيات
    Public Enum Permission
        ViewEmployees
        AddEmployee
        EditEmployee
        DeleteEmployee
        ViewSalaries
        CalculateSalaries
        ApproveSalaries
        ViewReports
        GenerateReports
        ManageUsers
        SystemSettings
        BackupRestore
        AuditLogs
    End Enum
End Namespace
