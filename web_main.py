# -*- coding: utf-8 -*-
"""
تطبيق الويب الرئيسي
Main Web Application using FastAPI
"""

from fastapi import FastAPI, Request, Depends, HTTPException, status, Form, File, UploadFile
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
import uvicorn
from pathlib import Path

# استيراد الإعدادات والنماذج
from config.settings import WEB_CONFIG, TEMPLATES_DIR, STATIC_DIR, create_directories
from database.database_manager import db_manager
from models.user import User
from models.employee import Employee
from models.salary import MonthlySalary
from models.bank_account import BankAccount
from utils.arabic_support import arabic_support

# إنشاء التطبيق
app = FastAPI(
    title="نظام المحاسبة المتكامل",
    description="نظام إدارة رواتب الموظفين والمصروفات للمؤسسات الحكومية",
    version="1.0.0"
)

# إعداد الملفات الثابتة والقوالب
create_directories()
app.mount("/static", StaticFiles(directory=str(STATIC_DIR)), name="static")
templates = Jinja2Templates(directory=str(TEMPLATES_DIR))

# إعداد الأمان
security = HTTPBearer()

# دالة للحصول على جلسة قاعدة البيانات
def get_db():
    session = db_manager.get_session()
    try:
        yield session
    finally:
        session.close()

# دالة للتحقق من المصادقة
def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security), db: Session = Depends(get_db)):
    # هنا يمكن إضافة التحقق من JWT token
    # للبساية، سنستخدم session بسيط
    return None

# الصفحة الرئيسية
@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    return templates.TemplateResponse("index.html", {
        "request": request,
        "title": "نظام المحاسبة المتكامل",
        "organization": "وزارة الشباب والرياضة - دائرة الطب الرياضي"
    })

# صفحة تسجيل الدخول
@app.get("/login", response_class=HTMLResponse)
async def login_page(request: Request):
    return templates.TemplateResponse("login.html", {
        "request": request,
        "title": "تسجيل الدخول"
    })

@app.post("/login")
async def login(
    request: Request,
    username: str = Form(...),
    password: str = Form(...),
    db: Session = Depends(get_db)
):
    # التحقق من بيانات المستخدم
    user = db.query(User).filter(User.username == username).first()
    
    if not user or not user.verify_password(password):
        return templates.TemplateResponse("login.html", {
            "request": request,
            "title": "تسجيل الدخول",
            "error": "اسم المستخدم أو كلمة المرور غير صحيحة"
        })
    
    if not user.is_active:
        return templates.TemplateResponse("login.html", {
            "request": request,
            "title": "تسجيل الدخول",
            "error": "الحساب غير مفعل"
        })
    
    if user.is_locked():
        return templates.TemplateResponse("login.html", {
            "request": request,
            "title": "تسجيل الدخول",
            "error": "الحساب مقفل مؤقتاً"
        })
    
    # تسجيل الدخول الناجح
    user.record_login()
    db.commit()
    
    # إعادة توجيه إلى لوحة التحكم
    response = RedirectResponse(url="/dashboard", status_code=status.HTTP_302_FOUND)
    # هنا يمكن إضافة session cookie
    return response

# لوحة التحكم
@app.get("/dashboard", response_class=HTMLResponse)
async def dashboard(request: Request, db: Session = Depends(get_db)):
    # الحصول على الإحصائيات
    stats = db_manager.get_database_statistics()
    
    # الحصول على آخر الموظفين المضافين
    recent_employees = db.query(Employee).filter(Employee.is_active == True).order_by(Employee.created_at.desc()).limit(5).all()
    
    # الحصول على إجمالي الأرصدة البنكية
    total_balance = BankAccount.get_total_balance()
    
    return templates.TemplateResponse("dashboard.html", {
        "request": request,
        "title": "لوحة التحكم",
        "stats": stats,
        "recent_employees": recent_employees,
        "total_balance": total_balance
    })

# إدارة الموظفين
@app.get("/employees", response_class=HTMLResponse)
async def employees_list(
    request: Request,
    search: str = None,
    department: str = None,
    section: str = None,
    job_grade: str = None,
    db: Session = Depends(get_db)
):
    # بناء الاستعلام مع الفلاتر
    query = db.query(Employee).filter(Employee.is_active == True)

    if search:
        query = query.filter(
            (Employee.full_name.contains(search)) |
            (Employee.employee_number.contains(search))
        )

    if department:
        query = query.filter(Employee.department == department)

    if section:
        query = query.filter(Employee.section == section)

    if job_grade:
        query = query.filter(Employee.job_grade == job_grade)

    employees = query.order_by(Employee.full_name).all()

    return templates.TemplateResponse("employees/list.html", {
        "request": request,
        "title": "إدارة الموظفين",
        "employees": employees
    })

@app.get("/employees/add", response_class=HTMLResponse)
async def add_employee_form(request: Request):
    return templates.TemplateResponse("employees/add.html", {
        "request": request,
        "title": "إضافة موظف جديد"
    })

@app.post("/employees/add")
async def add_employee(
    request: Request,
    employee_number: str = Form(...),
    full_name: str = Form(...),
    job_title: str = Form(...),
    job_grade: str = Form(...),
    basic_salary: float = Form(...),
    qualification: str = Form(None),
    stage: str = Form(None),
    ministry: str = Form("وزارة الشباب والرياضة"),
    department: str = Form("دائرة الطب الرياضي"),
    section: str = Form("قسم إداري"),
    division: str = Form("شعبة الحسابات"),
    hire_date: str = Form(None),
    birth_date: str = Form(None),
    national_id: str = Form(None),
    phone: str = Form(None),
    email: str = Form(None),
    address: str = Form(None),
    add_another: str = Form(None),
    db: Session = Depends(get_db)
):
    try:
        # التحقق من عدم تكرار رقم الموظف
        existing = db.query(Employee).filter(Employee.employee_number == employee_number).first()
        if existing:
            return templates.TemplateResponse("employees/add.html", {
                "request": request,
                "title": "إضافة موظف جديد",
                "error": "رقم الموظف موجود مسبقاً"
            })

        # تحويل التواريخ
        from datetime import datetime
        hire_date_obj = datetime.strptime(hire_date, "%Y-%m-%d").date() if hire_date else None
        birth_date_obj = datetime.strptime(birth_date, "%Y-%m-%d").date() if birth_date else None

        # إنشاء موظف جديد
        employee = Employee(
            employee_number=employee_number,
            full_name=full_name,
            job_title=job_title,
            job_grade=job_grade,
            basic_salary=basic_salary,
            qualification=qualification,
            stage=stage,
            ministry=ministry,
            department=department,
            section=section,
            division=division,
            hire_date=hire_date_obj,
            birth_date=birth_date_obj,
            national_id=national_id,
            phone=phone,
            email=email,
            address=address
        )

        db.add(employee)
        db.commit()

        # إضافة الاستقطاعات الافتراضية
        from models.deduction import Deduction
        default_deductions = [
            ("retirement", None, 10.0),  # تقاعد 10%
            ("government_contribution", None, 15.0),  # مساهمة حكومية 15%
        ]

        for deduction_type, amount, percentage in default_deductions:
            deduction = Deduction(
                employee_id=employee.id,
                deduction_type=deduction_type,
                amount=amount or 0,
                percentage=percentage
            )
            db.add(deduction)

        db.commit()

        # إذا كان المطلوب إضافة موظف آخر
        if add_another:
            return templates.TemplateResponse("employees/add.html", {
                "request": request,
                "title": "إضافة موظف جديد",
                "success": f"تم إضافة الموظف {full_name} بنجاح. يمكنك إضافة موظف آخر."
            })

        return RedirectResponse(url="/employees", status_code=status.HTTP_302_FOUND)

    except Exception as e:
        return templates.TemplateResponse("employees/add.html", {
            "request": request,
            "title": "إضافة موظف جديد",
            "error": f"خطأ في إضافة الموظف: {str(e)}"
        })

# عرض تفاصيل الموظف
@app.get("/employees/{employee_id}", response_class=HTMLResponse)
async def employee_detail(employee_id: int, request: Request, db: Session = Depends(get_db)):
    employee = db.query(Employee).filter(Employee.id == employee_id).first()
    if not employee:
        raise HTTPException(status_code=404, detail="الموظف غير موجود")

    return templates.TemplateResponse("employees/detail.html", {
        "request": request,
        "title": f"تفاصيل الموظف - {employee.full_name}",
        "employee": employee
    })

# تعديل الموظف
@app.get("/employees/{employee_id}/edit", response_class=HTMLResponse)
async def edit_employee_form(employee_id: int, request: Request, db: Session = Depends(get_db)):
    employee = db.query(Employee).filter(Employee.id == employee_id).first()
    if not employee:
        raise HTTPException(status_code=404, detail="الموظف غير موجود")

    return templates.TemplateResponse("employees/edit.html", {
        "request": request,
        "title": f"تعديل الموظف - {employee.full_name}",
        "employee": employee
    })

@app.post("/employees/{employee_id}/edit")
async def update_employee(
    employee_id: int,
    request: Request,
    full_name: str = Form(...),
    job_title: str = Form(...),
    job_grade: str = Form(...),
    basic_salary: float = Form(...),
    qualification: str = Form(None),
    stage: str = Form(None),
    phone: str = Form(None),
    email: str = Form(None),
    address: str = Form(None),
    db: Session = Depends(get_db)
):
    employee = db.query(Employee).filter(Employee.id == employee_id).first()
    if not employee:
        raise HTTPException(status_code=404, detail="الموظف غير موجود")

    try:
        # تحديث البيانات
        employee.full_name = full_name
        employee.job_title = job_title
        employee.job_grade = job_grade
        employee.basic_salary = basic_salary
        employee.qualification = qualification
        employee.stage = stage
        employee.phone = phone
        employee.email = email
        employee.address = address

        db.commit()

        return RedirectResponse(url=f"/employees/{employee_id}", status_code=status.HTTP_302_FOUND)

    except Exception as e:
        return templates.TemplateResponse("employees/edit.html", {
            "request": request,
            "title": f"تعديل الموظف - {employee.full_name}",
            "employee": employee,
            "error": f"خطأ في تحديث الموظف: {str(e)}"
        })

# حذف الموظف
@app.post("/employees/{employee_id}/delete")
async def delete_employee(employee_id: int, db: Session = Depends(get_db)):
    employee = db.query(Employee).filter(Employee.id == employee_id).first()
    if not employee:
        raise HTTPException(status_code=404, detail="الموظف غير موجود")

    # إلغاء تفعيل الموظف بدلاً من الحذف الفعلي
    employee.is_active = False
    db.commit()

    return RedirectResponse(url="/employees", status_code=status.HTTP_302_FOUND)

# إدارة مخصصات الموظف
@app.get("/employees/{employee_id}/allowances", response_class=HTMLResponse)
async def employee_allowances(employee_id: int, request: Request, db: Session = Depends(get_db)):
    employee = db.query(Employee).filter(Employee.id == employee_id).first()
    if not employee:
        raise HTTPException(status_code=404, detail="الموظف غير موجود")

    return templates.TemplateResponse("employees/allowances.html", {
        "request": request,
        "title": f"مخصصات الموظف - {employee.full_name}",
        "employee": employee
    })

# إضافة مخصص جديد
@app.post("/employees/{employee_id}/allowances/add")
async def add_employee_allowance(
    employee_id: int,
    request: Request,
    allowance_type: str = Form(...),
    amount: float = Form(...),
    effective_date: str = Form(None),
    notes: str = Form(None),
    db: Session = Depends(get_db)
):
    employee = db.query(Employee).filter(Employee.id == employee_id).first()
    if not employee:
        raise HTTPException(status_code=404, detail="الموظف غير موجود")

    try:
        from models.allowance import Allowance
        from datetime import datetime

        # تحويل التاريخ
        effective_date_obj = datetime.strptime(effective_date, "%Y-%m-%d").date() if effective_date else None

        # التحقق من عدم وجود نفس المخصص
        existing = db.query(Allowance).filter(
            Allowance.employee_id == employee_id,
            Allowance.allowance_type == allowance_type,
            Allowance.is_active == True
        ).first()

        if existing:
            return templates.TemplateResponse("employees/allowances.html", {
                "request": request,
                "title": f"مخصصات الموظف - {employee.full_name}",
                "employee": employee,
                "error": f"المخصص {allowance_type} موجود مسبقاً للموظف"
            })

        # إنشاء المخصص الجديد
        allowance = Allowance(
            employee_id=employee_id,
            allowance_type=allowance_type,
            amount=amount,
            notes=notes
        )

        if effective_date_obj:
            allowance.effective_date = effective_date_obj

        db.add(allowance)
        db.commit()

        return RedirectResponse(url=f"/employees/{employee_id}/allowances", status_code=status.HTTP_302_FOUND)

    except Exception as e:
        return templates.TemplateResponse("employees/allowances.html", {
            "request": request,
            "title": f"مخصصات الموظف - {employee.full_name}",
            "employee": employee,
            "error": f"خطأ في إضافة المخصص: {str(e)}"
        })

# تعديل مخصص
@app.post("/employees/{employee_id}/allowances/{allowance_id}/edit")
async def edit_employee_allowance(
    employee_id: int,
    allowance_id: int,
    amount: float = Form(...),
    db: Session = Depends(get_db)
):
    from models.allowance import Allowance

    allowance = db.query(Allowance).filter(
        Allowance.id == allowance_id,
        Allowance.employee_id == employee_id
    ).first()

    if not allowance:
        raise HTTPException(status_code=404, detail="المخصص غير موجود")

    allowance.amount = amount
    db.commit()

    return RedirectResponse(url=f"/employees/{employee_id}/allowances", status_code=status.HTTP_302_FOUND)

# إلغاء تفعيل مخصص
@app.post("/employees/{employee_id}/allowances/{allowance_id}/deactivate")
async def deactivate_employee_allowance(
    employee_id: int,
    allowance_id: int,
    db: Session = Depends(get_db)
):
    from models.allowance import Allowance

    allowance = db.query(Allowance).filter(
        Allowance.id == allowance_id,
        Allowance.employee_id == employee_id
    ).first()

    if not allowance:
        raise HTTPException(status_code=404, detail="المخصص غير موجود")

    allowance.is_active = False
    db.commit()

    return RedirectResponse(url=f"/employees/{employee_id}/allowances", status_code=status.HTTP_302_FOUND)

# إعادة تفعيل مخصص
@app.post("/employees/{employee_id}/allowances/{allowance_id}/activate")
async def activate_employee_allowance(
    employee_id: int,
    allowance_id: int,
    db: Session = Depends(get_db)
):
    from models.allowance import Allowance

    allowance = db.query(Allowance).filter(
        Allowance.id == allowance_id,
        Allowance.employee_id == employee_id
    ).first()

    if not allowance:
        raise HTTPException(status_code=404, detail="المخصص غير موجود")

    allowance.is_active = True
    db.commit()

    return RedirectResponse(url=f"/employees/{employee_id}/allowances", status_code=status.HTTP_302_FOUND)

# صفحة استيراد الموظفين
@app.get("/employees/import", response_class=HTMLResponse)
async def import_employees_form(request: Request):
    return templates.TemplateResponse("employees/import.html", {
        "request": request,
        "title": "استيراد الموظفين من Excel"
    })

# استيراد الموظفين من Excel
@app.post("/employees/import")
async def import_employees_excel(
    request: Request,
    excel_file: UploadFile = File(...),
    sheet_name: str = Form(None),
    skip_duplicates: bool = Form(False),
    add_default_deductions: bool = Form(False),
    db: Session = Depends(get_db)
):
    try:
        import pandas as pd
        import io
        from datetime import datetime

        # التحقق من نوع الملف
        if not excel_file.filename.endswith(('.xlsx', '.xls')):
            return templates.TemplateResponse("employees/import.html", {
                "request": request,
                "title": "استيراد الموظفين من Excel",
                "error": "يرجى رفع ملف Excel صحيح (.xlsx أو .xls)"
            })

        # قراءة محتوى الملف
        contents = await excel_file.read()

        # قراءة البيانات من Excel
        try:
            df = pd.read_excel(io.BytesIO(contents), sheet_name=sheet_name)
        except Exception as e:
            return templates.TemplateResponse("employees/import.html", {
                "request": request,
                "title": "استيراد الموظفين من Excel",
                "error": f"خطأ في قراءة ملف Excel: {str(e)}"
            })

        # تنظيف البيانات
        df = df.dropna(how='all')  # حذف الصفوف الفارغة تماماً
        df = df.fillna('')  # ملء القيم الفارغة بنص فارغ

        # التحقق من وجود الأعمدة المطلوبة
        required_columns = ['الرقم الوظيفي', 'الاسم الكامل', 'المنصب', 'الدرجة الوظيفية', 'الراتب الأساسي']
        missing_columns = [col for col in required_columns if col not in df.columns]

        if missing_columns:
            return templates.TemplateResponse("employees/import.html", {
                "request": request,
                "title": "استيراد الموظفين من Excel",
                "error": f"الأعمدة المطلوبة مفقودة: {', '.join(missing_columns)}"
            })

        # إحصائيات الاستيراد
        total_rows = len(df)
        imported_count = 0
        skipped_count = 0
        error_count = 0
        errors = []

        # معالجة كل صف
        for index, row in df.iterrows():
            try:
                # التحقق من البيانات المطلوبة
                employee_number = str(row['الرقم الوظيفي']).strip()
                full_name = str(row['الاسم الكامل']).strip()
                job_title = str(row['المنصب']).strip()
                job_grade = str(row['الدرجة الوظيفية']).strip()

                if not all([employee_number, full_name, job_title, job_grade]):
                    errors.append(f"الصف {index + 2}: بيانات مطلوبة مفقودة")
                    error_count += 1
                    continue

                # تحويل الراتب الأساسي
                try:
                    basic_salary = float(row['الراتب الأساسي'])
                except (ValueError, TypeError):
                    errors.append(f"الصف {index + 2}: الراتب الأساسي غير صحيح")
                    error_count += 1
                    continue

                # التحقق من عدم تكرار الرقم الوظيفي
                existing = db.query(Employee).filter(Employee.employee_number == employee_number).first()
                if existing:
                    if skip_duplicates:
                        skipped_count += 1
                        continue
                    else:
                        errors.append(f"الصف {index + 2}: الرقم الوظيفي {employee_number} موجود مسبقاً")
                        error_count += 1
                        continue

                # إنشاء الموظف الجديد
                employee = Employee(
                    employee_number=employee_number,
                    full_name=full_name,
                    job_title=job_title,
                    job_grade=job_grade,
                    basic_salary=basic_salary,
                    qualification=str(row.get('الشهادة', '')).strip() or None,
                    stage=str(row.get('المرحلة', '')).strip() or None,
                    phone=str(row.get('رقم الهاتف', '')).strip() or None,
                    email=str(row.get('البريد الإلكتروني', '')).strip() or None,
                    address=str(row.get('العنوان', '')).strip() or None,
                    ministry="وزارة الشباب والرياضة",
                    department="دائرة الطب الرياضي",
                    section="قسم إداري",
                    division="شعبة الحسابات"
                )

                db.add(employee)
                db.flush()  # للحصول على ID الموظف

                # إضافة الاستقطاعات الافتراضية إذا كان مطلوباً
                if add_default_deductions:
                    from models.deduction import Deduction
                    default_deductions = [
                        ("retirement", None, 10.0),  # تقاعد 10%
                        ("government_contribution", None, 15.0),  # مساهمة حكومية 15%
                    ]

                    for deduction_type, amount, percentage in default_deductions:
                        deduction = Deduction(
                            employee_id=employee.id,
                            deduction_type=deduction_type,
                            amount=amount or 0,
                            percentage=percentage
                        )
                        db.add(deduction)

                imported_count += 1

            except Exception as e:
                errors.append(f"الصف {index + 2}: خطأ غير متوقع - {str(e)}")
                error_count += 1
                continue

        # حفظ التغييرات
        db.commit()

        # إعداد رسالة النتيجة
        if imported_count > 0:
            success_message = f"تم استيراد {imported_count} موظف بنجاح"
            if skipped_count > 0:
                success_message += f" وتم تجاهل {skipped_count} موظف مكرر"
            if error_count > 0:
                success_message += f" مع {error_count} خطأ"
        else:
            success_message = "لم يتم استيراد أي موظف"

        return templates.TemplateResponse("employees/import.html", {
            "request": request,
            "title": "استيراد الموظفين من Excel",
            "success": success_message,
            "import_stats": {
                "total": total_rows,
                "imported": imported_count,
                "skipped": skipped_count,
                "errors": error_count,
                "error_details": errors[:10]  # أول 10 أخطاء فقط
            }
        })

    except Exception as e:
        return templates.TemplateResponse("employees/import.html", {
            "request": request,
            "title": "استيراد الموظفين من Excel",
            "error": f"خطأ في معالجة الملف: {str(e)}"
        })

# تحميل قالب Excel
@app.get("/employees/template/excel")
async def download_excel_template():
    from fastapi.responses import StreamingResponse
    import io
    import pandas as pd

    # إنشاء بيانات تجريبية للقالب
    sample_data = [
        {
            'الرقم الوظيفي': '12001',
            'الاسم الكامل': 'أحمد محمد علي',
            'المنصب': 'محاسب أول',
            'الدرجة الوظيفية': 'الثالثة',
            'الراتب الأساسي': 750000,
            'الشهادة': 'بكالوريوس',
            'المرحلة': 'الأولى',
            'رقم الهاتف': '07901234567',
            'البريد الإلكتروني': '<EMAIL>',
            'العنوان': 'بغداد - الكرادة'
        },
        {
            'الرقم الوظيفي': '12002',
            'الاسم الكامل': 'فاطمة حسن محمود',
            'المنصب': 'كاتبة',
            'الدرجة الوظيفية': 'الخامسة',
            'الراتب الأساسي': 580000,
            'الشهادة': 'إعدادية',
            'المرحلة': 'الثانية',
            'رقم الهاتف': '07801234567',
            'البريد الإلكتروني': '<EMAIL>',
            'العنوان': 'بغداد - الجادرية'
        }
    ]

    df = pd.DataFrame(sample_data)

    # إنشاء ملف Excel في الذاكرة
    output = io.BytesIO()
    with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
        df.to_excel(writer, sheet_name='الموظفون', index=False)

        # تنسيق الورقة
        workbook = writer.book
        worksheet = writer.sheets['الموظفون']

        # تنسيق الرأس
        header_format = workbook.add_format({
            'bold': True,
            'text_wrap': True,
            'valign': 'top',
            'fg_color': '#4472C4',
            'font_color': 'white',
            'border': 1
        })

        # تطبيق التنسيق على الرأس
        for col_num, value in enumerate(df.columns.values):
            worksheet.write(0, col_num, value, header_format)
            worksheet.set_column(col_num, col_num, 20)

    output.seek(0)

    return StreamingResponse(
        io.BytesIO(output.read()),
        media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        headers={"Content-Disposition": "attachment; filename=employee_template.xlsx"}
    )

# تصدير الموظفين إلى Excel
@app.get("/employees/export/excel")
async def export_employees_excel(db: Session = Depends(get_db)):
    from fastapi.responses import StreamingResponse
    import io
    import pandas as pd

    employees = db.query(Employee).filter(Employee.is_active == True).all()

    # تحويل البيانات إلى DataFrame
    data = []
    for emp in employees:
        data.append({
            'الرقم الوظيفي': emp.employee_number,
            'الاسم الكامل': emp.full_name,
            'المنصب': emp.job_title,
            'الدرجة الوظيفية': emp.job_grade,
            'الراتب الأساسي': emp.basic_salary,
            'الشهادة': emp.qualification,
            'القسم': emp.section,
            'الشعبة': emp.division,
            'رقم الهاتف': emp.phone,
            'البريد الإلكتروني': emp.email
        })

    df = pd.DataFrame(data)

    # إنشاء ملف Excel في الذاكرة
    output = io.BytesIO()
    with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
        df.to_excel(writer, sheet_name='الموظفون', index=False)

    output.seek(0)

    return StreamingResponse(
        io.BytesIO(output.read()),
        media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        headers={"Content-Disposition": "attachment; filename=employees.xlsx"}
    )

# إدارة الرواتب
@app.get("/salaries", response_class=HTMLResponse)
async def salaries_list(request: Request, db: Session = Depends(get_db)):
    from datetime import datetime
    current_month = datetime.now().month
    current_year = datetime.now().year
    
    salaries = db.query(MonthlySalary).filter(
        MonthlySalary.month == current_month,
        MonthlySalary.year == current_year
    ).all()
    
    return templates.TemplateResponse("salaries/list.html", {
        "request": request,
        "title": "إدارة الرواتب",
        "salaries": salaries,
        "current_month": current_month,
        "current_year": current_year
    })

# إدارة الحسابات البنكية
@app.get("/bank-accounts", response_class=HTMLResponse)
async def bank_accounts_list(request: Request, db: Session = Depends(get_db)):
    accounts = db.query(BankAccount).filter(BankAccount.is_active == True).all()
    
    return templates.TemplateResponse("bank_accounts/list.html", {
        "request": request,
        "title": "إدارة الحسابات البنكية",
        "accounts": accounts
    })

# التقارير
@app.get("/reports", response_class=HTMLResponse)
async def reports_page(request: Request):
    return templates.TemplateResponse("reports/index.html", {
        "request": request,
        "title": "التقارير"
    })

# إعدادات النظام
@app.get("/settings", response_class=HTMLResponse)
async def settings_page(request: Request):
    return templates.TemplateResponse("settings/index.html", {
        "request": request,
        "title": "إعدادات النظام"
    })

# تسجيل الخروج
@app.get("/logout")
async def logout():
    response = RedirectResponse(url="/login", status_code=status.HTTP_302_FOUND)
    # هنا يمكن حذف session cookie
    return response

# API endpoints للبيانات
@app.get("/api/employees")
async def api_employees(db: Session = Depends(get_db)):
    employees = db.query(Employee).filter(Employee.is_active == True).all()
    return [employee.to_dict() for employee in employees]

@app.get("/api/employees/{employee_id}")
async def api_employee_detail(employee_id: int, db: Session = Depends(get_db)):
    employee = db.query(Employee).filter(Employee.id == employee_id).first()
    if not employee:
        raise HTTPException(status_code=404, detail="الموظف غير موجود")
    return employee.to_dict()

@app.get("/api/bank-accounts")
async def api_bank_accounts(db: Session = Depends(get_db)):
    accounts = db.query(BankAccount).filter(BankAccount.is_active == True).all()
    return [account.to_dict() for account in accounts]

# معالج الأخطاء
@app.exception_handler(404)
async def not_found_handler(request: Request, exc: HTTPException):
    return templates.TemplateResponse("errors/404.html", {
        "request": request,
        "title": "الصفحة غير موجودة"
    }, status_code=404)

@app.exception_handler(500)
async def server_error_handler(request: Request, exc: HTTPException):
    return templates.TemplateResponse("errors/500.html", {
        "request": request,
        "title": "خطأ في الخادم"
    }, status_code=500)

if __name__ == "__main__":
    uvicorn.run(
        "web_main:app",
        host=WEB_CONFIG['host'],
        port=WEB_CONFIG['port'],
        reload=WEB_CONFIG['debug']
    )
