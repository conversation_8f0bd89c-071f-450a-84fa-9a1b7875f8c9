# -*- coding: utf-8 -*-
"""
تطبيق الويب الرئيسي
Main Web Application using FastAPI
"""

from fastapi import FastAPI, Request, Depends, HTTPException, status, Form
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
import uvicorn
from pathlib import Path

# استيراد الإعدادات والنماذج
from config.settings import WEB_CONFIG, TEMPLATES_DIR, STATIC_DIR, create_directories
from database.database_manager import db_manager
from models.user import User
from models.employee import Employee
from models.salary import MonthlySalary
from models.bank_account import BankAccount
from utils.arabic_support import arabic_support

# إنشاء التطبيق
app = FastAPI(
    title="نظام المحاسبة المتكامل",
    description="نظام إدارة رواتب الموظفين والمصروفات للمؤسسات الحكومية",
    version="1.0.0"
)

# إعداد الملفات الثابتة والقوالب
create_directories()
app.mount("/static", StaticFiles(directory=str(STATIC_DIR)), name="static")
templates = Jinja2Templates(directory=str(TEMPLATES_DIR))

# إعداد الأمان
security = HTTPBearer()

# دالة للحصول على جلسة قاعدة البيانات
def get_db():
    session = db_manager.get_session()
    try:
        yield session
    finally:
        session.close()

# دالة للتحقق من المصادقة
def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security), db: Session = Depends(get_db)):
    # هنا يمكن إضافة التحقق من JWT token
    # للبساية، سنستخدم session بسيط
    return None

# الصفحة الرئيسية
@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    return templates.TemplateResponse("index.html", {
        "request": request,
        "title": "نظام المحاسبة المتكامل",
        "organization": "وزارة الشباب والرياضة - دائرة الطب الرياضي"
    })

# صفحة تسجيل الدخول
@app.get("/login", response_class=HTMLResponse)
async def login_page(request: Request):
    return templates.TemplateResponse("login.html", {
        "request": request,
        "title": "تسجيل الدخول"
    })

@app.post("/login")
async def login(
    request: Request,
    username: str = Form(...),
    password: str = Form(...),
    db: Session = Depends(get_db)
):
    # التحقق من بيانات المستخدم
    user = db.query(User).filter(User.username == username).first()
    
    if not user or not user.verify_password(password):
        return templates.TemplateResponse("login.html", {
            "request": request,
            "title": "تسجيل الدخول",
            "error": "اسم المستخدم أو كلمة المرور غير صحيحة"
        })
    
    if not user.is_active:
        return templates.TemplateResponse("login.html", {
            "request": request,
            "title": "تسجيل الدخول",
            "error": "الحساب غير مفعل"
        })
    
    if user.is_locked():
        return templates.TemplateResponse("login.html", {
            "request": request,
            "title": "تسجيل الدخول",
            "error": "الحساب مقفل مؤقتاً"
        })
    
    # تسجيل الدخول الناجح
    user.record_login()
    db.commit()
    
    # إعادة توجيه إلى لوحة التحكم
    response = RedirectResponse(url="/dashboard", status_code=status.HTTP_302_FOUND)
    # هنا يمكن إضافة session cookie
    return response

# لوحة التحكم
@app.get("/dashboard", response_class=HTMLResponse)
async def dashboard(request: Request, db: Session = Depends(get_db)):
    # الحصول على الإحصائيات
    stats = db_manager.get_database_statistics()
    
    # الحصول على آخر الموظفين المضافين
    recent_employees = db.query(Employee).filter(Employee.is_active == True).order_by(Employee.created_at.desc()).limit(5).all()
    
    # الحصول على إجمالي الأرصدة البنكية
    total_balance = BankAccount.get_total_balance()
    
    return templates.TemplateResponse("dashboard.html", {
        "request": request,
        "title": "لوحة التحكم",
        "stats": stats,
        "recent_employees": recent_employees,
        "total_balance": total_balance
    })

# إدارة الموظفين
@app.get("/employees", response_class=HTMLResponse)
async def employees_list(request: Request, db: Session = Depends(get_db)):
    employees = db.query(Employee).filter(Employee.is_active == True).all()
    
    return templates.TemplateResponse("employees/list.html", {
        "request": request,
        "title": "إدارة الموظفين",
        "employees": employees
    })

@app.get("/employees/add", response_class=HTMLResponse)
async def add_employee_form(request: Request):
    return templates.TemplateResponse("employees/add.html", {
        "request": request,
        "title": "إضافة موظف جديد"
    })

@app.post("/employees/add")
async def add_employee(
    request: Request,
    employee_number: str = Form(...),
    full_name: str = Form(...),
    job_title: str = Form(...),
    job_grade: str = Form(...),
    basic_salary: float = Form(...),
    qualification: str = Form(None),
    stage: str = Form(None),
    db: Session = Depends(get_db)
):
    try:
        # التحقق من عدم تكرار رقم الموظف
        existing = db.query(Employee).filter(Employee.employee_number == employee_number).first()
        if existing:
            return templates.TemplateResponse("employees/add.html", {
                "request": request,
                "title": "إضافة موظف جديد",
                "error": "رقم الموظف موجود مسبقاً"
            })
        
        # إنشاء موظف جديد
        employee = Employee(
            employee_number=employee_number,
            full_name=full_name,
            job_title=job_title,
            job_grade=job_grade,
            basic_salary=basic_salary,
            qualification=qualification,
            stage=stage
        )
        
        db.add(employee)
        db.commit()
        
        return RedirectResponse(url="/employees", status_code=status.HTTP_302_FOUND)
        
    except Exception as e:
        return templates.TemplateResponse("employees/add.html", {
            "request": request,
            "title": "إضافة موظف جديد",
            "error": f"خطأ في إضافة الموظف: {str(e)}"
        })

# إدارة الرواتب
@app.get("/salaries", response_class=HTMLResponse)
async def salaries_list(request: Request, db: Session = Depends(get_db)):
    from datetime import datetime
    current_month = datetime.now().month
    current_year = datetime.now().year
    
    salaries = db.query(MonthlySalary).filter(
        MonthlySalary.month == current_month,
        MonthlySalary.year == current_year
    ).all()
    
    return templates.TemplateResponse("salaries/list.html", {
        "request": request,
        "title": "إدارة الرواتب",
        "salaries": salaries,
        "current_month": current_month,
        "current_year": current_year
    })

# إدارة الحسابات البنكية
@app.get("/bank-accounts", response_class=HTMLResponse)
async def bank_accounts_list(request: Request, db: Session = Depends(get_db)):
    accounts = db.query(BankAccount).filter(BankAccount.is_active == True).all()
    
    return templates.TemplateResponse("bank_accounts/list.html", {
        "request": request,
        "title": "إدارة الحسابات البنكية",
        "accounts": accounts
    })

# التقارير
@app.get("/reports", response_class=HTMLResponse)
async def reports_page(request: Request):
    return templates.TemplateResponse("reports/index.html", {
        "request": request,
        "title": "التقارير"
    })

# إعدادات النظام
@app.get("/settings", response_class=HTMLResponse)
async def settings_page(request: Request):
    return templates.TemplateResponse("settings/index.html", {
        "request": request,
        "title": "إعدادات النظام"
    })

# تسجيل الخروج
@app.get("/logout")
async def logout():
    response = RedirectResponse(url="/login", status_code=status.HTTP_302_FOUND)
    # هنا يمكن حذف session cookie
    return response

# API endpoints للبيانات
@app.get("/api/employees")
async def api_employees(db: Session = Depends(get_db)):
    employees = db.query(Employee).filter(Employee.is_active == True).all()
    return [employee.to_dict() for employee in employees]

@app.get("/api/employees/{employee_id}")
async def api_employee_detail(employee_id: int, db: Session = Depends(get_db)):
    employee = db.query(Employee).filter(Employee.id == employee_id).first()
    if not employee:
        raise HTTPException(status_code=404, detail="الموظف غير موجود")
    return employee.to_dict()

@app.get("/api/bank-accounts")
async def api_bank_accounts(db: Session = Depends(get_db)):
    accounts = db.query(BankAccount).filter(BankAccount.is_active == True).all()
    return [account.to_dict() for account in accounts]

# معالج الأخطاء
@app.exception_handler(404)
async def not_found_handler(request: Request, exc: HTTPException):
    return templates.TemplateResponse("errors/404.html", {
        "request": request,
        "title": "الصفحة غير موجودة"
    }, status_code=404)

@app.exception_handler(500)
async def server_error_handler(request: Request, exc: HTTPException):
    return templates.TemplateResponse("errors/500.html", {
        "request": request,
        "title": "خطأ في الخادم"
    }, status_code=500)

if __name__ == "__main__":
    uvicorn.run(
        "web_main:app",
        host=WEB_CONFIG['host'],
        port=WEB_CONFIG['port'],
        reload=WEB_CONFIG['debug']
    )
