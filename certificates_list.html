<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الشهادات - نظام المحاسبة المتكامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: #f8f9fa;
        }
        
        .navbar {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .card {
            border: none;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
            border-radius: 12px;
        }
        
        .table th {
            background: #343a40;
            color: white;
            text-align: center;
            border: none;
        }
        
        .table td {
            vertical-align: middle;
            text-align: center;
        }
        
        .cert-level {
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .level-phd {
            background: linear-gradient(45deg, #6f42c1, #5a2d91);
            color: white;
        }
        
        .level-master {
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
        }
        
        .level-bachelor {
            background: linear-gradient(45deg, #28a745, #1e7e34);
            color: white;
        }
        
        .level-diploma {
            background: linear-gradient(45deg, #ffc107, #e0a800);
            color: #333;
        }
        
        .level-secondary {
            background: linear-gradient(45deg, #fd7e14, #e55a00);
            color: white;
        }
        
        .level-certificate {
            background: linear-gradient(45deg, #17a2b8, #138496);
            color: white;
        }
        
        .search-box {
            border-radius: 25px;
            border: 2px solid #e9ecef;
            padding: 10px 20px;
        }
        
        .search-box:focus {
            border-color: #dc3545;
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
        }
        
        .action-buttons .btn {
            margin: 2px;
        }
        
        .cert-stats {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="simple_homepage.html">
                <i class="fas fa-calculator me-2"></i>
                نظام المحاسبة المتكامل
            </a>
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="simple_homepage.html">
                    <i class="fas fa-home me-1"></i>الرئيسية
                </a>
                <a class="nav-link" href="employees_page.html">
                    <i class="fas fa-users me-1"></i>الموظفون
                </a>
                <a class="nav-link" href="basic_lists_page.html">
                    <i class="fas fa-list me-1"></i>القوائم الأساسية
                </a>
                <a class="nav-link active" href="certificates_list.html">
                    <i class="fas fa-certificate me-1"></i>الشهادات
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- عنوان الصفحة -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2 class="text-danger">
                        <i class="fas fa-certificate me-2"></i>
                        الشهادات الدراسية والمهنية
                    </h2>
                    <div>
                        <button class="btn btn-danger me-2" onclick="showAddCertModal()">
                            <i class="fas fa-plus me-1"></i>إضافة شهادة
                        </button>
                        <a href="basic_lists_page.html" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right me-1"></i>العودة للقوائم
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات الشهادات -->
        <div class="cert-stats">
            <div class="row text-center">
                <div class="col-md-2">
                    <h3><i class="fas fa-graduation-cap me-2"></i>2</h3>
                    <p class="mb-0">دكتوراه</p>
                </div>
                <div class="col-md-2">
                    <h3><i class="fas fa-user-graduate me-2"></i>3</h3>
                    <p class="mb-0">ماجستير</p>
                </div>
                <div class="col-md-2">
                    <h3><i class="fas fa-university me-2"></i>5</h3>
                    <p class="mb-0">بكالوريوس</p>
                </div>
                <div class="col-md-2">
                    <h3><i class="fas fa-scroll me-2"></i>3</h3>
                    <p class="mb-0">دبلوم</p>
                </div>
                <div class="col-md-2">
                    <h3><i class="fas fa-school me-2"></i>1</h3>
                    <p class="mb-0">ثانوية</p>
                </div>
                <div class="col-md-2">
                    <h3><i class="fas fa-award me-2"></i>1</h3>
                    <p class="mb-0">شهادات مهنية</p>
                </div>
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h4>15</h4>
                        <small>إجمالي الشهادات</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h4>125</h4>
                        <small>إجمالي الحاصلين</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h4>85%</h4>
                        <small>نسبة الجامعيين</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-dark">
                    <div class="card-body text-center">
                        <h4>12</h4>
                        <small>شهادات معتمدة</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- شريط البحث والفلترة -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-search"></i>
                                    </span>
                                    <input type="text" class="form-control search-box" placeholder="البحث عن شهادة..." id="searchInput">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="levelFilter">
                                    <option value="">جميع المستويات</option>
                                    <option value="دكتوراه">دكتوراه</option>
                                    <option value="ماجستير">ماجستير</option>
                                    <option value="بكالوريوس">بكالوريوس</option>
                                    <option value="دبلوم">دبلوم</option>
                                    <option value="ثانوية">ثانوية</option>
                                    <option value="مهنية">مهنية</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="typeFilter">
                                    <option value="">جميع الأنواع</option>
                                    <option value="أكاديمية">أكاديمية</option>
                                    <option value="مهنية">مهنية</option>
                                    <option value="تقنية">تقنية</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button class="btn btn-outline-secondary w-100" onclick="clearFilters()">
                                    <i class="fas fa-times me-1"></i>مسح
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- جدول الشهادات -->
        <div class="card">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>قائمة الشهادات
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0" id="certificatesTable">
                        <thead>
                            <tr>
                                <th>الرمز</th>
                                <th>اسم الشهادة</th>
                                <th>المستوى</th>
                                <th>النوع</th>
                                <th>مدة الدراسة</th>
                                <th>عدد الحاصلين</th>
                                <th>معتمدة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="certificatesTableBody">
                            <tr>
                                <td><strong>PHD-001</strong></td>
                                <td>دكتوراه في الطب</td>
                                <td><span class="cert-level level-phd">دكتوراه</span></td>
                                <td><span class="badge bg-primary">أكاديمية</span></td>
                                <td><span class="badge bg-info">6-8 سنوات</span></td>
                                <td><span class="badge bg-success">2</span></td>
                                <td><span class="badge bg-success">نعم</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewCert('PHD-001')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editCert('PHD-001')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="الحاصلين" onclick="viewHolders('PHD-001')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>PHD-002</strong></td>
                                <td>دكتوراه في المحاسبة</td>
                                <td><span class="cert-level level-phd">دكتوراه</span></td>
                                <td><span class="badge bg-primary">أكاديمية</span></td>
                                <td><span class="badge bg-info">4-6 سنوات</span></td>
                                <td><span class="badge bg-success">1</span></td>
                                <td><span class="badge bg-success">نعم</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewCert('PHD-002')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editCert('PHD-002')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="الحاصلين" onclick="viewHolders('PHD-002')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>MSC-001</strong></td>
                                <td>ماجستير في إدارة الأعمال</td>
                                <td><span class="cert-level level-master">ماجستير</span></td>
                                <td><span class="badge bg-primary">أكاديمية</span></td>
                                <td><span class="badge bg-info">2 سنة</span></td>
                                <td><span class="badge bg-success">5</span></td>
                                <td><span class="badge bg-success">نعم</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewCert('MSC-001')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editCert('MSC-001')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="الحاصلين" onclick="viewHolders('MSC-001')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>MSC-002</strong></td>
                                <td>ماجستير في الطب</td>
                                <td><span class="cert-level level-master">ماجستير</span></td>
                                <td><span class="badge bg-primary">أكاديمية</span></td>
                                <td><span class="badge bg-info">2-3 سنة</span></td>
                                <td><span class="badge bg-success">3</span></td>
                                <td><span class="badge bg-success">نعم</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewCert('MSC-002')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editCert('MSC-002')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="الحاصلين" onclick="viewHolders('MSC-002')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>BSC-001</strong></td>
                                <td>بكالوريوس طب وجراحة</td>
                                <td><span class="cert-level level-bachelor">بكالوريوس</span></td>
                                <td><span class="badge bg-primary">أكاديمية</span></td>
                                <td><span class="badge bg-info">6 سنوات</span></td>
                                <td><span class="badge bg-success">15</span></td>
                                <td><span class="badge bg-success">نعم</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewCert('BSC-001')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editCert('BSC-001')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="الحاصلين" onclick="viewHolders('BSC-001')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>BSC-002</strong></td>
                                <td>بكالوريوس محاسبة</td>
                                <td><span class="cert-level level-bachelor">بكالوريوس</span></td>
                                <td><span class="badge bg-primary">أكاديمية</span></td>
                                <td><span class="badge bg-info">4 سنوات</span></td>
                                <td><span class="badge bg-success">25</span></td>
                                <td><span class="badge bg-success">نعم</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewCert('BSC-002')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editCert('BSC-002')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="الحاصلين" onclick="viewHolders('BSC-002')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>BSC-003</strong></td>
                                <td>بكالوريوس إدارة أعمال</td>
                                <td><span class="cert-level level-bachelor">بكالوريوس</span></td>
                                <td><span class="badge bg-primary">أكاديمية</span></td>
                                <td><span class="badge bg-info">4 سنوات</span></td>
                                <td><span class="badge bg-success">18</span></td>
                                <td><span class="badge bg-success">نعم</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewCert('BSC-003')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editCert('BSC-003')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="الحاصلين" onclick="viewHolders('BSC-003')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>DIP-001</strong></td>
                                <td>دبلوم تقني محاسبة</td>
                                <td><span class="cert-level level-diploma">دبلوم</span></td>
                                <td><span class="badge bg-warning">تقنية</span></td>
                                <td><span class="badge bg-info">2 سنة</span></td>
                                <td><span class="badge bg-success">12</span></td>
                                <td><span class="badge bg-success">نعم</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewCert('DIP-001')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editCert('DIP-001')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="الحاصلين" onclick="viewHolders('DIP-001')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between align-items-center">
                    <span class="text-muted">عرض 8 من أصل 15 شهادة</span>
                    <div>
                        <button class="btn btn-outline-danger btn-sm me-2" onclick="exportCertificates()">
                            <i class="fas fa-file-excel me-1"></i>تصدير Excel
                        </button>
                        <button class="btn btn-outline-info btn-sm" onclick="printCertificates()">
                            <i class="fas fa-print me-1"></i>طباعة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // وظائف البحث والفلترة
        function filterCertificates() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const levelFilter = document.getElementById('levelFilter').value;
            const typeFilter = document.getElementById('typeFilter').value;
            
            const rows = document.querySelectorAll('#certificatesTableBody tr');
            
            rows.forEach(row => {
                const name = row.cells[1].textContent.toLowerCase();
                const level = row.cells[2].textContent;
                const type = row.cells[3].textContent;
                
                const matchesSearch = name.includes(searchTerm);
                const matchesLevel = !levelFilter || level.includes(levelFilter);
                const matchesType = !typeFilter || type.includes(typeFilter);
                
                if (matchesSearch && matchesLevel && matchesType) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }
        
        function clearFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('levelFilter').value = '';
            document.getElementById('typeFilter').value = '';
            filterCertificates();
        }
        
        // ربط أحداث البحث
        document.getElementById('searchInput').addEventListener('input', filterCertificates);
        document.getElementById('levelFilter').addEventListener('change', filterCertificates);
        document.getElementById('typeFilter').addEventListener('change', filterCertificates);
        
        // وظائف الإجراءات المحسنة
        function viewCert(code) {
            // البحث عن الشهادة في الجدول
            const rows = document.querySelectorAll('#certificatesTableBody tr');
            let certData = null;

            rows.forEach(row => {
                if (row.cells[0].textContent.trim() === code) {
                    certData = {
                        code: code,
                        name: row.cells[1].textContent,
                        level: row.cells[2].textContent,
                        type: row.cells[3].textContent,
                        duration: row.cells[4].textContent,
                        holders: row.cells[5].textContent,
                        accredited: row.cells[6].textContent
                    };
                }
            });

            if (!certData) {
                alert('لم يتم العثور على الشهادة المطلوبة');
                return;
            }

            // بيانات إضافية للشهادات
            const additionalData = {
                'PHD-001': {
                    description: 'شهادة الدكتوراه في الطب تؤهل الحاصل عليها لممارسة الطب والتخصص',
                    requirements: ['بكالوريوس طب وجراحة', 'إقامة طبية 4 سنوات', 'اجتياز امتحان البورد', 'رسالة دكتوراه'],
                    institutions: ['جامعة بغداد - كلية الطب', 'الجامعة المستنصرية - كلية الطب', 'جامعة البصرة - كلية الطب']
                },
                'BSC-002': {
                    description: 'شهادة البكالوريوس في المحاسبة تؤهل للعمل في مجال المحاسبة والمراجعة',
                    requirements: ['شهادة الثانوية العامة', 'معدل لا يقل عن 70%', 'اجتياز امتحان القبول'],
                    institutions: ['جامعة بغداد - كلية الإدارة والاقتصاد', 'الجامعة المستنصرية - كلية الإدارة والاقتصاد']
                }
            };

            const fullData = { ...certData, ...(additionalData[code] || {}) };

            const modal = `
                <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999; display: flex; align-items: center; justify-content: center;" onclick="this.remove()">
                    <div style="background: white; border-radius: 12px; padding: 30px; max-width: 700px; width: 90%; max-height: 80%; overflow-y: auto; box-shadow: 0 10px 30px rgba(0,0,0,0.3);" onclick="event.stopPropagation()">
                        <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 20px; border-bottom: 2px solid #007bff; padding-bottom: 15px;">
                            <h3 style="color: #007bff; margin: 0; display: flex; align-items: center;">
                                <i class="fas fa-certificate" style="margin-left: 10px;"></i>
                                تفاصيل الشهادة: ${certData.name}
                            </h3>
                            <button onclick="closeModal(this)" style="background: #dc3545; color: white; border: none; border-radius: 50%; width: 30px; height: 30px; cursor: pointer; transition: all 0.3s ease; display: flex; align-items: center; justify-content: center;" onmouseover="this.style.background='#c82333'; this.style.transform='scale(1.1)'" onmouseout="this.style.background='#dc3545'; this.style.transform='scale(1)'" title="إغلاق النافذة">×</button>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div>
                                <h5 style="color: #495057; margin-bottom: 10px;"><i class="fas fa-info-circle" style="margin-left: 5px;"></i>المعلومات الأساسية</h5>
                                <p><strong>الرمز:</strong> ${code}</p>
                                <p><strong>المستوى:</strong> <span style="background: #007bff; color: white; padding: 3px 8px; border-radius: 10px; font-size: 0.8rem;">${certData.level}</span></p>
                                <p><strong>النوع:</strong> <span style="background: #28a745; color: white; padding: 3px 8px; border-radius: 10px; font-size: 0.8rem;">${certData.type}</span></p>
                                <p><strong>مدة الدراسة:</strong> ${certData.duration}</p>
                                <p><strong>عدد الحاصلين:</strong> <span style="background: #ffc107; color: #333; padding: 2px 6px; border-radius: 8px;">${certData.holders}</span></p>
                                <p><strong>معتمدة:</strong> <span style="background: ${certData.accredited === 'نعم' ? '#28a745' : '#dc3545'}; color: white; padding: 2px 6px; border-radius: 8px;">${certData.accredited}</span></p>
                            </div>
                            <div>
                                <h5 style="color: #495057; margin-bottom: 10px;"><i class="fas fa-clipboard-list" style="margin-left: 5px;"></i>الوصف</h5>
                                <p style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-right: 4px solid #007bff;">${fullData.description || 'لا يوجد وصف متاح'}</p>
                            </div>
                        </div>

                        ${fullData.requirements ? `
                        <div style="margin-bottom: 20px;">
                            <h5 style="color: #495057; margin-bottom: 10px;"><i class="fas fa-check-circle" style="margin-left: 5px;"></i>المتطلبات والشروط</h5>
                            <ul style="background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 0;">
                                ${fullData.requirements.map(req => `<li style="margin-bottom: 5px;">${req}</li>`).join('')}
                            </ul>
                        </div>
                        ` : ''}

                        ${fullData.institutions ? `
                        <div style="margin-bottom: 20px;">
                            <h5 style="color: #495057; margin-bottom: 10px;"><i class="fas fa-university" style="margin-left: 5px;"></i>الجهات المانحة</h5>
                            <ul style="background: #fff3cd; padding: 15px; border-radius: 8px; margin: 0;">
                                ${fullData.institutions.map(inst => `<li style="margin-bottom: 5px;">${inst}</li>`).join('')}
                            </ul>
                        </div>
                        ` : ''}

                        <div style="display: flex; gap: 10px; justify-content: center; margin-top: 20px;">
                            <button onclick="editCert('${code}')" style="background: #ffc107; color: #333; border: none; padding: 10px 20px; border-radius: 8px; cursor: pointer; display: flex; align-items: center; gap: 5px; transition: all 0.3s ease;" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 4px 12px rgba(255,193,7,0.3)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                                <i class="fas fa-edit"></i> تعديل الشهادة
                            </button>
                            <button onclick="viewHolders('${code}')" style="background: #17a2b8; color: white; border: none; padding: 10px 20px; border-radius: 8px; cursor: pointer; display: flex; align-items: center; gap: 5px; transition: all 0.3s ease;" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 4px 12px rgba(23,162,184,0.3)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                                <i class="fas fa-users"></i> عرض الحاصلين
                            </button>
                            <button onclick="closeModal(this)" style="background: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 8px; cursor: pointer; display: flex; align-items: center; gap: 5px; transition: all 0.3s ease;" onmouseover="this.style.background='#dc3545'; this.style.transform='translateY(-2px)'; this.style.boxShadow='0 4px 12px rgba(220,53,69,0.3)'" onmouseout="this.style.background='#6c757d'; this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                                <i class="fas fa-times"></i> إغلاق
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modal);
        }

        function editCert(code) {
            alert(`تعديل الشهادة: ${code}\n\nيمكن تعديل:\n• اسم الشهادة\n• المستوى والنوع\n• مدة الدراسة\n• شروط الحصول\n• حالة الاعتماد`);
        }

        function viewHolders(code) {
            alert(`الحاصلين على الشهادة: ${code}\n\nسيتم عرض:\n• قائمة الموظفين\n• تواريخ الحصول\n• الجهات المانحة\n• درجات التقدير\n• الوثائق المرفقة`);
        }
        
        function showAddCertModal() {
            const modal = `
                <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999; display: flex; align-items: center; justify-content: center;" onclick="this.remove()">
                    <div style="background: white; border-radius: 12px; padding: 30px; max-width: 700px; width: 90%; max-height: 80%; overflow-y: auto; box-shadow: 0 10px 30px rgba(0,0,0,0.3);" onclick="event.stopPropagation()">
                        <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 20px; border-bottom: 2px solid #dc3545; padding-bottom: 15px;">
                            <h3 style="color: #dc3545; margin: 0; display: flex; align-items: center;">
                                <i class="fas fa-plus-circle" style="margin-left: 10px;"></i>
                                إضافة شهادة جديدة
                            </h3>
                            <button onclick="closeModal(this)" style="background: #dc3545; color: white; border: none; border-radius: 50%; width: 30px; height: 30px; cursor: pointer; transition: all 0.3s ease; display: flex; align-items: center; justify-content: center;" onmouseover="this.style.background='#c82333'; this.style.transform='scale(1.1)'" onmouseout="this.style.background='#dc3545'; this.style.transform='scale(1)'" title="إغلاق النافذة">×</button>
                        </div>

                        <form id="addCertForm" style="display: grid; gap: 15px;">
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                                <div>
                                    <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #495057;">رمز الشهادة</label>
                                    <input type="text" id="certCode" placeholder="مثال: BSC-004" style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'Cairo', sans-serif;" required>
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #495057;">اسم الشهادة</label>
                                    <input type="text" id="certName" placeholder="مثال: بكالوريوس هندسة" style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'Cairo', sans-serif;" required>
                                </div>
                            </div>

                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                                <div>
                                    <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #495057;">المستوى التعليمي</label>
                                    <select id="certLevel" style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'Cairo', sans-serif;" required>
                                        <option value="">اختر المستوى</option>
                                        <option value="دكتوراه">دكتوراه</option>
                                        <option value="ماجستير">ماجستير</option>
                                        <option value="بكالوريوس">بكالوريوس</option>
                                        <option value="دبلوم">دبلوم</option>
                                        <option value="ثانوية">ثانوية</option>
                                        <option value="مهنية">مهنية</option>
                                    </select>
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #495057;">نوع الشهادة</label>
                                    <select id="certType" style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'Cairo', sans-serif;" required>
                                        <option value="">اختر النوع</option>
                                        <option value="أكاديمية">أكاديمية</option>
                                        <option value="مهنية">مهنية</option>
                                        <option value="تقنية">تقنية</option>
                                    </select>
                                </div>
                            </div>

                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                                <div>
                                    <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #495057;">مدة الدراسة</label>
                                    <input type="text" id="certDuration" placeholder="مثال: 4 سنوات" style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'Cairo', sans-serif;" required>
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #495057;">معتمدة</label>
                                    <select id="certAccredited" style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'Cairo', sans-serif;" required>
                                        <option value="نعم">نعم</option>
                                        <option value="لا">لا</option>
                                    </select>
                                </div>
                            </div>

                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #495057;">الوصف والمتطلبات</label>
                                <textarea id="certDescription" rows="3" placeholder="وصف الشهادة والمتطلبات للحصول عليها..." style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'Cairo', sans-serif; resize: vertical;"></textarea>
                            </div>

                            <div style="display: flex; gap: 10px; justify-content: center; margin-top: 20px;">
                                <button type="button" onclick="addNewCertificate()" style="background: #dc3545; color: white; border: none; padding: 12px 25px; border-radius: 8px; cursor: pointer; display: flex; align-items: center; gap: 5px;">
                                    <i class="fas fa-plus"></i> إضافة الشهادة
                                </button>
                                <button type="button" onclick="closeModal(this)" style="background: #6c757d; color: white; border: none; padding: 12px 25px; border-radius: 8px; cursor: pointer; transition: all 0.3s ease; display: flex; align-items: center; gap: 5px;" onmouseover="this.style.background='#dc3545'; this.style.transform='translateY(-2px)'" onmouseout="this.style.background='#6c757d'; this.style.transform='translateY(0)'">
                                    <i class="fas fa-times"></i> إلغاء
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modal);
        }

        // وظيفة إضافة شهادة جديدة
        function addNewCertificate() {
            const code = document.getElementById('certCode').value;
            const name = document.getElementById('certName').value;
            const level = document.getElementById('certLevel').value;
            const type = document.getElementById('certType').value;
            const duration = document.getElementById('certDuration').value;
            const accredited = document.getElementById('certAccredited').value;
            const description = document.getElementById('certDescription').value;

            // التحقق من صحة البيانات
            if (!code || !name || !level || !type || !duration) {
                alert('⚠️ يرجى ملء جميع الحقول المطلوبة');
                return;
            }

            // التحقق من عدم تكرار الرمز
            const existingCodes = Array.from(document.querySelectorAll('#certificatesTableBody tr td:first-child')).map(td => td.textContent.trim());
            if (existingCodes.includes(code)) {
                alert('⚠️ رمز الشهادة موجود مسبقاً. يرجى اختيار رمز آخر.');
                return;
            }

            // إنشاء صف جديد في الجدول
            const newRow = createCertificateRow(code, name, level, type, duration, accredited);
            document.getElementById('certificatesTableBody').appendChild(newRow);

            // إغلاق النافذة
            const modal = document.querySelector('div[style*="position: fixed"]');
            if (modal) modal.remove();

            // رسالة تأكيد
            showSuccessMessage(`تم إضافة الشهادة "${name}" بنجاح!`);
        }

        // إنشاء صف جديد في الجدول
        function createCertificateRow(code, name, level, type, duration, accredited) {
            const row = document.createElement('tr');

            // تحديد فئة المستوى
            const levelClass = {
                'دكتوراه': 'level-phd',
                'ماجستير': 'level-master',
                'بكالوريوس': 'level-bachelor',
                'دبلوم': 'level-diploma',
                'ثانوية': 'level-secondary',
                'مهنية': 'level-certificate'
            }[level] || 'level-certificate';

            // تحديد لون النوع
            const typeClass = {
                'أكاديمية': 'bg-primary',
                'مهنية': 'bg-success',
                'تقنية': 'bg-warning'
            }[type] || 'bg-secondary';

            row.innerHTML = `
                <td><strong>${code}</strong></td>
                <td>${name}</td>
                <td><span class="cert-level ${levelClass}">${level}</span></td>
                <td><span class="badge ${typeClass}">${type}</span></td>
                <td><span class="badge bg-info">${duration}</span></td>
                <td><span class="badge bg-success">0</span></td>
                <td><span class="badge ${accredited === 'نعم' ? 'bg-success' : 'bg-danger'}">${accredited}</span></td>
                <td class="action-buttons">
                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewCert('${code}')">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editCert('${code}')">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-outline-primary btn-sm" title="الحاصلين" onclick="viewHolders('${code}')">
                        <i class="fas fa-users"></i>
                    </button>
                </td>
            `;

            return row;
        }

        // وظيفة إغلاق النافذة
        function closeModal(element) {
            const modal = element.closest('div[style*="position: fixed"]');
            if (modal) modal.remove();
        }

        // رسالة نجاح
        function showSuccessMessage(message) {
            const successModal = `
                <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999; display: flex; align-items: center; justify-content: center;">
                    <div style="background: white; border-radius: 12px; padding: 30px; max-width: 400px; width: 90%; text-align: center; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
                        <div style="color: #dc3545; font-size: 3rem; margin-bottom: 15px;">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <h4 style="color: #dc3545; margin-bottom: 15px;">تم بنجاح!</h4>
                        <p style="color: #6c757d; margin-bottom: 20px;">${message}</p>
                        <button onclick="this.closest('div').remove();" style="background: #dc3545; color: white; border: none; padding: 10px 20px; border-radius: 8px; cursor: pointer;">
                            موافق
                        </button>
                    </div>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', successModal);
        }

        function exportCertificates() {
            alert(`📊 تصدير الشهادات\n\nسيتم إنشاء ملف Excel يحتوي على:\n• جميع الشهادات والتفاصيل\n• إحصائيات الحاصلين\n• التصنيفات والمستويات\n• مخططات بيانية\n\nحجم الملف المتوقع: 900 KB`);
        }
        
        function printCertificates() {
            alert(`🖨️ طباعة دليل الشهادات\n\nسيتم إعداد:\n• دليل شامل للشهادات\n• التصنيفات والمستويات\n• إحصائيات مفصلة\n• مخططات بيانية\n\nعدد الصفحات المتوقع: 18 صفحة`);
        }

        console.log('✅ صفحة الشهادات محملة بنجاح');
        console.log('🎓 15 شهادة متاحة');
        console.log('📊 6 مستويات تعليمية');
    </script>
</body>
</html>
