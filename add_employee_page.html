<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة موظف جديد - نظام المحاسبة المتكامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: #f8f9fa;
        }
        
        .navbar {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .card {
            border: none;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
            border-radius: 12px;
        }
        
        .form-control, .form-select {
            border-radius: 8px;
            border: 2px solid #e9ecef;
            padding: 10px 15px;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
        
        .btn {
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 600;
        }
        
        .photo-upload {
            width: 150px;
            height: 150px;
            border: 3px dashed #dee2e6;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }
        
        .photo-upload:hover {
            border-color: #007bff;
            background: #e3f2fd;
        }
        
        .photo-preview {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            object-fit: cover;
            border: 3px solid #007bff;
        }
        
        .section-header {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .required {
            color: #dc3545;
        }
        
        .salary-calculator {
            background: #e8f5e8;
            border: 2px solid #28a745;
            border-radius: 8px;
            padding: 15px;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="simple_homepage.html">
                <i class="fas fa-calculator me-2"></i>
                نظام المحاسبة المتكامل
            </a>
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="simple_homepage.html">
                    <i class="fas fa-home me-1"></i>الرئيسية
                </a>
                <a class="nav-link" href="employees_page.html">
                    <i class="fas fa-users me-1"></i>الموظفون
                </a>
                <a class="nav-link active" href="add_employee_page.html">
                    <i class="fas fa-user-plus me-1"></i>إضافة موظف
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- عنوان الصفحة -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2 class="text-primary">
                        <i class="fas fa-user-plus me-2"></i>
                        إضافة موظف جديد
                    </h2>
                    <a href="employees_page.html" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-right me-1"></i>العودة للقائمة
                    </a>
                </div>
            </div>
        </div>

        <form id="addEmployeeForm">
            <div class="row">
                <!-- القسم الأول: الصورة الشخصية -->
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body text-center">
                            <h6 class="card-title">الصورة الشخصية</h6>
                            <div class="photo-upload mx-auto mb-3" onclick="document.getElementById('photoInput').click()">
                                <div id="photoPreview">
                                    <i class="fas fa-camera fa-2x text-muted"></i>
                                    <p class="mt-2 text-muted">انقر لرفع الصورة</p>
                                </div>
                            </div>
                            <input type="file" id="photoInput" accept="image/*" style="display: none;" onchange="previewPhoto(this)">
                            <small class="text-muted">الحد الأقصى: 2MB<br>الصيغ المدعومة: JPG, PNG</small>
                        </div>
                    </div>
                </div>

                <!-- القسم الثاني: النموذج الرئيسي -->
                <div class="col-md-9">
                    <!-- البيانات الأساسية -->
                    <div class="card mb-4">
                        <div class="section-header">
                            <h5 class="mb-0">
                                <i class="fas fa-user me-2"></i>
                                البيانات الأساسية
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الرقم الوظيفي <span class="required">*</span></label>
                                    <input type="text" class="form-control" id="employeeId" placeholder="مثال: 12006" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الاسم الكامل <span class="required">*</span></label>
                                    <input type="text" class="form-control" id="fullName" placeholder="الاسم الثلاثي واللقب" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">تاريخ الميلاد</label>
                                    <input type="date" class="form-control" id="birthDate">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الجنس</label>
                                    <select class="form-select" id="gender">
                                        <option value="">اختر الجنس</option>
                                        <option value="ذكر">ذكر</option>
                                        <option value="أنثى">أنثى</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">رقم الهوية/البطاقة الموحدة</label>
                                    <input type="text" class="form-control" id="nationalId" placeholder="رقم الهوية">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">رقم الهاتف</label>
                                    <input type="tel" class="form-control" id="phone" placeholder="07xxxxxxxxx">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات الوظيفة -->
                    <div class="card mb-4">
                        <div class="section-header">
                            <h5 class="mb-0">
                                <i class="fas fa-briefcase me-2"></i>
                                معلومات الوظيفة
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">المنصب <span class="required">*</span></label>
                                    <input type="text" class="form-control" id="position" placeholder="مثال: محاسب أول" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">القسم <span class="required">*</span></label>
                                    <select class="form-select" id="department" required>
                                        <option value="">اختر القسم</option>
                                        <option value="المحاسبة">المحاسبة</option>
                                        <option value="الشؤون الإدارية">الشؤون الإدارية</option>
                                        <option value="الشؤون الطبية">الشؤون الطبية</option>
                                        <option value="الموارد البشرية">الموارد البشرية</option>
                                        <option value="تكنولوجيا المعلومات">تكنولوجيا المعلومات</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الدرجة الوظيفية <span class="required">*</span></label>
                                    <select class="form-select" id="grade" onchange="calculateSalary()" required>
                                        <option value="">اختر الدرجة</option>
                                        <option value="الأولى" data-salary="950000">الدرجة الأولى</option>
                                        <option value="الثانية" data-salary="850000">الدرجة الثانية</option>
                                        <option value="الثالثة" data-salary="750000">الدرجة الثالثة</option>
                                        <option value="الرابعة" data-salary="650000">الدرجة الرابعة</option>
                                        <option value="الخامسة" data-salary="580000">الدرجة الخامسة</option>
                                        <option value="السادسة" data-salary="520000">الدرجة السادسة</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">تاريخ التعيين</label>
                                    <input type="date" class="form-control" id="hireDate">
                                </div>
                                <div class="col-md-12 mb-3">
                                    <label class="form-label">الحالة الوظيفية</label>
                                    <select class="form-select" id="status">
                                        <option value="نشط">نشط</option>
                                        <option value="معطل">معطل</option>
                                        <option value="في إجازة">في إجازة</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات الراتب -->
                    <div class="card mb-4">
                        <div class="section-header">
                            <h5 class="mb-0">
                                <i class="fas fa-money-bill-wave me-2"></i>
                                معلومات الراتب
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="salary-calculator mb-3">
                                <h6 class="text-success">حاسبة الراتب التلقائية</h6>
                                <p class="mb-0">سيتم حساب الراتب تلقائياً بناءً على الدرجة الوظيفية المختارة</p>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الراتب الأساسي (د.ع)</label>
                                    <input type="number" class="form-control" id="basicSalary" readonly>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">مخصصات إضافية (د.ع)</label>
                                    <input type="number" class="form-control" id="allowances" placeholder="0" onchange="calculateTotal()">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الاستقطاعات (د.ع)</label>
                                    <input type="number" class="form-control" id="deductions" placeholder="0" onchange="calculateTotal()">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">الراتب الصافي (د.ع)</label>
                                    <input type="number" class="form-control" id="netSalary" readonly style="background: #e8f5e8; font-weight: bold;">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الحفظ -->
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <button type="submit" class="btn btn-success btn-lg me-2">
                                        <i class="fas fa-save me-1"></i>حفظ الموظف
                                    </button>
                                    <button type="button" class="btn btn-info btn-lg me-2" onclick="saveAndAddAnother()">
                                        <i class="fas fa-plus me-1"></i>حفظ وإضافة آخر
                                    </button>
                                </div>
                                <div>
                                    <button type="reset" class="btn btn-warning me-2">
                                        <i class="fas fa-undo me-1"></i>إعادة تعيين
                                    </button>
                                    <a href="employees_page.html" class="btn btn-secondary">
                                        <i class="fas fa-times me-1"></i>إلغاء
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // معاينة الصورة
        function previewPhoto(input) {
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('photoPreview').innerHTML = 
                        `<img src="${e.target.result}" class="photo-preview" alt="صورة الموظف">`;
                }
                reader.readAsDataURL(input.files[0]);
            }
        }

        // حساب الراتب بناءً على الدرجة
        function calculateSalary() {
            const gradeSelect = document.getElementById('grade');
            const selectedOption = gradeSelect.options[gradeSelect.selectedIndex];
            const basicSalary = selectedOption.getAttribute('data-salary') || 0;
            
            document.getElementById('basicSalary').value = basicSalary;
            calculateTotal();
        }

        // حساب الراتب الصافي
        function calculateTotal() {
            const basic = parseFloat(document.getElementById('basicSalary').value) || 0;
            const allowances = parseFloat(document.getElementById('allowances').value) || 0;
            const deductions = parseFloat(document.getElementById('deductions').value) || 0;
            
            const netSalary = basic + allowances - deductions;
            document.getElementById('netSalary').value = netSalary;
        }

        // حفظ الموظف
        document.getElementById('addEmployeeForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = {
                employeeId: document.getElementById('employeeId').value,
                fullName: document.getElementById('fullName').value,
                position: document.getElementById('position').value,
                department: document.getElementById('department').value,
                grade: document.getElementById('grade').value,
                basicSalary: document.getElementById('basicSalary').value,
                netSalary: document.getElementById('netSalary').value,
                status: document.getElementById('status').value
            };
            
            // التحقق من البيانات المطلوبة
            if (!formData.employeeId || !formData.fullName || !formData.position || !formData.department || !formData.grade) {
                alert('⚠️ يرجى ملء جميع الحقول المطلوبة (المميزة بـ *)');
                return;
            }
            
            // محاكاة حفظ البيانات
            alert(`✅ تم حفظ الموظف بنجاح!\n\n` +
                  `الرقم الوظيفي: ${formData.employeeId}\n` +
                  `الاسم: ${formData.fullName}\n` +
                  `المنصب: ${formData.position}\n` +
                  `القسم: ${formData.department}\n` +
                  `الدرجة: ${formData.grade}\n` +
                  `الراتب الصافي: ${formData.netSalary} د.ع\n\n` +
                  `سيتم توجيهك إلى قائمة الموظفين...`);
            
            // التوجه إلى قائمة الموظفين
            setTimeout(() => {
                window.location.href = 'employees_page.html';
            }, 2000);
        });

        // حفظ وإضافة موظف آخر
        function saveAndAddAnother() {
            // حفظ البيانات الحالية
            const event = new Event('submit');
            document.getElementById('addEmployeeForm').dispatchEvent(event);
            
            // إعادة تعيين النموذج بعد الحفظ
            setTimeout(() => {
                document.getElementById('addEmployeeForm').reset();
                document.getElementById('photoPreview').innerHTML = 
                    `<i class="fas fa-camera fa-2x text-muted"></i><p class="mt-2 text-muted">انقر لرفع الصورة</p>`;
                alert('📝 تم مسح النموذج. يمكنك إضافة موظف جديد الآن.');
            }, 2500);
        }

        console.log('✅ صفحة إضافة موظف محملة بنجاح');
        console.log('📝 النموذج جاهز للاستخدام');
        console.log('🧮 حاسبة الراتب مفعلة');
    </script>
</body>
</html>
