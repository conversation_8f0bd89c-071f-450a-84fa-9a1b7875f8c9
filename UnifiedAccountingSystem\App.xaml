<?xml version="1.0" encoding="utf-8"?>
<Application x:Class="Application"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    StartupUri="MainWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="BlueGrey" SecondaryColor="Orange"/>
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
            </ResourceDictionary.MergedDictionaries>
            
            <!-- تعريف الألوان الرئيسية -->
            <Color x:Key="PrimaryColor">#2C3E50</Color>
            <Color x:Key="AccentColor">#E67E22</Color>
            <Color x:Key="BackgroundColor">#ECF0F1</Color>
            
            <!-- تعريف الفرش -->
            <SolidColorBrush x:Key="PrimaryBrush" Color="{StaticResource PrimaryColor}"/>
            <SolidColorBrush x:Key="AccentBrush" Color="{StaticResource AccentColor}"/>
            <SolidColorBrush x:Key="BackgroundBrush" Color="{StaticResource BackgroundColor}"/>
            
            <!-- نمط الأزرار -->
            <Style x:Key="MenuButton" TargetType="Button" BasedOn="{StaticResource MaterialDesignFlatButton}">
                <Setter Property="Height" Value="45"/>
                <Setter Property="FontFamily" Value="Cairo"/>
                <Setter Property="FontSize" Value="14"/>
                <Setter Property="Foreground" Value="White"/>
                <Setter Property="Background" Value="Transparent"/>
                <Setter Property="BorderThickness" Value="0"/>
                <Setter Property="Margin" Value="5"/>
                <Setter Property="Padding" Value="15,5"/>
                <Setter Property="HorizontalContentAlignment" Value="Right"/>
            </Style>
            
            <!-- نمط العناوين -->
            <Style x:Key="PageTitle" TargetType="TextBlock">
                <Setter Property="FontFamily" Value="Cairo"/>
                <Setter Property="FontSize" Value="24"/>
                <Setter Property="FontWeight" Value="Bold"/>
                <Setter Property="Margin" Value="20,10"/>
                <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
            </Style>
        </ResourceDictionary>
    </Application.Resources>
</Application>
