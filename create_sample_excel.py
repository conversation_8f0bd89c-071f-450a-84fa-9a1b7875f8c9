#!/usr/bin/env python3
"""
إنشاء ملف Excel تجريبي لاختبار استيراد الموظفين
Create Sample Excel File for Testing Employee Import
"""

import pandas as pd
from datetime import datetime, date

def create_sample_excel():
    """إنشاء ملف Excel تجريبي مع بيانات الموظفين"""
    
    # بيانات الموظفين التجريبية
    employees_data = [
        {
            'الرقم الوظيفي': '12001',
            'الاسم الكامل': 'أحمد محمد علي',
            'المنصب': 'محاسب أول',
            'الدرجة الوظيفية': 'الثالثة',
            'الراتب الأساسي': 750000,
            'الشهادة': 'بكالوريوس محاسبة',
            'المرحلة': 'الأولى',
            'رقم الهاتف': '07901234567',
            'البريد الإلكتروني': '<EMAIL>',
            'العنوان': 'بغداد - الكرادة'
        },
        {
            'الرقم الوظيفي': '12002',
            'الاسم الكامل': 'فاطمة حسن محمود',
            'المنصب': 'كاتبة',
            'الدرجة الوظيفية': 'الخامسة',
            'الراتب الأساسي': 580000,
            'الشهادة': 'إعدادية',
            'المرحلة': 'الثانية',
            'رقم الهاتف': '07801234567',
            'البريد الإلكتروني': '<EMAIL>',
            'العنوان': 'بغداد - الجادرية'
        },
        {
            'الرقم الوظيفي': '12003',
            'الاسم الكامل': 'محمد عبد الله أحمد',
            'المنصب': 'مدير الشعبة',
            'الدرجة الوظيفية': 'الأولى',
            'الراتب الأساسي': 950000,
            'الشهادة': 'ماجستير إدارة',
            'المرحلة': 'الأولى',
            'رقم الهاتف': '07701234567',
            'البريد الإلكتروني': '<EMAIL>',
            'العنوان': 'بغداد - المنصور'
        },
        {
            'الرقم الوظيفي': '12004',
            'الاسم الكامل': 'سارة علي حسين',
            'المنصب': 'محاسبة',
            'الدرجة الوظيفية': 'الرابعة',
            'الراتب الأساسي': 650000,
            'الشهادة': 'بكالوريوس',
            'المرحلة': 'الثانية',
            'رقم الهاتف': '07601234567',
            'البريد الإلكتروني': '<EMAIL>',
            'العنوان': 'بغداد - الدورة'
        },
        {
            'الرقم الوظيفي': '12005',
            'الاسم الكامل': 'عمر خالد محمد',
            'المنصب': 'مهندس',
            'الدرجة الوظيفية': 'الثانية',
            'الراتب الأساسي': 850000,
            'الشهادة': 'بكالوريوس هندسة',
            'المرحلة': 'الأولى',
            'رقم الهاتف': '07501234567',
            'البريد الإلكتروني': '<EMAIL>',
            'العنوان': 'بغداد - الكاظمية'
        },
        {
            'الرقم الوظيفي': '12006',
            'الاسم الكامل': 'نور الهدى عبد الرحمن',
            'المنصب': 'سكرتيرة',
            'الدرجة الوظيفية': 'السادسة',
            'الراتب الأساسي': 520000,
            'الشهادة': 'دبلوم',
            'المرحلة': 'الثالثة',
            'رقم الهاتف': '07401234567',
            'البريد الإلكتروني': '<EMAIL>',
            'العنوان': 'بغداد - الأعظمية'
        },
        {
            'الرقم الوظيفي': '12007',
            'الاسم الكامل': 'حسام الدين طارق',
            'المنصب': 'مراقب مالي',
            'الدرجة الوظيفية': 'الثالثة',
            'الراتب الأساسي': 780000,
            'الشهادة': 'بكالوريوس محاسبة',
            'المرحلة': 'الأولى',
            'رقم الهاتف': '07301234567',
            'البريد الإلكتروني': '<EMAIL>',
            'العنوان': 'بغداد - الرصافة'
        },
        {
            'الرقم الوظيفي': '12008',
            'الاسم الكامل': 'ليلى أحمد صالح',
            'المنصب': 'طبيبة',
            'الدرجة الوظيفية': 'الأولى',
            'الراتب الأساسي': 1200000,
            'الشهادة': 'دكتوراه طب',
            'المرحلة': 'الأولى',
            'رقم الهاتف': '07201234567',
            'البريد الإلكتروني': '<EMAIL>',
            'العنوان': 'بغداد - الكرخ'
        },
        {
            'الرقم الوظيفي': '12009',
            'الاسم الكامل': 'يوسف عبد الكريم',
            'المنصب': 'فني',
            'الدرجة الوظيفية': 'السابعة',
            'الراتب الأساسي': 480000,
            'الشهادة': 'إعدادية مهنية',
            'المرحلة': 'الرابعة',
            'رقم الهاتف': '07101234567',
            'البريد الإلكتروني': '<EMAIL>',
            'العنوان': 'بغداد - الشعلة'
        },
        {
            'الرقم الوظيفي': '12010',
            'الاسم الكامل': 'رنا محمود علي',
            'المنصب': 'مدققة',
            'الدرجة الوظيفية': 'الرابعة',
            'الراتب الأساسي': 680000,
            'الشهادة': 'بكالوريوس',
            'المرحلة': 'الثانية',
            'رقم الهاتف': '07001234567',
            'البريد الإلكتروني': '<EMAIL>',
            'العنوان': 'بغداد - الزعفرانية'
        },
        {
            'الرقم الوظيفي': '12011',
            'الاسم الكامل': 'كريم حسن عبد الله',
            'المنصب': 'مبرمج',
            'الدرجة الوظيفية': 'الثالثة',
            'الراتب الأساسي': 820000,
            'الشهادة': 'بكالوريوس حاسوب',
            'المرحلة': 'الأولى',
            'رقم الهاتف': '06901234567',
            'البريد الإلكتروني': '<EMAIL>',
            'العنوان': 'بغداد - الحرية'
        },
        {
            'الرقم الوظيفي': '12012',
            'الاسم الكامل': 'هدى صلاح الدين',
            'المنصب': 'مترجمة',
            'الدرجة الوظيفية': 'الرابعة',
            'الراتب الأساسي': 700000,
            'الشهادة': 'بكالوريوس لغة إنجليزية',
            'المرحلة': 'الثانية',
            'رقم الهاتف': '06801234567',
            'البريد الإلكتروني': '<EMAIL>',
            'العنوان': 'بغداد - الغزالية'
        },
        {
            'الرقم الوظيفي': '12013',
            'الاسم الكامل': 'باسم عادل محمد',
            'المنصب': 'حارس',
            'الدرجة الوظيفية': 'العاشرة',
            'الراتب الأساسي': 420000,
            'الشهادة': 'ابتدائية',
            'المرحلة': 'الخامسة',
            'رقم الهاتف': '06701234567',
            'البريد الإلكتروني': '',
            'العنوان': 'بغداد - الشعب'
        },
        {
            'الرقم الوظيفي': '12014',
            'الاسم الكامل': 'زينب فاضل حسين',
            'المنصب': 'ممرضة',
            'الدرجة الوظيفية': 'الخامسة',
            'الراتب الأساسي': 600000,
            'الشهادة': 'دبلوم تمريض',
            'المرحلة': 'الثانية',
            'رقم الهاتف': '06601234567',
            'البريد الإلكتروني': '<EMAIL>',
            'العنوان': 'بغداد - الطالبية'
        },
        {
            'الرقم الوظيفي': '12015',
            'الاسم الكامل': 'علي جاسم محمد',
            'المنصب': 'سائق',
            'الدرجة الوظيفية': 'التاسعة',
            'الراتب الأساسي': 450000,
            'الشهادة': 'متوسطة',
            'المرحلة': 'الرابعة',
            'رقم الهاتف': '06501234567',
            'البريد الإلكتروني': '',
            'العنوان': 'بغداد - الصدر'
        }
    ]
    
    # إنشاء DataFrame
    df = pd.DataFrame(employees_data)
    
    # إنشاء ملف Excel مع تنسيق
    with pd.ExcelWriter('sample_employees.xlsx', engine='xlsxwriter') as writer:
        df.to_excel(writer, sheet_name='الموظفون', index=False)
        
        # الحصول على workbook و worksheet
        workbook = writer.book
        worksheet = writer.sheets['الموظفون']
        
        # تنسيق الرأس
        header_format = workbook.add_format({
            'bold': True,
            'text_wrap': True,
            'valign': 'top',
            'fg_color': '#4472C4',
            'font_color': 'white',
            'border': 1,
            'font_size': 12
        })
        
        # تنسيق البيانات
        data_format = workbook.add_format({
            'text_wrap': True,
            'valign': 'top',
            'border': 1,
            'font_size': 11
        })
        
        # تنسيق الأرقام
        number_format = workbook.add_format({
            'num_format': '#,##0',
            'border': 1,
            'font_size': 11
        })
        
        # تطبيق التنسيق على الرأس
        for col_num, value in enumerate(df.columns.values):
            worksheet.write(0, col_num, value, header_format)
        
        # تطبيق التنسيق على البيانات
        for row_num in range(1, len(df) + 1):
            for col_num in range(len(df.columns)):
                if col_num == 4:  # عمود الراتب الأساسي
                    worksheet.write(row_num, col_num, df.iloc[row_num-1, col_num], number_format)
                else:
                    worksheet.write(row_num, col_num, df.iloc[row_num-1, col_num], data_format)
        
        # تعديل عرض الأعمدة
        column_widths = [15, 25, 20, 18, 15, 20, 12, 18, 30, 25]
        for i, width in enumerate(column_widths):
            worksheet.set_column(i, i, width)
        
        # إضافة تجميد للصف الأول
        worksheet.freeze_panes(1, 0)
    
    print("✅ تم إنشاء ملف sample_employees.xlsx بنجاح!")
    print(f"📊 يحتوي الملف على {len(employees_data)} موظف")
    print("📁 يمكنك استخدام هذا الملف لاختبار وظيفة الاستيراد")

if __name__ == "__main__":
    create_sample_excel()
