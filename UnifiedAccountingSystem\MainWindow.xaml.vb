Imports System.Windows
Imports System.Windows.Controls
Imports System.Windows.Media.Animation
Imports UnifiedAccountingSystem.Models
Imports UnifiedAccountingSystem.Views

Public Class MainWindow
    Private Property MenuCollapsed As Boolean = False
    Public Property CurrentUser As User

    Public Sub New()
        InitializeComponent()

        AddHandler ButtonToggleMenu.Click, AddressOf ToggleMenu
    End Sub

    Private Sub MainWindow_Loaded(sender As Object, e As RoutedEventArgs) Handles Me.Loaded
        ' تهيئة النافذة الرئيسية
        InitializeMainWindow()
    End Sub

    Private Sub InitializeMainWindow()
        ' تعيين اتجاه النص من اليمين إلى اليسار
        FlowDirection = FlowDirection.RightToLeft

        ' تحديث معلومات المستخدم
        UpdateUserInfo()

        ' تهيئة قائمة التنقل
        InitializeNavigation()

        ' تعيين الصفحة الافتراضية
        ListViewMenu.SelectedIndex = 0
    End Sub

    Private Sub UpdateUserInfo()
        If CurrentUser IsNot Nothing Then
            UserNameText.Text = CurrentUser.FullName
            ' يمكن إضافة المزيد من معلومات المستخدم هنا
        Else
            UserNameText.Text = "مستخدم غير معروف"
        End If
    End Sub

    Private Sub InitializeNavigation()
        ' إضافة معالجات الأحداث للقائمة
        For Each item As ListViewItem In TryCast(TryCast(GridMenu.Children(0), StackPanel).Children(1), ListView).Items
            AddHandler item.Selected, AddressOf NavigationItem_Selected
        Next
    End Sub

    Private Sub NavigationItem_Selected(sender As Object, e As RoutedEventArgs)
        ' الحصول على العنصر المحدد
        Dim item = TryCast(sender, ListViewItem)
        If item Is Nothing Then Return

        ' الحصول على نص العنصر
        Dim itemText = TryCast(TryCast(item.Content, StackPanel).Children(1), TextBlock).Text

        ' تنفيذ التنقل حسب العنصر المحدد
        Select Case itemText
            Case "المستخدمين"
                NavigateToUsers()
            Case "الإعدادات والتهيئة"
                NavigateToSettings()
            Case "الحسابات"
                NavigateToAccounts()
            Case "الموظفين"
                NavigateToEmployees()
            Case "الرواتب"
                NavigateToSalaries()
            Case "التقارير"
                NavigateToReports()
            Case "حول البرنامج"
                NavigateToAbout()
        End Select
    End Sub

    Private Sub NavigateToUsers()
        ' التنقل إلى صفحة المستخدمين
        MessageBox.Show("سيتم التنقل إلى صفحة المستخدمين")
        ' TODO: تنفيذ التنقل الفعلي
    End Sub

    Private Sub NavigateToSettings()
        ' التنقل إلى صفحة الإعدادات
        MessageBox.Show("سيتم التنقل إلى صفحة الإعدادات")
        ' TODO: تنفيذ التنقل الفعلي
    End Sub

    Private Sub NavigateToAccounts()
        ' التنقل إلى صفحة الحسابات
        MessageBox.Show("سيتم التنقل إلى صفحة الحسابات")
        ' TODO: تنفيذ التنقل الفعلي
    End Sub

    Private Sub NavigateToEmployees()
        ' التنقل إلى صفحة الموظفين
        MessageBox.Show("سيتم التنقل إلى صفحة الموظفين")
        ' TODO: تنفيذ التنقل الفعلي
    End Sub

    Private Sub NavigateToSalaries()
        ' التنقل إلى صفحة الرواتب
        MessageBox.Show("سيتم التنقل إلى صفحة الرواتب")
        ' TODO: تنفيذ التنقل الفعلي
    End Sub

    Private Sub NavigateToReports()
        ' التنقل إلى صفحة التقارير
        MessageBox.Show("سيتم التنقل إلى صفحة التقارير")
        ' TODO: تنفيذ التنقل الفعلي
    End Sub

    Private Sub NavigateToAbout()
        ' التنقل إلى صفحة حول البرنامج
        MessageBox.Show("سيتم التنقل إلى صفحة حول البرنامج")
        ' TODO: تنفيذ التنقل الفعلي
    End Sub

    Private Sub ListViewMenu_SelectionChanged(sender As Object, e As SelectionChangedEventArgs)
        Dim index = ListViewMenu.SelectedIndex
        MoveCursorMenu(index)
        
        Select Case DirectCast(ListViewMenu.SelectedItem, ListViewItem).Tag.ToString()
            Case "Users"
                CurrentPageTitle.Text = "إدارة المستخدمين"
                ' PageContent.Navigate(New UsersView())
            Case "Settings"
                CurrentPageTitle.Text = "الإعدادات"
                ' PageContent.Navigate(New SettingsView())
            Case "Accounts"
                CurrentPageTitle.Text = "الحسابات"
                ' PageContent.Navigate(New AccountsView())
            Case "Employees"
                CurrentPageTitle.Text = "إدارة الموظفين"
                PageContent.Navigate(New EmployeesView())
            Case "Salaries"
                CurrentPageTitle.Text = "الرواتب"
                ' PageContent.Navigate(New SalariesView())
            Case "Reports"
                CurrentPageTitle.Text = "التقارير"
                ' PageContent.Navigate(New ReportsView())
        End Select
    End Sub

    Private Sub MoveCursorMenu(index As Integer)
        ' يمكن إضافة تأثيرات حركية للقائمة هنا
    End Sub

    Private Sub ToggleMenu(sender As Object, e As RoutedEventArgs)
        If MenuCollapsed Then
            Dim sb As Storyboard = TryCast(FindResource("MenuOpen"), Storyboard)
            sb.Begin(GridMenu)
        Else
            Dim sb As Storyboard = TryCast(FindResource("MenuClose"), Storyboard)
            sb.Begin(GridMenu)
        End If
        MenuCollapsed = Not MenuCollapsed
    End Sub
End Class
