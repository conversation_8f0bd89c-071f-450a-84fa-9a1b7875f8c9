# -*- coding: utf-8 -*-
"""
نموذج الرواتب الشهرية
Monthly Salary Model
"""

from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, Date, ForeignKey, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from database.database_manager import Base
from datetime import datetime, date

class MonthlySalary(Base):
    __tablename__ = "monthly_salaries"
    
    id = Column(Integer, primary_key=True, index=True)
    employee_id = Column(Integer, ForeignKey("employees.id"), nullable=False)
    month = Column(Integer, nullable=False)  # 1-12
    year = Column(Integer, nullable=False)
    
    # مكونات الراتب
    basic_salary = Column(Float, nullable=False, default=0.0)
    total_allowances = Column(Float, default=0.0)
    total_deductions = Column(Float, default=0.0)
    gross_salary = Column(Float, default=0.0)  # الراتب الإجمالي
    net_salary = Column(Float, nullable=False, default=0.0)  # الراتب الصافي
    
    # معلومات المعالجة
    is_processed = Column(Boolean, default=False)
    processed_at = Column(DateTime)
    processed_by = Column(String(50))
    
    # معلومات إضافية
    working_days = Column(Integer, default=30)
    actual_working_days = Column(Integer, default=30)
    overtime_hours = Column(Float, default=0.0)
    overtime_amount = Column(Float, default=0.0)
    bonus_amount = Column(Float, default=0.0)
    penalty_amount = Column(Float, default=0.0)
    
    # ملاحظات
    notes = Column(Text)
    
    # تواريخ النظام
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    
    # العلاقات
    employee = relationship("Employee", back_populates="monthly_salaries")
    
    def __init__(self, employee_id, month, year, basic_salary=0.0, **kwargs):
        self.employee_id = employee_id
        self.month = month
        self.year = year
        self.basic_salary = basic_salary
        
        # معلومات إضافية
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def calculate_salary(self):
        """حساب الراتب الشهري"""
        from database.database_manager import db_manager
        
        session = db_manager.get_session()
        try:
            # الحصول على بيانات الموظف
            employee = session.query(Employee).filter(Employee.id == self.employee_id).first()
            if not employee:
                return False
            
            # حساب المخصصات
            self.total_allowances = sum(
                allowance.amount for allowance in employee.allowances 
                if allowance.is_active
            )
            
            # حساب الاستقطاعات
            self.total_deductions = 0
            for deduction in employee.deductions:
                if not deduction.is_active:
                    continue
                
                if deduction.percentage:
                    self.total_deductions += self.basic_salary * (deduction.percentage / 100)
                else:
                    self.total_deductions += deduction.amount
            
            # حساب الراتب الإجمالي
            self.gross_salary = self.basic_salary + self.total_allowances + self.overtime_amount + self.bonus_amount
            
            # حساب الراتب الصافي
            total_deductions = self.total_deductions + self.penalty_amount
            self.net_salary = max(0, self.gross_salary - total_deductions)
            
            return True
            
        except Exception as e:
            print(f"خطأ في حساب الراتب: {e}")
            return False
        finally:
            session.close()
    
    def process_salary(self, processed_by=None):
        """معالجة الراتب"""
        if self.calculate_salary():
            self.is_processed = True
            self.processed_at = datetime.now()
            self.processed_by = processed_by
            return True
        return False
    
    def unprocess_salary(self):
        """إلغاء معالجة الراتب"""
        self.is_processed = False
        self.processed_at = None
        self.processed_by = None
    
    def get_month_name(self):
        """الحصول على اسم الشهر بالعربية"""
        months_arabic = {
            1: "كانون الثاني", 2: "شباط", 3: "آذار", 4: "نيسان",
            5: "أيار", 6: "حزيران", 7: "تموز", 8: "آب",
            9: "أيلول", 10: "تشرين الأول", 11: "تشرين الثاني", 12: "كانون الأول"
        }
        return months_arabic.get(self.month, str(self.month))
    
    def get_period_display(self):
        """الحصول على عرض الفترة"""
        return f"{self.get_month_name()} {self.year}"
    
    def calculate_attendance_ratio(self):
        """حساب نسبة الحضور"""
        if self.working_days > 0:
            return (self.actual_working_days / self.working_days) * 100
        return 100
    
    def get_salary_breakdown(self):
        """الحصول على تفصيل الراتب"""
        return {
            'basic_salary': self.basic_salary,
            'allowances': self.total_allowances,
            'overtime': self.overtime_amount,
            'bonus': self.bonus_amount,
            'gross_salary': self.gross_salary,
            'deductions': self.total_deductions,
            'penalties': self.penalty_amount,
            'total_deductions': self.total_deductions + self.penalty_amount,
            'net_salary': self.net_salary
        }
    
    @classmethod
    def get_salary_by_period(cls, month, year, employee_id=None):
        """الحصول على الرواتب لفترة معينة"""
        from database.database_manager import db_manager
        
        session = db_manager.get_session()
        try:
            query = session.query(cls).filter(cls.month == month, cls.year == year)
            
            if employee_id:
                query = query.filter(cls.employee_id == employee_id)
            
            return query.all()
        finally:
            session.close()
    
    @classmethod
    def get_employee_salary_history(cls, employee_id, limit=12):
        """الحصول على تاريخ رواتب الموظف"""
        from database.database_manager import db_manager
        
        session = db_manager.get_session()
        try:
            return session.query(cls).filter(
                cls.employee_id == employee_id
            ).order_by(cls.year.desc(), cls.month.desc()).limit(limit).all()
        finally:
            session.close()
    
    def to_dict(self):
        """تحويل إلى قاموس"""
        return {
            'id': self.id,
            'employee_id': self.employee_id,
            'month': self.month,
            'year': self.year,
            'month_name': self.get_month_name(),
            'period_display': self.get_period_display(),
            'basic_salary': self.basic_salary,
            'total_allowances': self.total_allowances,
            'total_deductions': self.total_deductions,
            'gross_salary': self.gross_salary,
            'net_salary': self.net_salary,
            'working_days': self.working_days,
            'actual_working_days': self.actual_working_days,
            'attendance_ratio': self.calculate_attendance_ratio(),
            'overtime_hours': self.overtime_hours,
            'overtime_amount': self.overtime_amount,
            'bonus_amount': self.bonus_amount,
            'penalty_amount': self.penalty_amount,
            'is_processed': self.is_processed,
            'processed_at': self.processed_at.isoformat() if self.processed_at else None,
            'processed_by': self.processed_by,
            'notes': self.notes,
            'salary_breakdown': self.get_salary_breakdown()
        }
    
    def __repr__(self):
        return f"<MonthlySalary(employee_id={self.employee_id}, period='{self.get_period_display()}', net_salary={self.net_salary})>"
