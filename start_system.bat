@echo off
chcp 65001 >nul
title نظام المحاسبة المتكامل - وزارة الشباب والرياضة

echo.
echo ================================================================
echo                    نظام المحاسبة المتكامل
echo              وزارة الشباب والرياضة - دائرة الطب الرياضي
echo ================================================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: Python غير مثبت على النظام
    echo يرجى تحميل وتثبيت Python 3.10 أو أحدث من:
    echo https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ تم العثور على Python

REM التحقق من وجود pip
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: pip غير متوفر
    pause
    exit /b 1
)

echo ✅ تم العثور على pip

REM التحقق من وجود ملف المتطلبات
if not exist "requirements.txt" (
    echo ❌ خطأ: ملف requirements.txt غير موجود
    pause
    exit /b 1
)

echo ✅ ملف المتطلبات موجود

:MENU
echo.
echo ================================================================
echo                         القائمة الرئيسية
echo ================================================================
echo.
echo 1. تشغيل تطبيق سطح المكتب
echo 2. تشغيل تطبيق الويب
echo 3. تثبيت المتطلبات
echo 4. إعداد قاعدة البيانات
echo 5. تشغيل النظام الكامل (إعداد + تشغيل)
echo 6. فتح مجلد النظام
echo 7. عرض معلومات النظام
echo 0. خروج
echo.
echo ================================================================

set /p choice="اختر رقماً من القائمة: "

if "%choice%"=="1" goto DESKTOP
if "%choice%"=="2" goto WEB
if "%choice%"=="3" goto INSTALL
if "%choice%"=="4" goto SETUP_DB
if "%choice%"=="5" goto FULL_SETUP
if "%choice%"=="6" goto OPEN_FOLDER
if "%choice%"=="7" goto SYSTEM_INFO
if "%choice%"=="0" goto EXIT

echo ❌ خيار غير صحيح، يرجى المحاولة مرة أخرى
goto MENU

:DESKTOP
echo.
echo 🖥️ جاري تشغيل تطبيق سطح المكتب...
echo.
python main.py
if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في تشغيل التطبيق
    echo يرجى التأكد من تثبيت المتطلبات أولاً
    pause
)
goto MENU

:WEB
echo.
echo 🌐 جاري تشغيل تطبيق الويب...
echo 📍 سيتم فتح الرابط: http://localhost:8000
echo 💡 لإيقاف الخادم اضغط Ctrl+C
echo.
timeout /t 3 >nul
start http://localhost:8000
python web_main.py
if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في تشغيل الخادم
    echo يرجى التأكد من تثبيت المتطلبات أولاً
    pause
)
goto MENU

:INSTALL
echo.
echo 📦 جاري تثبيت المتطلبات...
echo.
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ فشل في تثبيت المتطلبات
    pause
) else (
    echo ✅ تم تثبيت المتطلبات بنجاح
    pause
)
goto MENU

:SETUP_DB
echo.
echo 🗄️ جاري إعداد قاعدة البيانات...
echo.
python -c "from config.settings import create_directories; create_directories(); print('✅ تم إعداد قاعدة البيانات بنجاح')"
if errorlevel 1 (
    echo ❌ فشل في إعداد قاعدة البيانات
    pause
) else (
    pause
)
goto MENU

:FULL_SETUP
echo.
echo 🚀 جاري الإعداد الكامل للنظام...
echo.

echo 📦 الخطوة 1: تثبيت المتطلبات...
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ فشل في تثبيت المتطلبات
    pause
    goto MENU
)

echo 🗄️ الخطوة 2: إعداد قاعدة البيانات...
python -c "from config.settings import create_directories; create_directories()"
if errorlevel 1 (
    echo ❌ فشل في إعداد قاعدة البيانات
    pause
    goto MENU
)

echo ✅ تم الإعداد بنجاح!
echo.
echo اختر وضع التشغيل:
echo 1. تطبيق سطح المكتب
echo 2. تطبيق الويب
echo.
set /p run_choice="اختيارك: "

if "%run_choice%"=="1" (
    echo 🖥️ جاري تشغيل تطبيق سطح المكتب...
    python main.py
) else if "%run_choice%"=="2" (
    echo 🌐 جاري تشغيل تطبيق الويب...
    start http://localhost:8000
    python web_main.py
)

goto MENU

:OPEN_FOLDER
echo.
echo 📁 فتح مجلد النظام...
explorer .
goto MENU

:SYSTEM_INFO
echo.
echo ================================================================
echo                        معلومات النظام
echo ================================================================
echo.
echo 🏛️ المؤسسة: وزارة الشباب والرياضة
echo 🏢 الدائرة: دائرة الطب الرياضي
echo 📋 القسم: قسم إداري
echo 💼 الشعبة: شعبة الحسابات
echo.
echo 🏦 الحسابات البنكية:
echo    - حساب تشغيلي: 069100011822001
echo    - حساب رواتب: 069100011822002
echo    - البنك: مصرف الرافدين - فرع دور الضباط (69)
echo.
echo 💻 معلومات تقنية:
python --version
echo 📁 المجلد: %CD%
echo 🕒 التاريخ: %DATE% %TIME%
echo.
echo ================================================================
pause
goto MENU

:EXIT
echo.
echo 👋 شكراً لاستخدام نظام المحاسبة المتكامل
echo    وزارة الشباب والرياضة - دائرة الطب الرياضي
echo.
timeout /t 2 >nul
exit /b 0
