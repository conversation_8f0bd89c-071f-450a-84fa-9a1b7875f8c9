# 🚀 دليل البدء السريع
## نظام المحاسبة المتكامل

---

## ⚡ التشغيل السريع (3 خطوات)

### 1️⃣ تشغيل ملف الإعداد
```bash
# في Windows
start_system.bat

# أو باستخدام Python
python run_system.py
```

### 2️⃣ اختيار الخيار 5 (الإعداد الكامل)
- سيتم تثبيت المتطلبات تلقائياً
- سيتم إعداد قاعدة البيانات
- اختر وضع التشغيل المطلوب

### 3️⃣ تسجيل الدخول
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

---

## 🖥️ تطبيق سطح المكتب

### التشغيل المباشر
```bash
python main.py
```

### المميزات
- ✅ واجهة عربية كاملة RTL
- ✅ إدارة الموظفين والرواتب
- ✅ تقارير PDF وExcel
- ✅ نسخ احتياطي تلقائي

---

## 🌐 تطبيق الويب

### التشغيل
```bash
python web_main.py
```

### الوصول
- **الرابط:** http://localhost:8000
- **المتصفح:** أي متصفح حديث
- **الهاتف:** يدعم الهواتف الذكية

---

## 📋 المتطلبات السريعة

### النظام
- Windows 7+ أو Linux أو macOS
- Python 3.10+
- 4 GB RAM
- 500 MB مساحة فارغة

### التثبيت التلقائي
```bash
pip install -r requirements.txt
```

---

## 🏛️ بيانات المؤسسة

### الهيكل التنظيمي
```
وزارة الشباب والرياضة
└── دائرة الطب الرياضي
    └── قسم إداري
        └── شعبة الحسابات
```

### الحسابات البنكية
- **تشغيلي:** 069100011822001
- **رواتب:** 069100011822002
- **البنك:** مصرف الرافدين - فرع دور الضباط (69)

---

## 👤 المستخدمون الافتراضيون

| المستخدم | كلمة المرور | الصلاحية |
|----------|-------------|----------|
| admin    | admin123    | مدير النظام |

> ⚠️ **مهم:** غير كلمة المرور فور الدخول الأول

---

## 🔧 الإعدادات السريعة

### تغيير قاعدة البيانات
```python
# في config/settings.py
DATABASE_CONFIG['type'] = 'sqlserver'  # أو 'sqlite'
```

### تخصيص المخصصات
```python
# في config/settings.py
SALARY_CONFIG['allowances'] = [
    'منصب', 'زوجية', 'أولاد', 'هندسية'
]
```

---

## 📊 الوظائف الأساسية

### إضافة موظف
1. الموظفون → إضافة موظف جديد
2. املأ البيانات الأساسية
3. أضف المخصصات والاستقطاعات
4. احفظ

### معالجة الرواتب
1. الرواتب → معالجة الرواتب
2. اختر الشهر والسنة
3. اضغط "معالجة الكل"
4. راجع النتائج

### إنشاء تقرير
1. التقارير → اختر نوع التقرير
2. حدد المعايير والفترة
3. اختر صيغة التصدير
4. اضغط "إنشاء"

---

## 🛠️ استكشاف الأخطاء

### مشاكل شائعة

#### خطأ في تشغيل التطبيق
```bash
# الحل
pip install --upgrade -r requirements.txt
python run_system.py --setup
```

#### مشكلة في النصوص العربية
```bash
# تأكد من تثبيت الخطوط
# أو استخدم خط النظام الافتراضي
```

#### بطء في الأداء
```bash
# تنظيف قاعدة البيانات
python -c "from database.database_manager import db_manager; db_manager.backup_database()"
```

---

## 📞 الدعم السريع

### الأخطاء الطارئة
1. أعد تشغيل التطبيق
2. تحقق من ملف logs/
3. أنشئ نسخة احتياطية
4. اتصل بالدعم

### معلومات الاتصال
- **البريد:** <EMAIL>
- **الهاتف:** +964-1-XXXXXXX

---

## 🎯 نصائح سريعة

### للمبتدئين
- ابدأ بإضافة 2-3 موظفين للتجربة
- استخدم البيانات الوهمية أولاً
- اقرأ دليل المستخدم كاملاً

### للمتقدمين
- استخدم SQL Server للمؤسسات الكبيرة
- فعل النسخ الاحتياطي التلقائي
- خصص التقارير حسب الحاجة

### للمطورين
- راجع ملف setup.py للتخصيص
- استخدم virtual environment
- اتبع معايير PEP 8

---

## 🔄 التحديثات

### التحقق من التحديثات
```bash
git pull origin main
pip install --upgrade -r requirements.txt
```

### النسخ الاحتياطي قبل التحديث
```bash
python -c "from database.database_manager import db_manager; db_manager.backup_database()"
```

---

## ✅ قائمة التحقق

- [ ] تم تثبيت Python 3.10+
- [ ] تم تثبيت المتطلبات
- [ ] تم إعداد قاعدة البيانات
- [ ] تم تسجيل الدخول بنجاح
- [ ] تم إضافة موظف تجريبي
- [ ] تم إنشاء تقرير تجريبي
- [ ] تم إنشاء نسخة احتياطية

---

**🎉 مبروك! النظام جاهز للاستخدام**

للمساعدة الإضافية، راجع ملف README.md الكامل أو اتصل بفريق الدعم.

---

*© 2024 وزارة الشباب والرياضة - دائرة الطب الرياضي*
