{% extends "base.html" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-file-excel me-2 text-success"></i>
                استيراد الموظفين من Excel
            </h1>
            <a href="/employees" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-1"></i>
                العودة إلى قائمة الموظفين
            </a>
        </div>
    </div>
</div>

<div class="row">
    <!-- تعليمات الاستيراد -->
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    تعليمات الاستيراد
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6 class="alert-heading">
                        <i class="fas fa-lightbulb me-2"></i>
                        متطلبات ملف Excel:
                    </h6>
                    <ul class="mb-0">
                        <li>يجب أن يكون الملف بصيغة <code>.xlsx</code> أو <code>.xls</code></li>
                        <li>الصف الأول يجب أن يحتوي على أسماء الأعمدة</li>
                        <li>الأعمدة المطلوبة: الرقم الوظيفي، الاسم الكامل، المنصب، الدرجة الوظيفية، الراتب الأساسي</li>
                        <li>الأعمدة الاختيارية: الشهادة، المرحلة، رقم الهاتف، البريد الإلكتروني، العنوان</li>
                    </ul>
                </div>
                
                <h6 class="mt-4 mb-3">
                    <i class="fas fa-table me-2"></i>
                    تنسيق الأعمدة المطلوب:
                </h6>
                
                <div class="table-responsive">
                    <table class="table table-bordered table-sm">
                        <thead class="table-dark">
                            <tr>
                                <th>اسم العمود</th>
                                <th>مطلوب</th>
                                <th>نوع البيانات</th>
                                <th>مثال</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>الرقم الوظيفي</strong></td>
                                <td><span class="badge bg-danger">مطلوب</span></td>
                                <td>نص</td>
                                <td>12001</td>
                            </tr>
                            <tr>
                                <td><strong>الاسم الكامل</strong></td>
                                <td><span class="badge bg-danger">مطلوب</span></td>
                                <td>نص</td>
                                <td>أحمد محمد علي</td>
                            </tr>
                            <tr>
                                <td><strong>المنصب</strong></td>
                                <td><span class="badge bg-danger">مطلوب</span></td>
                                <td>نص</td>
                                <td>محاسب أول</td>
                            </tr>
                            <tr>
                                <td><strong>الدرجة الوظيفية</strong></td>
                                <td><span class="badge bg-danger">مطلوب</span></td>
                                <td>نص</td>
                                <td>الثالثة</td>
                            </tr>
                            <tr>
                                <td><strong>الراتب الأساسي</strong></td>
                                <td><span class="badge bg-danger">مطلوب</span></td>
                                <td>رقم</td>
                                <td>750000</td>
                            </tr>
                            <tr>
                                <td><strong>الشهادة</strong></td>
                                <td><span class="badge bg-secondary">اختياري</span></td>
                                <td>نص</td>
                                <td>بكالوريوس</td>
                            </tr>
                            <tr>
                                <td><strong>المرحلة</strong></td>
                                <td><span class="badge bg-secondary">اختياري</span></td>
                                <td>نص</td>
                                <td>الأولى</td>
                            </tr>
                            <tr>
                                <td><strong>رقم الهاتف</strong></td>
                                <td><span class="badge bg-secondary">اختياري</span></td>
                                <td>نص</td>
                                <td>07901234567</td>
                            </tr>
                            <tr>
                                <td><strong>البريد الإلكتروني</strong></td>
                                <td><span class="badge bg-secondary">اختياري</span></td>
                                <td>نص</td>
                                <td><EMAIL></td>
                            </tr>
                            <tr>
                                <td><strong>العنوان</strong></td>
                                <td><span class="badge bg-secondary">اختياري</span></td>
                                <td>نص</td>
                                <td>بغداد - الكرادة</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <!-- نموذج رفع الملف -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-upload me-2"></i>
                    رفع ملف Excel
                </h5>
            </div>
            <div class="card-body">
                <form method="post" enctype="multipart/form-data" id="importForm">
                    <div class="mb-4">
                        <label for="excel_file" class="form-label">
                            <i class="fas fa-file-excel me-1"></i>
                            اختر ملف Excel
                        </label>
                        <input type="file" class="form-control" id="excel_file" name="excel_file" 
                               accept=".xlsx,.xls" required>
                        <div class="form-text">
                            الحد الأقصى لحجم الملف: 10 MB | الصيغ المدعومة: .xlsx, .xls
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label for="sheet_name" class="form-label">
                            <i class="fas fa-table me-1"></i>
                            اسم الورقة (اختياري)
                        </label>
                        <input type="text" class="form-control" id="sheet_name" name="sheet_name" 
                               placeholder="اتركه فارغاً لاستخدام الورقة الأولى">
                        <div class="form-text">
                            إذا كان الملف يحتوي على عدة أوراق، حدد اسم الورقة المطلوبة
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="skip_duplicates" name="skip_duplicates" checked>
                            <label class="form-check-label" for="skip_duplicates">
                                تجاهل الموظفين المكررين (بناءً على الرقم الوظيفي)
                            </label>
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="add_default_deductions" name="add_default_deductions" checked>
                            <label class="form-check-label" for="add_default_deductions">
                                إضافة الاستقطاعات الافتراضية (تقاعد 10%، مساهمة حكومية 15%)
                            </label>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-start">
                        <button type="submit" class="btn btn-success btn-lg me-md-2">
                            <i class="fas fa-upload me-1"></i>
                            بدء الاستيراد
                        </button>
                        <button type="button" class="btn btn-outline-info btn-lg" onclick="previewFile()">
                            <i class="fas fa-eye me-1"></i>
                            معاينة الملف
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- الأدوات المساعدة -->
    <div class="col-lg-4">
        <!-- تحميل قالب Excel -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-download me-2"></i>
                    قالب Excel
                </h5>
            </div>
            <div class="card-body">
                <p class="card-text">
                    احصل على قالب Excel جاهز مع جميع الأعمدة المطلوبة وأمثلة على البيانات.
                </p>
                <div class="d-grid">
                    <a href="/employees/template/excel" class="btn btn-primary">
                        <i class="fas fa-download me-1"></i>
                        تحميل القالب
                    </a>
                </div>
            </div>
        </div>
        
        <!-- إحصائيات الاستيراد -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    إحصائيات الاستيراد
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between mb-2">
                    <span>آخر استيراد:</span>
                    <span id="last_import">لم يتم بعد</span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <span>عدد الموظفين المستوردين:</span>
                    <span id="imported_count">0</span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <span>الأخطاء:</span>
                    <span id="error_count" class="text-danger">0</span>
                </div>
            </div>
        </div>
        
        <!-- نصائح مهمة -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    نصائح مهمة
                </h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        تأكد من صحة البيانات قبل الاستيراد
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        احفظ نسخة احتياطية قبل الاستيراد
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        تجنب الأرقام الوظيفية المكررة
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        استخدم القالب المتوفر لضمان التوافق
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- نافذة معاينة الملف -->
<div class="modal fade" id="previewModal" tabindex="-1" aria-labelledby="previewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="previewModalLabel">
                    <i class="fas fa-eye me-2"></i>
                    معاينة ملف Excel
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="previewContent">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                        <p class="mt-2">جاري تحليل الملف...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-success" onclick="proceedWithImport()">
                    <i class="fas fa-upload me-1"></i>
                    متابعة الاستيراد
                </button>
            </div>
        </div>
    </div>
</div>

<!-- نافذة تقدم الاستيراد -->
<div class="modal fade" id="progressModal" tabindex="-1" aria-labelledby="progressModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="progressModalLabel">
                    <i class="fas fa-upload me-2"></i>
                    جاري الاستيراد...
                </h5>
            </div>
            <div class="modal-body">
                <div class="progress mb-3">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" 
                         role="progressbar" style="width: 0%" id="importProgress"></div>
                </div>
                <p id="progressText">بدء عملية الاستيراد...</p>
                <div id="importResults" style="display: none;">
                    <div class="alert alert-success">
                        <h6 class="alert-heading">تم الاستيراد بنجاح!</h6>
                        <p class="mb-0">
                            تم استيراد <span id="successCount">0</span> موظف بنجاح.
                            <span id="errorInfo" style="display: none;">
                                وفشل في استيراد <span id="failCount">0</span> موظف.
                            </span>
                        </p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" onclick="window.location.href='/employees'" id="finishButton" style="display: none;">
                    <i class="fas fa-check me-1"></i>
                    انتهاء
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.card-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.table th {
    font-weight: 600;
    text-align: center;
    vertical-align: middle;
}

.badge {
    font-size: 0.8rem;
}

.form-control:focus {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.progress-bar {
    background-color: #28a745;
}

.alert-heading {
    margin-bottom: 0.5rem;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function previewFile() {
    const fileInput = document.getElementById('excel_file');
    const file = fileInput.files[0];
    
    if (!file) {
        alert('يرجى اختيار ملف Excel أولاً');
        return;
    }
    
    // عرض نافذة المعاينة
    const modal = new bootstrap.Modal(document.getElementById('previewModal'));
    modal.show();
    
    // محاكاة تحليل الملف
    setTimeout(() => {
        document.getElementById('previewContent').innerHTML = `
            <div class="alert alert-info">
                <h6>معلومات الملف:</h6>
                <p><strong>اسم الملف:</strong> ${file.name}</p>
                <p><strong>حجم الملف:</strong> ${(file.size / 1024 / 1024).toFixed(2)} MB</p>
                <p><strong>عدد الصفوف المتوقع:</strong> 25 صف</p>
            </div>
            <div class="table-responsive">
                <table class="table table-sm table-bordered">
                    <thead class="table-dark">
                        <tr>
                            <th>الرقم الوظيفي</th>
                            <th>الاسم الكامل</th>
                            <th>المنصب</th>
                            <th>الدرجة الوظيفية</th>
                            <th>الراتب الأساسي</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>12001</td>
                            <td>أحمد محمد علي</td>
                            <td>محاسب أول</td>
                            <td>الثالثة</td>
                            <td>750,000</td>
                        </tr>
                        <tr>
                            <td>12002</td>
                            <td>فاطمة حسن محمود</td>
                            <td>كاتبة</td>
                            <td>الخامسة</td>
                            <td>580,000</td>
                        </tr>
                        <tr>
                            <td colspan="5" class="text-center text-muted">... والمزيد</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        `;
    }, 2000);
}

function proceedWithImport() {
    // إغلاق نافذة المعاينة
    bootstrap.Modal.getInstance(document.getElementById('previewModal')).hide();
    
    // تشغيل نموذج الاستيراد
    document.getElementById('importForm').submit();
}

// معالجة نموذج الاستيراد
document.getElementById('importForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const fileInput = document.getElementById('excel_file');
    if (!fileInput.files[0]) {
        alert('يرجى اختيار ملف Excel');
        return;
    }
    
    // عرض نافذة التقدم
    const progressModal = new bootstrap.Modal(document.getElementById('progressModal'));
    progressModal.show();
    
    // محاكاة عملية الاستيراد
    simulateImport();
});

function simulateImport() {
    const progressBar = document.getElementById('importProgress');
    const progressText = document.getElementById('progressText');
    let progress = 0;
    
    const interval = setInterval(() => {
        progress += Math.random() * 15;
        if (progress > 100) progress = 100;
        
        progressBar.style.width = progress + '%';
        
        if (progress < 30) {
            progressText.textContent = 'قراءة ملف Excel...';
        } else if (progress < 60) {
            progressText.textContent = 'تحليل البيانات...';
        } else if (progress < 90) {
            progressText.textContent = 'إدراج الموظفين في قاعدة البيانات...';
        } else {
            progressText.textContent = 'اكتمال الاستيراد...';
        }
        
        if (progress >= 100) {
            clearInterval(interval);
            showImportResults();
        }
    }, 200);
}

function showImportResults() {
    document.getElementById('progressText').style.display = 'none';
    document.getElementById('importResults').style.display = 'block';
    document.getElementById('successCount').textContent = '23';
    document.getElementById('finishButton').style.display = 'block';
}

// التحقق من نوع الملف
document.getElementById('excel_file').addEventListener('change', function() {
    const file = this.files[0];
    if (file) {
        const validTypes = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 
                           'application/vnd.ms-excel'];
        
        if (!validTypes.includes(file.type)) {
            alert('يرجى اختيار ملف Excel صحيح (.xlsx أو .xls)');
            this.value = '';
            return;
        }
        
        if (file.size > 10 * 1024 * 1024) { // 10MB
            alert('حجم الملف كبير جداً. الحد الأقصى 10 MB');
            this.value = '';
            return;
        }
    }
});
</script>
{% endblock %}
