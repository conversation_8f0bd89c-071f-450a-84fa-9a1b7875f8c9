<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دليل المناصب - نظام المحاسبة المتكامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: #f8f9fa;
        }
        
        .navbar {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .card {
            border: none;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
            border-radius: 12px;
        }
        
        .table th {
            background: #343a40;
            color: white;
            text-align: center;
            border: none;
        }
        
        .table td {
            vertical-align: middle;
            text-align: center;
        }
        
        .badge {
            font-size: 0.8rem;
        }
        
        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }
        
        .search-box {
            border-radius: 25px;
            border: 2px solid #e9ecef;
            padding: 10px 20px;
        }
        
        .search-box:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
        
        .action-buttons .btn {
            margin: 2px;
        }
        
        .position-level {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .level-executive {
            background: #dc3545;
            color: white;
        }
        
        .level-senior {
            background: #fd7e14;
            color: white;
        }
        
        .level-middle {
            background: #ffc107;
            color: #333;
        }
        
        .level-junior {
            background: #28a745;
            color: white;
        }
        
        .level-entry {
            background: #6c757d;
            color: white;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="simple_homepage.html">
                <i class="fas fa-calculator me-2"></i>
                نظام المحاسبة المتكامل
            </a>
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="simple_homepage.html">
                    <i class="fas fa-home me-1"></i>الرئيسية
                </a>
                <a class="nav-link" href="employees_page.html">
                    <i class="fas fa-users me-1"></i>الموظفون
                </a>
                <a class="nav-link" href="basic_lists_page.html">
                    <i class="fas fa-list me-1"></i>القوائم الأساسية
                </a>
                <a class="nav-link active" href="positions_list.html">
                    <i class="fas fa-user-tie me-1"></i>دليل المناصب
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- عنوان الصفحة -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2 class="text-primary">
                        <i class="fas fa-user-tie me-2"></i>
                        دليل المناصب الوظيفية
                    </h2>
                    <div>
                        <button class="btn btn-success me-2" onclick="showAddPositionModal()">
                            <i class="fas fa-plus me-1"></i>إضافة منصب
                        </button>
                        <a href="basic_lists_page.html" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right me-1"></i>العودة للقوائم
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="row mb-4">
            <div class="col-md-2">
                <div class="card bg-danger text-white">
                    <div class="card-body text-center">
                        <h4>3</h4>
                        <small>تنفيذي</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card bg-warning text-dark">
                    <div class="card-body text-center">
                        <h4>5</h4>
                        <small>أول</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h4>8</h4>
                        <small>متوسط</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h4>6</h4>
                        <small>مبتدئ</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card bg-secondary text-white">
                    <div class="card-body text-center">
                        <h4>3</h4>
                        <small>مساعد</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h4>25</h4>
                        <small>المجموع</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- شريط البحث والفلترة -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-search"></i>
                                    </span>
                                    <input type="text" class="form-control search-box" placeholder="البحث عن منصب..." id="searchInput">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="levelFilter">
                                    <option value="">جميع المستويات</option>
                                    <option value="تنفيذي">تنفيذي</option>
                                    <option value="أول">أول</option>
                                    <option value="متوسط">متوسط</option>
                                    <option value="مبتدئ">مبتدئ</option>
                                    <option value="مساعد">مساعد</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="departmentFilter">
                                    <option value="">جميع الأقسام</option>
                                    <option value="الإدارة العليا">الإدارة العليا</option>
                                    <option value="المحاسبة">المحاسبة</option>
                                    <option value="الشؤون الإدارية">الشؤون الإدارية</option>
                                    <option value="الشؤون الطبية">الشؤون الطبية</option>
                                    <option value="تكنولوجيا المعلومات">تكنولوجيا المعلومات</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button class="btn btn-outline-secondary w-100" onclick="clearFilters()">
                                    <i class="fas fa-times me-1"></i>مسح
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- جدول المناصب -->
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>قائمة المناصب الوظيفية
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0" id="positionsTable">
                        <thead>
                            <tr>
                                <th>الرمز</th>
                                <th>اسم المنصب</th>
                                <th>المستوى</th>
                                <th>القسم</th>
                                <th>الدرجة المطلوبة</th>
                                <th>الراتب الأساسي</th>
                                <th>عدد الموظفين</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="positionsTableBody">
                            <tr>
                                <td><strong>P001</strong></td>
                                <td>مدير عام</td>
                                <td><span class="position-level level-executive">تنفيذي</span></td>
                                <td>الإدارة العليا</td>
                                <td><span class="badge bg-info">خاص</span></td>
                                <td><strong>1,500,000 د.ع</strong></td>
                                <td><span class="badge bg-success">1</span></td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewPosition('P001')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editPosition('P001')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="الموظفين" onclick="viewEmployees('P001')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>P002</strong></td>
                                <td>مدير قسم المحاسبة</td>
                                <td><span class="position-level level-senior">أول</span></td>
                                <td>المحاسبة</td>
                                <td><span class="badge bg-info">الأولى</span></td>
                                <td><strong>1,200,000 د.ع</strong></td>
                                <td><span class="badge bg-success">1</span></td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewPosition('P002')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editPosition('P002')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="الموظفين" onclick="viewEmployees('P002')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>P003</strong></td>
                                <td>محاسب أول</td>
                                <td><span class="position-level level-middle">متوسط</span></td>
                                <td>المحاسبة</td>
                                <td><span class="badge bg-info">الثانية</span></td>
                                <td><strong>950,000 د.ع</strong></td>
                                <td><span class="badge bg-success">3</span></td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewPosition('P003')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editPosition('P003')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="الموظفين" onclick="viewEmployees('P003')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>P004</strong></td>
                                <td>محاسب</td>
                                <td><span class="position-level level-middle">متوسط</span></td>
                                <td>المحاسبة</td>
                                <td><span class="badge bg-info">الثالثة</span></td>
                                <td><strong>750,000 د.ع</strong></td>
                                <td><span class="badge bg-success">5</span></td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewPosition('P004')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editPosition('P004')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="الموظفين" onclick="viewEmployees('P004')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>P005</strong></td>
                                <td>طبيب اختصاص</td>
                                <td><span class="position-level level-senior">أول</span></td>
                                <td>الشؤون الطبية</td>
                                <td><span class="badge bg-info">الأولى</span></td>
                                <td><strong>1,100,000 د.ع</strong></td>
                                <td><span class="badge bg-success">2</span></td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewPosition('P005')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editPosition('P005')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="الموظفين" onclick="viewEmployees('P005')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>P006</strong></td>
                                <td>طبيب عام</td>
                                <td><span class="position-level level-middle">متوسط</span></td>
                                <td>الشؤون الطبية</td>
                                <td><span class="badge bg-info">الثانية</span></td>
                                <td><strong>850,000 د.ع</strong></td>
                                <td><span class="badge bg-success">4</span></td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewPosition('P006')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editPosition('P006')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="الموظفين" onclick="viewEmployees('P006')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>P007</strong></td>
                                <td>كاتب</td>
                                <td><span class="position-level level-junior">مبتدئ</span></td>
                                <td>الشؤون الإدارية</td>
                                <td><span class="badge bg-info">الخامسة</span></td>
                                <td><strong>580,000 د.ع</strong></td>
                                <td><span class="badge bg-success">6</span></td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewPosition('P007')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editPosition('P007')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="الموظفين" onclick="viewEmployees('P007')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>P008</strong></td>
                                <td>مساعد إداري</td>
                                <td><span class="position-level level-entry">مساعد</span></td>
                                <td>الشؤون الإدارية</td>
                                <td><span class="badge bg-info">السادسة</span></td>
                                <td><strong>520,000 د.ع</strong></td>
                                <td><span class="badge bg-success">3</span></td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewPosition('P008')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editPosition('P008')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="الموظفين" onclick="viewEmployees('P008')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between align-items-center">
                    <span class="text-muted">عرض 8 من أصل 25 منصب</span>
                    <div>
                        <button class="btn btn-outline-primary btn-sm me-2" onclick="exportPositions()">
                            <i class="fas fa-file-excel me-1"></i>تصدير Excel
                        </button>
                        <button class="btn btn-outline-info btn-sm" onclick="printPositions()">
                            <i class="fas fa-print me-1"></i>طباعة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // وظائف البحث والفلترة
        function filterPositions() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const levelFilter = document.getElementById('levelFilter').value;
            const departmentFilter = document.getElementById('departmentFilter').value;
            
            const rows = document.querySelectorAll('#positionsTableBody tr');
            
            rows.forEach(row => {
                const name = row.cells[1].textContent.toLowerCase();
                const level = row.cells[2].textContent;
                const department = row.cells[3].textContent;
                
                const matchesSearch = name.includes(searchTerm);
                const matchesLevel = !levelFilter || level.includes(levelFilter);
                const matchesDepartment = !departmentFilter || department.includes(departmentFilter);
                
                if (matchesSearch && matchesLevel && matchesDepartment) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }
        
        function clearFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('levelFilter').value = '';
            document.getElementById('departmentFilter').value = '';
            filterPositions();
        }
        
        // ربط أحداث البحث
        document.getElementById('searchInput').addEventListener('input', filterPositions);
        document.getElementById('levelFilter').addEventListener('change', filterPositions);
        document.getElementById('departmentFilter').addEventListener('change', filterPositions);
        
        // وظائف الإجراءات
        function viewPosition(code) {
            alert(`عرض تفاصيل المنصب: ${code}\n\nسيتم عرض:\n• الوصف الوظيفي الكامل\n• المتطلبات والمؤهلات\n• المسؤوليات والواجبات\n• هيكل الراتب والمخصصات\n• مسار التطوير الوظيفي`);
        }
        
        function editPosition(code) {
            alert(`تعديل المنصب: ${code}\n\nيمكن تعديل:\n• اسم المنصب\n• المستوى الوظيفي\n• القسم التابع له\n• الدرجة المطلوبة\n• الراتب الأساسي\n• الوصف الوظيفي`);
        }
        
        function viewEmployees(code) {
            alert(`موظفو المنصب: ${code}\n\nسيتم عرض:\n• قائمة الموظفين الحاليين\n• الشواغر المتاحة\n• تاريخ التعيينات\n• إحصائيات الأداء\n• خطط التوظيف المستقبلية`);
        }
        
        function showAddPositionModal() {
            alert(`إضافة منصب جديد\n\nالحقول المطلوبة:\n• رمز المنصب\n• اسم المنصب\n• المستوى الوظيفي\n• القسم\n• الدرجة المطلوبة\n• الراتب الأساسي\n• الوصف الوظيفي\n• المتطلبات`);
        }
        
        function exportPositions() {
            alert(`📊 تصدير دليل المناصب\n\nسيتم إنشاء ملف Excel يحتوي على:\n• جميع المناصب الوظيفية\n• التفاصيل الكاملة\n• الإحصائيات\n• المخططات البيانية\n\nحجم الملف المتوقع: 1.2 MB`);
        }
        
        function printPositions() {
            alert(`🖨️ طباعة دليل المناصب\n\nسيتم إعداد:\n• تقرير مفصل للطباعة\n• تنسيق احترافي\n• شعار المؤسسة\n• فهرس المحتويات\n\nعدد الصفحات المتوقع: 15 صفحة`);
        }

        console.log('✅ صفحة دليل المناصب محملة بنجاح');
        console.log('🔍 البحث والفلترة متاحة');
        console.log('📋 25 منصب وظيفي متاح');
    </script>
</body>
</html>
