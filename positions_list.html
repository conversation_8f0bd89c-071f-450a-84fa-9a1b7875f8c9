<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>دليل المناصب - نظام المحاسبة المتكامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: #f8f9fa;
        }
        
        .navbar {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .card {
            border: none;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
            border-radius: 12px;
        }
        
        .table th {
            background: #343a40;
            color: white;
            text-align: center;
            border: none;
        }
        
        .table td {
            vertical-align: middle;
            text-align: center;
        }
        
        .badge {
            font-size: 0.8rem;
        }
        
        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }
        
        .search-box {
            border-radius: 25px;
            border: 2px solid #e9ecef;
            padding: 10px 20px;
        }
        
        .search-box:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }
        
        .action-buttons {
            white-space: nowrap;
            text-align: center;
        }

        .action-buttons .btn {
            margin: 2px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            border-width: 2px;
        }

        .action-buttons .btn:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 1;
        }

        .action-buttons .btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255,255,255,0.3);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: all 0.3s ease;
            z-index: -1;
        }

        .action-buttons .btn:active::before {
            width: 100%;
            height: 100%;
        }

        .btn-outline-info:hover {
            background: linear-gradient(135deg, #17a2b8, #138496) !important;
            border-color: #17a2b8 !important;
            color: white !important;
            box-shadow: 0 0 15px rgba(23, 162, 184, 0.4) !important;
        }

        .btn-outline-warning:hover {
            background: linear-gradient(135deg, #ffc107, #e0a800) !important;
            border-color: #ffc107 !important;
            color: #212529 !important;
            box-shadow: 0 0 15px rgba(255, 193, 7, 0.4) !important;
        }

        .btn-outline-primary:hover {
            background: linear-gradient(135deg, #007bff, #0056b3) !important;
            border-color: #007bff !important;
            color: white !important;
            box-shadow: 0 0 15px rgba(0, 123, 255, 0.4) !important;
        }

        /* تأثيرات خاصة للأيقونات */
        .action-buttons .btn i {
            transition: all 0.3s ease;
        }

        .action-buttons .btn:hover i {
            transform: scale(1.2);
        }

        /* تأثير النبض للأزرار */
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .action-buttons .btn:focus {
            animation: pulse 0.6s ease-in-out;
        }
        
        .position-level {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .level-executive {
            background: #dc3545;
            color: white;
        }
        
        .level-senior {
            background: #fd7e14;
            color: white;
        }
        
        .level-middle {
            background: #ffc107;
            color: #333;
        }
        
        .level-junior {
            background: #28a745;
            color: white;
        }
        
        .level-entry {
            background: #6c757d;
            color: white;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="simple_homepage.html">
                <i class="fas fa-calculator me-2"></i>
                نظام المحاسبة المتكامل
            </a>
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="simple_homepage.html">
                    <i class="fas fa-home me-1"></i>الرئيسية
                </a>
                <a class="nav-link" href="employees_page.html">
                    <i class="fas fa-users me-1"></i>الموظفون
                </a>
                <a class="nav-link" href="basic_lists_page.html">
                    <i class="fas fa-list me-1"></i>القوائم الأساسية
                </a>
                <a class="nav-link active" href="positions_list.html">
                    <i class="fas fa-user-tie me-1"></i>دليل المناصب
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- عنوان الصفحة -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2 class="text-primary">
                        <i class="fas fa-user-tie me-2"></i>
                        دليل المناصب الوظيفية
                    </h2>
                    <div>
                        <button class="btn btn-success me-2" onclick="showAddPositionModal()">
                            <i class="fas fa-plus me-1"></i>إضافة منصب
                        </button>
                        <a href="basic_lists_page.html" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-right me-1"></i>العودة للقوائم
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات سريعة -->
        <div class="row mb-4">
            <div class="col-md-2">
                <div class="card bg-danger text-white">
                    <div class="card-body text-center">
                        <h4>3</h4>
                        <small>تنفيذي</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card bg-warning text-dark">
                    <div class="card-body text-center">
                        <h4>5</h4>
                        <small>أول</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h4>8</h4>
                        <small>متوسط</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h4>6</h4>
                        <small>مبتدئ</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card bg-secondary text-white">
                    <div class="card-body text-center">
                        <h4>3</h4>
                        <small>مساعد</small>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h4>25</h4>
                        <small>المجموع</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- شريط البحث والفلترة -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-search"></i>
                                    </span>
                                    <input type="text" class="form-control search-box" placeholder="البحث عن منصب..." id="searchInput">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="levelFilter">
                                    <option value="">جميع المستويات</option>
                                    <option value="تنفيذي">تنفيذي</option>
                                    <option value="أول">أول</option>
                                    <option value="متوسط">متوسط</option>
                                    <option value="مبتدئ">مبتدئ</option>
                                    <option value="مساعد">مساعد</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="departmentFilter">
                                    <option value="">جميع الأقسام</option>
                                    <option value="الإدارة العليا">الإدارة العليا</option>
                                    <option value="المحاسبة">المحاسبة</option>
                                    <option value="الشؤون الإدارية">الشؤون الإدارية</option>
                                    <option value="الشؤون الطبية">الشؤون الطبية</option>
                                    <option value="تكنولوجيا المعلومات">تكنولوجيا المعلومات</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button class="btn btn-outline-secondary w-100" onclick="clearFilters()">
                                    <i class="fas fa-times me-1"></i>مسح
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- جدول المناصب -->
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>قائمة المناصب الوظيفية
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0" id="positionsTable">
                        <thead>
                            <tr>
                                <th>الرمز</th>
                                <th>اسم المنصب</th>
                                <th>المستوى</th>
                                <th>القسم</th>
                                <th>الدرجة المطلوبة</th>
                                <th>الراتب الأساسي</th>
                                <th>عدد الموظفين</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="positionsTableBody">
                            <tr>
                                <td><strong>P001</strong></td>
                                <td>مدير عام</td>
                                <td><span class="position-level level-executive">تنفيذي</span></td>
                                <td>الإدارة العليا</td>
                                <td><span class="badge bg-info">خاص</span></td>
                                <td><strong>1,500,000 د.ع</strong></td>
                                <td><span class="badge bg-success">1</span></td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewPosition('P001')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editPosition('P001')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="الموظفين" onclick="viewEmployees('P001')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>P002</strong></td>
                                <td>مدير قسم المحاسبة</td>
                                <td><span class="position-level level-senior">أول</span></td>
                                <td>المحاسبة</td>
                                <td><span class="badge bg-info">الأولى</span></td>
                                <td><strong>1,200,000 د.ع</strong></td>
                                <td><span class="badge bg-success">1</span></td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewPosition('P002')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editPosition('P002')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="الموظفين" onclick="viewEmployees('P002')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>P003</strong></td>
                                <td>محاسب أول</td>
                                <td><span class="position-level level-middle">متوسط</span></td>
                                <td>المحاسبة</td>
                                <td><span class="badge bg-info">الثانية</span></td>
                                <td><strong>950,000 د.ع</strong></td>
                                <td><span class="badge bg-success">3</span></td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewPosition('P003')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editPosition('P003')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="الموظفين" onclick="viewEmployees('P003')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>P004</strong></td>
                                <td>محاسب</td>
                                <td><span class="position-level level-middle">متوسط</span></td>
                                <td>المحاسبة</td>
                                <td><span class="badge bg-info">الثالثة</span></td>
                                <td><strong>750,000 د.ع</strong></td>
                                <td><span class="badge bg-success">5</span></td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewPosition('P004')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editPosition('P004')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="الموظفين" onclick="viewEmployees('P004')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>P005</strong></td>
                                <td>طبيب اختصاص</td>
                                <td><span class="position-level level-senior">أول</span></td>
                                <td>الشؤون الطبية</td>
                                <td><span class="badge bg-info">الأولى</span></td>
                                <td><strong>1,100,000 د.ع</strong></td>
                                <td><span class="badge bg-success">2</span></td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewPosition('P005')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editPosition('P005')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="الموظفين" onclick="viewEmployees('P005')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>P006</strong></td>
                                <td>طبيب عام</td>
                                <td><span class="position-level level-middle">متوسط</span></td>
                                <td>الشؤون الطبية</td>
                                <td><span class="badge bg-info">الثانية</span></td>
                                <td><strong>850,000 د.ع</strong></td>
                                <td><span class="badge bg-success">4</span></td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewPosition('P006')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editPosition('P006')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="الموظفين" onclick="viewEmployees('P006')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>P007</strong></td>
                                <td>كاتب</td>
                                <td><span class="position-level level-junior">مبتدئ</span></td>
                                <td>الشؤون الإدارية</td>
                                <td><span class="badge bg-info">الخامسة</span></td>
                                <td><strong>580,000 د.ع</strong></td>
                                <td><span class="badge bg-success">6</span></td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewPosition('P007')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editPosition('P007')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="الموظفين" onclick="viewEmployees('P007')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>P008</strong></td>
                                <td>مساعد إداري</td>
                                <td><span class="position-level level-entry">مساعد</span></td>
                                <td>الشؤون الإدارية</td>
                                <td><span class="badge bg-info">السادسة</span></td>
                                <td><strong>520,000 د.ع</strong></td>
                                <td><span class="badge bg-success">3</span></td>
                                <td><span class="badge bg-success">نشط</span></td>
                                <td class="action-buttons">
                                    <button class="btn btn-outline-info btn-sm" title="عرض التفاصيل" onclick="viewPosition('P008')">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" title="تعديل" onclick="editPosition('P008')">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-outline-primary btn-sm" title="الموظفين" onclick="viewEmployees('P008')">
                                        <i class="fas fa-users"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="card-footer">
                <div class="d-flex justify-content-between align-items-center">
                    <span class="text-muted">عرض 8 من أصل 25 منصب</span>
                    <div>
                        <button class="btn btn-outline-primary btn-sm me-2" onclick="exportPositions()">
                            <i class="fas fa-file-excel me-1"></i>تصدير Excel
                        </button>
                        <button class="btn btn-outline-info btn-sm" onclick="printPositions()">
                            <i class="fas fa-print me-1"></i>طباعة
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // وظائف البحث والفلترة
        function filterPositions() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const levelFilter = document.getElementById('levelFilter').value;
            const departmentFilter = document.getElementById('departmentFilter').value;
            
            const rows = document.querySelectorAll('#positionsTableBody tr');
            
            rows.forEach(row => {
                const name = row.cells[1].textContent.toLowerCase();
                const level = row.cells[2].textContent;
                const department = row.cells[3].textContent;
                
                const matchesSearch = name.includes(searchTerm);
                const matchesLevel = !levelFilter || level.includes(levelFilter);
                const matchesDepartment = !departmentFilter || department.includes(departmentFilter);
                
                if (matchesSearch && matchesLevel && matchesDepartment) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }
        
        function clearFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('levelFilter').value = '';
            document.getElementById('departmentFilter').value = '';
            filterPositions();
        }
        
        // ربط أحداث البحث
        document.getElementById('searchInput').addEventListener('input', filterPositions);
        document.getElementById('levelFilter').addEventListener('change', filterPositions);
        document.getElementById('departmentFilter').addEventListener('change', filterPositions);
        
        // وظائف الإجراءات المحسنة
        function viewPosition(code) {
            // بيانات المناصب التفصيلية
            const positions = {
                'P001': {
                    name: 'مدير عام',
                    level: 'تنفيذي',
                    department: 'الإدارة العليا',
                    grade: 'خاص',
                    salary: '1,500,000',
                    employees: 1,
                    description: 'المسؤول الأول عن إدارة المؤسسة وتوجيه السياسات العامة',
                    requirements: ['خبرة 20+ سنة', 'شهادة عليا في الإدارة', 'مهارات قيادية متقدمة'],
                    responsibilities: ['وضع الاستراتيجيات', 'الإشراف على الأقسام', 'اتخاذ القرارات الاستراتيجية']
                },
                'P002': {
                    name: 'مدير قسم المحاسبة',
                    level: 'أول',
                    department: 'المحاسبة',
                    grade: 'الأولى',
                    salary: '1,200,000',
                    employees: 1,
                    description: 'مسؤول عن إدارة جميع العمليات المحاسبية والمالية',
                    requirements: ['خبرة 15+ سنة', 'بكالوريوس محاسبة', 'شهادة CPA مفضلة'],
                    responsibilities: ['إعداد التقارير المالية', 'مراجعة الحسابات', 'إدارة فريق المحاسبة']
                },
                'P003': {
                    name: 'محاسب أول',
                    level: 'متوسط',
                    department: 'المحاسبة',
                    grade: 'الثانية',
                    salary: '950,000',
                    employees: 3,
                    description: 'محاسب متخصص في العمليات المحاسبية المعقدة',
                    requirements: ['خبرة 10+ سنوات', 'بكالوريوس محاسبة', 'إتقان برامج المحاسبة'],
                    responsibilities: ['إعداد القوائم المالية', 'مراجعة العمليات', 'تدريب المحاسبين الجدد']
                }
            };

            const position = positions[code] || {
                name: 'منصب غير محدد',
                description: 'لا توجد تفاصيل متاحة'
            };

            // إنشاء نافذة تفاصيل تفاعلية
            const modal = `
                <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999; display: flex; align-items: center; justify-content: center;" onclick="this.remove()">
                    <div style="background: white; border-radius: 12px; padding: 30px; max-width: 600px; width: 90%; max-height: 80%; overflow-y: auto; box-shadow: 0 10px 30px rgba(0,0,0,0.3);" onclick="event.stopPropagation()">
                        <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 20px; border-bottom: 2px solid #007bff; padding-bottom: 15px;">
                            <h3 style="color: #007bff; margin: 0; display: flex; align-items: center;">
                                <i class="fas fa-user-tie" style="margin-left: 10px;"></i>
                                تفاصيل المنصب: ${position.name}
                            </h3>
                            <button onclick="closeModal(this)" style="background: #dc3545; color: white; border: none; border-radius: 50%; width: 30px; height: 30px; cursor: pointer; transition: all 0.3s ease; display: flex; align-items: center; justify-content: center;" onmouseover="this.style.background='#c82333'; this.style.transform='scale(1.1)'" onmouseout="this.style.background='#dc3545'; this.style.transform='scale(1)'" title="إغلاق النافذة">×</button>
                        </div>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                            <div>
                                <h5 style="color: #495057; margin-bottom: 10px;"><i class="fas fa-info-circle" style="margin-left: 5px;"></i>المعلومات الأساسية</h5>
                                <p><strong>الرمز:</strong> ${code}</p>
                                <p><strong>المستوى:</strong> <span style="background: #007bff; color: white; padding: 3px 8px; border-radius: 10px; font-size: 0.8rem;">${position.level}</span></p>
                                <p><strong>القسم:</strong> ${position.department}</p>
                                <p><strong>الدرجة:</strong> ${position.grade}</p>
                                <p><strong>الراتب:</strong> <span style="color: #28a745; font-weight: bold;">${position.salary} د.ع</span></p>
                                <p><strong>عدد الموظفين:</strong> <span style="background: #28a745; color: white; padding: 2px 6px; border-radius: 8px;">${position.employees}</span></p>
                            </div>
                            <div>
                                <h5 style="color: #495057; margin-bottom: 10px;"><i class="fas fa-clipboard-list" style="margin-left: 5px;"></i>الوصف الوظيفي</h5>
                                <p style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-right: 4px solid #007bff;">${position.description}</p>
                            </div>
                        </div>

                        ${position.requirements ? `
                        <div style="margin-bottom: 20px;">
                            <h5 style="color: #495057; margin-bottom: 10px;"><i class="fas fa-check-circle" style="margin-left: 5px;"></i>المتطلبات والمؤهلات</h5>
                            <ul style="background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 0;">
                                ${position.requirements.map(req => `<li style="margin-bottom: 5px;">${req}</li>`).join('')}
                            </ul>
                        </div>
                        ` : ''}

                        ${position.responsibilities ? `
                        <div style="margin-bottom: 20px;">
                            <h5 style="color: #495057; margin-bottom: 10px;"><i class="fas fa-tasks" style="margin-left: 5px;"></i>المسؤوليات والواجبات</h5>
                            <ul style="background: #fff3cd; padding: 15px; border-radius: 8px; margin: 0;">
                                ${position.responsibilities.map(resp => `<li style="margin-bottom: 5px;">${resp}</li>`).join('')}
                            </ul>
                        </div>
                        ` : ''}

                        <div style="display: flex; gap: 10px; justify-content: center; margin-top: 20px;">
                            <button onclick="editPosition('${code}')" style="background: #ffc107; color: #333; border: none; padding: 10px 20px; border-radius: 8px; cursor: pointer; display: flex; align-items: center; gap: 5px; transition: all 0.3s ease;" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 4px 12px rgba(255,193,7,0.3)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                                <i class="fas fa-edit"></i> تعديل المنصب
                            </button>
                            <button onclick="viewEmployees('${code}')" style="background: #17a2b8; color: white; border: none; padding: 10px 20px; border-radius: 8px; cursor: pointer; display: flex; align-items: center; gap: 5px; transition: all 0.3s ease;" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 4px 12px rgba(23,162,184,0.3)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                                <i class="fas fa-users"></i> عرض الموظفين
                            </button>
                            <button onclick="closeModal(this)" style="background: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 8px; cursor: pointer; display: flex; align-items: center; gap: 5px; transition: all 0.3s ease;" onmouseover="this.style.background='#dc3545'; this.style.transform='translateY(-2px)'; this.style.boxShadow='0 4px 12px rgba(220,53,69,0.3)'" onmouseout="this.style.background='#6c757d'; this.style.transform='translateY(0)'; this.style.boxShadow='none'">
                                <i class="fas fa-times"></i> إغلاق
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modal);
        }

        function editPosition(code) {
            // إغلاق أي نافذة مفتوحة
            const existingModal = document.querySelector('div[style*="position: fixed"]');
            if (existingModal) existingModal.remove();

            const modal = `
                <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999; display: flex; align-items: center; justify-content: center;" onclick="this.remove()">
                    <div style="background: white; border-radius: 12px; padding: 30px; max-width: 700px; width: 90%; max-height: 80%; overflow-y: auto; box-shadow: 0 10px 30px rgba(0,0,0,0.3);" onclick="event.stopPropagation()">
                        <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 20px; border-bottom: 2px solid #ffc107; padding-bottom: 15px;">
                            <h3 style="color: #ffc107; margin: 0; display: flex; align-items: center;">
                                <i class="fas fa-edit" style="margin-left: 10px;"></i>
                                تعديل المنصب: ${code}
                            </h3>
                            <button onclick="closeModal(this)" style="background: #dc3545; color: white; border: none; border-radius: 50%; width: 30px; height: 30px; cursor: pointer; transition: all 0.3s ease; display: flex; align-items: center; justify-content: center;" onmouseover="this.style.background='#c82333'; this.style.transform='scale(1.1)'" onmouseout="this.style.background='#dc3545'; this.style.transform='scale(1)'" title="إغلاق النافذة">×</button>
                        </div>

                        <form style="display: grid; gap: 15px;">
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                                <div>
                                    <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #495057;">اسم المنصب</label>
                                    <input type="text" value="مدير عام" style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'Cairo', sans-serif;">
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #495057;">المستوى الوظيفي</label>
                                    <select style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'Cairo', sans-serif;">
                                        <option>تنفيذي</option>
                                        <option>أول</option>
                                        <option>متوسط</option>
                                        <option>مبتدئ</option>
                                        <option>مساعد</option>
                                    </select>
                                </div>
                            </div>

                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                                <div>
                                    <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #495057;">القسم</label>
                                    <select style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'Cairo', sans-serif;">
                                        <option>الإدارة العليا</option>
                                        <option>المحاسبة</option>
                                        <option>الشؤون الإدارية</option>
                                        <option>الشؤون الطبية</option>
                                        <option>تكنولوجيا المعلومات</option>
                                    </select>
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #495057;">الدرجة المطلوبة</label>
                                    <select style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'Cairo', sans-serif;">
                                        <option>خاص</option>
                                        <option>الأولى</option>
                                        <option>الثانية</option>
                                        <option>الثالثة</option>
                                        <option>الرابعة</option>
                                        <option>الخامسة</option>
                                        <option>السادسة</option>
                                    </select>
                                </div>
                            </div>

                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #495057;">الراتب الأساسي (دينار عراقي)</label>
                                <input type="number" value="1500000" style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'Cairo', sans-serif;">
                            </div>

                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #495057;">الوصف الوظيفي</label>
                                <textarea rows="4" style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'Cairo', sans-serif; resize: vertical;">المسؤول الأول عن إدارة المؤسسة وتوجيه السياسات العامة</textarea>
                            </div>

                            <div style="display: flex; gap: 10px; justify-content: center; margin-top: 20px;">
                                <button type="button" onclick="savePosition('${code}')" style="background: #28a745; color: white; border: none; padding: 12px 25px; border-radius: 8px; cursor: pointer; display: flex; align-items: center; gap: 5px;">
                                    <i class="fas fa-save"></i> حفظ التغييرات
                                </button>
                                <button type="button" onclick="closeModal(this)" style="background: #6c757d; color: white; border: none; padding: 12px 25px; border-radius: 8px; cursor: pointer; transition: all 0.3s ease;" onmouseover="this.style.background='#dc3545'; this.style.transform='translateY(-2px)'" onmouseout="this.style.background='#6c757d'; this.style.transform='translateY(0)'">
                                    <i class="fas fa-times me-1"></i>إلغاء
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modal);
        }

        function viewEmployees(code) {
            // إغلاق أي نافذة مفتوحة
            const existingModal = document.querySelector('div[style*="position: fixed"]');
            if (existingModal) existingModal.remove();

            // بيانات الموظفين حسب المنصب
            const employeesData = {
                'P001': [
                    {name: 'د. أحمد محمد علي', id: '12001', startDate: '2015-01-01', performance: 'ممتاز', salary: '1,500,000'}
                ],
                'P002': [
                    {name: 'زينب حسن الكريم', id: '12002', startDate: '2018-03-15', performance: 'جيد جداً', salary: '1,200,000'}
                ],
                'P003': [
                    {name: 'علي أحمد حسين', id: '12003', startDate: '2019-06-01', performance: 'ممتاز', salary: '950,000'},
                    {name: 'فاطمة محمد علي', id: '12004', startDate: '2020-02-10', performance: 'جيد جداً', salary: '950,000'},
                    {name: 'حسام عبدالله', id: '12005', startDate: '2021-09-01', performance: 'جيد', salary: '950,000'}
                ]
            };

            const employees = employeesData[code] || [];
            const positionNames = {
                'P001': 'مدير عام',
                'P002': 'مدير قسم المحاسبة',
                'P003': 'محاسب أول'
            };

            const modal = `
                <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999; display: flex; align-items: center; justify-content: center;" onclick="this.remove()">
                    <div style="background: white; border-radius: 12px; padding: 30px; max-width: 800px; width: 90%; max-height: 80%; overflow-y: auto; box-shadow: 0 10px 30px rgba(0,0,0,0.3);" onclick="event.stopPropagation()">
                        <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 20px; border-bottom: 2px solid #17a2b8; padding-bottom: 15px;">
                            <h3 style="color: #17a2b8; margin: 0; display: flex; align-items: center;">
                                <i class="fas fa-users" style="margin-left: 10px;"></i>
                                موظفو منصب: ${positionNames[code] || code}
                            </h3>
                            <button onclick="closeModal(this)" style="background: #dc3545; color: white; border: none; border-radius: 50%; width: 30px; height: 30px; cursor: pointer; transition: all 0.3s ease; display: flex; align-items: center; justify-content: center;" onmouseover="this.style.background='#c82333'; this.style.transform='scale(1.1)'" onmouseout="this.style.background='#dc3545'; this.style.transform='scale(1)'" title="إغلاق النافذة">×</button>
                        </div>

                        <div style="margin-bottom: 20px; display: flex; gap: 15px; justify-content: center;">
                            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; text-align: center; flex: 1;">
                                <h4 style="margin: 0; color: #1976d2;">${employees.length}</h4>
                                <small>إجمالي الموظفين</small>
                            </div>
                            <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; text-align: center; flex: 1;">
                                <h4 style="margin: 0; color: #388e3c;">0</h4>
                                <small>الشواغر المتاحة</small>
                            </div>
                            <div style="background: #fff3e0; padding: 15px; border-radius: 8px; text-align: center; flex: 1;">
                                <h4 style="margin: 0; color: #f57c00;">85%</h4>
                                <small>متوسط الأداء</small>
                            </div>
                        </div>

                        ${employees.length > 0 ? `
                        <div style="overflow-x: auto;">
                            <table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
                                <thead>
                                    <tr style="background: #f8f9fa;">
                                        <th style="padding: 12px; text-align: center; border: 1px solid #dee2e6;">الرقم الوظيفي</th>
                                        <th style="padding: 12px; text-align: center; border: 1px solid #dee2e6;">الاسم الكامل</th>
                                        <th style="padding: 12px; text-align: center; border: 1px solid #dee2e6;">تاريخ التعيين</th>
                                        <th style="padding: 12px; text-align: center; border: 1px solid #dee2e6;">تقييم الأداء</th>
                                        <th style="padding: 12px; text-align: center; border: 1px solid #dee2e6;">الراتب</th>
                                        <th style="padding: 12px; text-align: center; border: 1px solid #dee2e6;">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${employees.map(emp => `
                                        <tr>
                                            <td style="padding: 10px; text-align: center; border: 1px solid #dee2e6;"><strong>${emp.id}</strong></td>
                                            <td style="padding: 10px; text-align: center; border: 1px solid #dee2e6;">${emp.name}</td>
                                            <td style="padding: 10px; text-align: center; border: 1px solid #dee2e6;">${emp.startDate}</td>
                                            <td style="padding: 10px; text-align: center; border: 1px solid #dee2e6;">
                                                <span style="background: ${emp.performance === 'ممتاز' ? '#28a745' : emp.performance === 'جيد جداً' ? '#17a2b8' : '#ffc107'}; color: white; padding: 3px 8px; border-radius: 10px; font-size: 0.8rem;">
                                                    ${emp.performance}
                                                </span>
                                            </td>
                                            <td style="padding: 10px; text-align: center; border: 1px solid #dee2e6; color: #28a745; font-weight: bold;">${emp.salary} د.ع</td>
                                            <td style="padding: 10px; text-align: center; border: 1px solid #dee2e6;">
                                                <button onclick="viewEmployeeDetails('${emp.id}')" style="background: #007bff; color: white; border: none; padding: 5px 10px; border-radius: 5px; cursor: pointer; margin: 2px;">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button onclick="editEmployee('${emp.id}')" style="background: #ffc107; color: #333; border: none; padding: 5px 10px; border-radius: 5px; cursor: pointer; margin: 2px;">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                        ` : `
                        <div style="text-align: center; padding: 40px; background: #f8f9fa; border-radius: 8px; margin: 20px 0;">
                            <i class="fas fa-users" style="font-size: 3rem; color: #6c757d; margin-bottom: 15px;"></i>
                            <h5 style="color: #6c757d;">لا يوجد موظفين في هذا المنصب حالياً</h5>
                            <p style="color: #6c757d;">يمكنك إضافة موظفين جدد من خلال صفحة إدارة الموظفين</p>
                        </div>
                        `}

                        <div style="display: flex; gap: 10px; justify-content: center; margin-top: 20px;">
                            <button onclick="window.open('add_employee_page.html', '_blank')" style="background: #28a745; color: white; border: none; padding: 12px 20px; border-radius: 8px; cursor: pointer; display: flex; align-items: center; gap: 5px;">
                                <i class="fas fa-plus"></i> إضافة موظف جديد
                            </button>
                            <button onclick="exportEmployeesList('${code}')" style="background: #17a2b8; color: white; border: none; padding: 12px 20px; border-radius: 8px; cursor: pointer; display: flex; align-items: center; gap: 5px;">
                                <i class="fas fa-file-excel"></i> تصدير القائمة
                            </button>
                            <button onclick="closeModal(this)" style="background: #6c757d; color: white; border: none; padding: 12px 20px; border-radius: 8px; cursor: pointer; transition: all 0.3s ease; display: flex; align-items: center; gap: 5px;" onmouseover="this.style.background='#dc3545'; this.style.transform='translateY(-2px)'" onmouseout="this.style.background='#6c757d'; this.style.transform='translateY(0)'">
                                <i class="fas fa-times"></i> إغلاق
                            </button>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modal);
        }
        
        // وظائف مساعدة إضافية
        function savePosition(code) {
            // محاكاة حفظ البيانات
            const modal = document.querySelector('div[style*="position: fixed"]');
            if (modal) modal.remove();

            // رسالة تأكيد
            const confirmModal = `
                <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999; display: flex; align-items: center; justify-content: center;">
                    <div style="background: white; border-radius: 12px; padding: 30px; max-width: 400px; width: 90%; text-align: center; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
                        <div style="color: #28a745; font-size: 3rem; margin-bottom: 15px;">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <h4 style="color: #28a745; margin-bottom: 15px;">تم الحفظ بنجاح!</h4>
                        <p style="color: #6c757d; margin-bottom: 20px;">تم تحديث بيانات المنصب ${code} بنجاح</p>
                        <button onclick="this.closest('div').remove(); location.reload();" style="background: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 8px; cursor: pointer;">
                            موافق
                        </button>
                    </div>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', confirmModal);
        }

        function viewEmployeeDetails(empId) {
            alert(`عرض تفاصيل الموظف: ${empId}\n\nسيتم عرض:\n• البيانات الشخصية الكاملة\n• تاريخ العمل والترقيات\n• التقييمات والإنجازات\n• الدورات التدريبية\n• معلومات الراتب والمخصصات`);
        }

        function editEmployee(empId) {
            alert(`تعديل بيانات الموظف: ${empId}\n\nيمكن تعديل:\n• البيانات الشخصية\n• معلومات الوظيفة\n• الراتب والدرجة\n• بيانات الاتصال\n• الحالة الوظيفية`);
        }

        function exportEmployeesList(positionCode) {
            alert(`📊 تصدير قائمة موظفي المنصب: ${positionCode}\n\nسيتم إنشاء ملف Excel يحتوي على:\n• بيانات جميع الموظفين\n• تفاصيل الرواتب\n• تقييمات الأداء\n• تواريخ التعيين والترقيات\n\nحجم الملف المتوقع: 500 KB`);
        }

        function showAddPositionModal() {
            const modal = `
                <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999; display: flex; align-items: center; justify-content: center;" onclick="this.remove()">
                    <div style="background: white; border-radius: 12px; padding: 30px; max-width: 700px; width: 90%; max-height: 80%; overflow-y: auto; box-shadow: 0 10px 30px rgba(0,0,0,0.3);" onclick="event.stopPropagation()">
                        <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 20px; border-bottom: 2px solid #28a745; padding-bottom: 15px;">
                            <h3 style="color: #28a745; margin: 0; display: flex; align-items: center;">
                                <i class="fas fa-plus-circle" style="margin-left: 10px;"></i>
                                إضافة منصب وظيفي جديد
                            </h3>
                            <button onclick="closeModal(this)" style="background: #dc3545; color: white; border: none; border-radius: 50%; width: 30px; height: 30px; cursor: pointer; transition: all 0.3s ease; display: flex; align-items: center; justify-content: center;" onmouseover="this.style.background='#c82333'; this.style.transform='scale(1.1)'" onmouseout="this.style.background='#dc3545'; this.style.transform='scale(1)'" title="إغلاق النافذة">×</button>
                        </div>

                        <form style="display: grid; gap: 15px;">
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                                <div>
                                    <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #495057;">رمز المنصب</label>
                                    <input type="text" placeholder="مثال: P009" style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'Cairo', sans-serif;">
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #495057;">اسم المنصب</label>
                                    <input type="text" placeholder="مثال: مساعد محاسب" style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'Cairo', sans-serif;">
                                </div>
                            </div>

                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                                <div>
                                    <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #495057;">المستوى الوظيفي</label>
                                    <select style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'Cairo', sans-serif;">
                                        <option value="">اختر المستوى</option>
                                        <option>تنفيذي</option>
                                        <option>أول</option>
                                        <option>متوسط</option>
                                        <option>مبتدئ</option>
                                        <option>مساعد</option>
                                    </select>
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #495057;">القسم</label>
                                    <select style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'Cairo', sans-serif;">
                                        <option value="">اختر القسم</option>
                                        <option>الإدارة العليا</option>
                                        <option>المحاسبة</option>
                                        <option>الشؤون الإدارية</option>
                                        <option>الشؤون الطبية</option>
                                        <option>تكنولوجيا المعلومات</option>
                                    </select>
                                </div>
                            </div>

                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                                <div>
                                    <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #495057;">الدرجة المطلوبة</label>
                                    <select style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'Cairo', sans-serif;">
                                        <option value="">اختر الدرجة</option>
                                        <option>خاص</option>
                                        <option>الأولى</option>
                                        <option>الثانية</option>
                                        <option>الثالثة</option>
                                        <option>الرابعة</option>
                                        <option>الخامسة</option>
                                        <option>السادسة</option>
                                    </select>
                                </div>
                                <div>
                                    <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #495057;">الراتب الأساسي (دينار عراقي)</label>
                                    <input type="number" placeholder="مثال: 650000" style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'Cairo', sans-serif;">
                                </div>
                            </div>

                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #495057;">الوصف الوظيفي</label>
                                <textarea rows="3" placeholder="وصف مختصر للمنصب ومسؤولياته..." style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'Cairo', sans-serif; resize: vertical;"></textarea>
                            </div>

                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #495057;">المتطلبات والمؤهلات</label>
                                <textarea rows="3" placeholder="المؤهلات المطلوبة وسنوات الخبرة..." style="width: 100%; padding: 10px; border: 2px solid #e9ecef; border-radius: 8px; font-family: 'Cairo', sans-serif; resize: vertical;"></textarea>
                            </div>

                            <div style="display: flex; gap: 10px; justify-content: center; margin-top: 20px;">
                                <button type="button" onclick="addNewPosition()" style="background: #28a745; color: white; border: none; padding: 12px 25px; border-radius: 8px; cursor: pointer; display: flex; align-items: center; gap: 5px;">
                                    <i class="fas fa-plus"></i> إضافة المنصب
                                </button>
                                <button type="button" onclick="closeModal(this)" style="background: #6c757d; color: white; border: none; padding: 12px 25px; border-radius: 8px; cursor: pointer; transition: all 0.3s ease; display: flex; align-items: center; gap: 5px;" onmouseover="this.style.background='#dc3545'; this.style.transform='translateY(-2px)'" onmouseout="this.style.background='#6c757d'; this.style.transform='translateY(0)'">
                                    <i class="fas fa-times"></i> إلغاء
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', modal);
        }

        function addNewPosition() {
            // محاكاة إضافة منصب جديد
            const modal = document.querySelector('div[style*="position: fixed"]');
            if (modal) modal.remove();

            const confirmModal = `
                <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999; display: flex; align-items: center; justify-content: center;">
                    <div style="background: white; border-radius: 12px; padding: 30px; max-width: 400px; width: 90%; text-align: center; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
                        <div style="color: #28a745; font-size: 3rem; margin-bottom: 15px;">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <h4 style="color: #28a745; margin-bottom: 15px;">تمت الإضافة بنجاح!</h4>
                        <p style="color: #6c757d; margin-bottom: 20px;">تم إضافة المنصب الجديد إلى النظام</p>
                        <button onclick="this.closest('div').remove(); location.reload();" style="background: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 8px; cursor: pointer;">
                            موافق
                        </button>
                    </div>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', confirmModal);
        }

        // وظيفة إغلاق النوافذ المحسنة
        function closeModal(button) {
            const modal = button.closest('div[style*="position: fixed"]');
            if (modal) {
                // إضافة تأثير انتقالي عند الإغلاق
                modal.style.opacity = '0';
                modal.style.transform = 'scale(0.95)';
                modal.style.transition = 'all 0.3s ease';

                setTimeout(() => {
                    modal.remove();
                }, 300);
            }
        }

        // وظيفة حفظ المنصب
        function savePosition(code) {
            // محاكاة حفظ البيانات
            const modal = document.querySelector('div[style*="position: fixed"]');
            if (modal) {
                // إضافة تأثير تحميل
                const saveBtn = modal.querySelector('button[onclick*="savePosition"]');
                const originalText = saveBtn.innerHTML;
                saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الحفظ...';
                saveBtn.disabled = true;

                setTimeout(() => {
                    modal.remove();
                    showSuccessMessage(`تم حفظ تعديلات المنصب ${code} بنجاح!`);
                }, 1500);
            }
        }

        // وظيفة عرض تفاصيل الموظف
        function viewEmployeeDetails(empId) {
            showSuccessMessage(`عرض تفاصيل الموظف رقم ${empId}`);
        }

        // وظيفة تعديل الموظف
        function editEmployee(empId) {
            showSuccessMessage(`تعديل بيانات الموظف رقم ${empId}`);
        }

        // وظيفة عرض رسالة نجاح
        function showSuccessMessage(message) {
            const successDiv = document.createElement('div');
            successDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, #28a745, #20c997);
                color: white;
                padding: 15px 25px;
                border-radius: 8px;
                box-shadow: 0 5px 15px rgba(0,0,0,0.2);
                z-index: 10000;
                font-family: 'Cairo', sans-serif;
                font-weight: 600;
                display: flex;
                align-items: center;
                gap: 10px;
                animation: slideInRight 0.3s ease;
            `;

            successDiv.innerHTML = `<i class="fas fa-check-circle"></i> ${message}`;
            document.body.appendChild(successDiv);

            setTimeout(() => {
                successDiv.style.animation = 'slideOutRight 0.3s ease';
                setTimeout(() => successDiv.remove(), 300);
            }, 3000);
        }

        // إضافة أنيميشن للرسائل
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }

            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);

        // وظيفة إغلاق سريعة بالضغط على Escape
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                const modal = document.querySelector('div[style*="position: fixed"]');
                if (modal) {
                    closeModal(modal.querySelector('button[onclick*="closeModal"]') || modal);
                }
            }
        });

        // تحسين تأثيرات الأزرار
        function addButtonEffects() {
            const buttons = document.querySelectorAll('button');
            buttons.forEach(button => {
                button.addEventListener('click', function() {
                    // تأثير النقر
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });
        }
        
        function exportPositions() {
            alert(`📊 تصدير دليل المناصب\n\nسيتم إنشاء ملف Excel يحتوي على:\n• جميع المناصب الوظيفية\n• التفاصيل الكاملة\n• الإحصائيات\n• المخططات البيانية\n\nحجم الملف المتوقع: 1.2 MB`);
        }
        
        function printPositions() {
            alert(`🖨️ طباعة دليل المناصب\n\nسيتم إعداد:\n• تقرير مفصل للطباعة\n• تنسيق احترافي\n• شعار المؤسسة\n• فهرس المحتويات\n\nعدد الصفحات المتوقع: 15 صفحة`);
        }

        // تفعيل تأثيرات الأزرار عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            addButtonEffects();
        });

        // إضافة تأثيرات إضافية للنوافذ المنبثقة
        function enhanceModalInteraction() {
            // إغلاق النافذة بالنقر خارجها
            document.addEventListener('click', function(event) {
                const modal = document.querySelector('div[style*="position: fixed"]');
                if (modal && event.target === modal) {
                    closeModal(modal.querySelector('button[onclick*="closeModal"]') || modal);
                }
            });
        }

        // تفعيل التحسينات
        enhanceModalInteraction();

        // تحسين تفاعل الأزرار
        function enhanceButtons() {
            const buttons = document.querySelectorAll('.action-buttons .btn');
            buttons.forEach(button => {
                // إضافة تأثير النقر
                button.addEventListener('click', function(e) {
                    // تأثير الموجة
                    const ripple = document.createElement('span');
                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;

                    ripple.style.cssText = `
                        position: absolute;
                        width: ${size}px;
                        height: ${size}px;
                        left: ${x}px;
                        top: ${y}px;
                        background: rgba(255,255,255,0.6);
                        border-radius: 50%;
                        transform: scale(0);
                        animation: ripple 0.6s ease-out;
                        pointer-events: none;
                    `;

                    this.appendChild(ripple);
                    setTimeout(() => ripple.remove(), 600);
                });

                // إضافة تأثير التمرير المحسن
                button.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-3px) scale(1.08)';
                    this.style.boxShadow = '0 6px 20px rgba(0,0,0,0.25)';
                });

                button.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                    this.style.boxShadow = '';
                });
            });
        }

        // إضافة أنيميشن الموجة
        const rippleStyle = document.createElement('style');
        rippleStyle.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(2);
                    opacity: 0;
                }
            }

            .action-buttons .btn {
                position: relative;
                overflow: hidden;
            }

            /* تحسين تأثيرات الأزرار حسب النوع */
            .btn-outline-info {
                border-color: #17a2b8;
                color: #17a2b8;
                background: linear-gradient(45deg, transparent 30%, rgba(23,162,184,0.1) 50%, transparent 70%);
                background-size: 200% 100%;
                background-position: 100% 0;
                transition: all 0.3s ease;
            }

            .btn-outline-info:hover {
                background-position: 0 0;
                transform: translateY(-2px) scale(1.05);
            }

            .btn-outline-warning {
                border-color: #ffc107;
                color: #ffc107;
                background: linear-gradient(45deg, transparent 30%, rgba(255,193,7,0.1) 50%, transparent 70%);
                background-size: 200% 100%;
                background-position: 100% 0;
                transition: all 0.3s ease;
            }

            .btn-outline-warning:hover {
                background-position: 0 0;
                transform: translateY(-2px) scale(1.05);
            }

            .btn-outline-primary {
                border-color: #007bff;
                color: #007bff;
                background: linear-gradient(45deg, transparent 30%, rgba(0,123,255,0.1) 50%, transparent 70%);
                background-size: 200% 100%;
                background-position: 100% 0;
                transition: all 0.3s ease;
            }

            .btn-outline-primary:hover {
                background-position: 0 0;
                transform: translateY(-2px) scale(1.05);
            }
        `;
        document.head.appendChild(rippleStyle);

        // تفعيل التحسينات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            enhanceButtons();
        });

        // إعادة تفعيل التحسينات عند تحديث المحتوى
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.addedNodes.length > 0) {
                    enhanceButtons();
                }
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        console.log('✅ صفحة دليل المناصب محملة بنجاح');
        console.log('🔍 البحث والفلترة متاحة');
        console.log('📋 25 منصب وظيفي متاح');
        console.log('🎯 الأزرار التفاعلية مفعلة');
        console.log('✨ التأثيرات البصرية جاهزة');
        console.log('🎯 أزرار الإغلاق مفعلة ومحسنة');
    </script>
</body>
</html>
