# -*- coding: utf-8 -*-
"""
تشغيل تطبيق الويب المبسط
Simple Web Application Launcher
"""

import os
import sys
from pathlib import Path

# إضافة المسار الحالي
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def create_basic_directories():
    """إنشاء المجلدات الأساسية"""
    directories = [
        "data",
        "web_app/templates",
        "web_app/static/css",
        "web_app/static/js",
        "web_app/static/images",
        "backups",
        "reports",
        "logs"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("✅ تم إنشاء المجلدات الأساسية")

def create_simple_template():
    """إنشاء قالب HTML بسيط"""
    template_content = """<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام المحاسبة المتكامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body { font-family: 'Cairo', sans-serif; }
        .hero { background: linear-gradient(135deg, #007bff 0%, #0056b3 100%); color: white; padding: 4rem 0; }
    </style>
</head>
<body>
    <div class="hero text-center">
        <div class="container">
            <h1 class="display-4 fw-bold mb-4">🏛️ نظام المحاسبة المتكامل</h1>
            <p class="lead mb-4">وزارة الشباب والرياضة - دائرة الطب الرياضي</p>
            <a href="/login" class="btn btn-light btn-lg">تسجيل الدخول</a>
        </div>
    </div>
    
    <div class="container mt-5">
        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <h5 class="card-title">👥 إدارة الموظفين</h5>
                        <p class="card-text">إدارة شاملة لبيانات الموظفين والمخصصات والاستقطاعات</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <h5 class="card-title">💰 نظام الرواتب</h5>
                        <p class="card-text">حساب الرواتب الشهرية تلقائياً وفقاً للنظام الحكومي</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <h5 class="card-title">📊 التقارير</h5>
                        <p class="card-text">تقارير شاملة مع إمكانية التصدير إلى PDF وExcel</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="alert alert-info text-center">
                    <h5>🔑 بيانات الدخول الافتراضية</h5>
                    <p class="mb-0"><strong>اسم المستخدم:</strong> admin | <strong>كلمة المرور:</strong> admin123</p>
                </div>
            </div>
        </div>
    </div>
    
    <footer class="bg-light text-center py-3 mt-5">
        <p class="mb-0">© 2024 وزارة الشباب والرياضة - دائرة الطب الرياضي</p>
    </footer>
</body>
</html>"""
    
    template_path = Path("web_app/templates/simple_index.html")
    with open(template_path, "w", encoding="utf-8") as f:
        f.write(template_content)
    
    print("✅ تم إنشاء القالب الأساسي")

def create_simple_app():
    """إنشاء تطبيق ويب مبسط"""
    try:
        from fastapi import FastAPI, Request
        from fastapi.responses import HTMLResponse
        from fastapi.templating import Jinja2Templates
        import uvicorn
        
        # إنشاء التطبيق
        app = FastAPI(title="نظام المحاسبة المتكامل")
        
        # إعداد القوالب
        templates = Jinja2Templates(directory="web_app/templates")
        
        @app.get("/", response_class=HTMLResponse)
        async def home(request: Request):
            return templates.TemplateResponse("simple_index.html", {"request": request})
        
        @app.get("/login", response_class=HTMLResponse)
        async def login(request: Request):
            return """
            <html dir="rtl">
            <head>
                <meta charset="UTF-8">
                <title>تسجيل الدخول</title>
                <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
                <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
                <style>body { font-family: 'Cairo', sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; display: flex; align-items: center; }</style>
            </head>
            <body>
                <div class="container">
                    <div class="row justify-content-center">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-primary text-white text-center">
                                    <h3>🏛️ نظام المحاسبة المتكامل</h3>
                                    <p class="mb-0">تسجيل الدخول</p>
                                </div>
                                <div class="card-body">
                                    <div class="alert alert-info">
                                        <strong>بيانات الدخول الافتراضية:</strong><br>
                                        اسم المستخدم: admin<br>
                                        كلمة المرور: admin123
                                    </div>
                                    <form>
                                        <div class="mb-3">
                                            <label class="form-label">اسم المستخدم</label>
                                            <input type="text" class="form-control" value="admin">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">كلمة المرور</label>
                                            <input type="password" class="form-control" value="admin123">
                                        </div>
                                        <button type="button" class="btn btn-primary w-100" onclick="alert('مرحباً! النظام قيد التطوير. سيتم تفعيل تسجيل الدخول قريباً.')">تسجيل الدخول</button>
                                    </form>
                                    <div class="text-center mt-3">
                                        <a href="/" class="text-decoration-none">العودة إلى الصفحة الرئيسية</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </body>
            </html>
            """
        
        return app
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد المكتبات: {e}")
        print("يرجى تثبيت المتطلبات: pip install fastapi uvicorn jinja2")
        return None

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء تشغيل نظام المحاسبة المتكامل - تطبيق الويب")
    print("=" * 60)
    
    # إنشاء المجلدات
    create_basic_directories()
    
    # إنشاء القالب
    create_simple_template()
    
    # إنشاء التطبيق
    app = create_simple_app()
    
    if app:
        print("🌐 جاري تشغيل الخادم...")
        print("📍 الرابط: http://localhost:8000")
        print("⏹️  لإيقاف الخادم اضغط Ctrl+C")
        print("=" * 60)
        
        try:
            import uvicorn
            uvicorn.run(app, host="127.0.0.1", port=8000, log_level="info")
        except ImportError:
            print("❌ خطأ: مكتبة uvicorn غير مثبتة")
            print("يرجى تشغيل: pip install uvicorn")
        except KeyboardInterrupt:
            print("\n⏹️ تم إيقاف الخادم")
        except Exception as e:
            print(f"❌ خطأ في تشغيل الخادم: {e}")
    else:
        print("❌ فشل في إنشاء التطبيق")

if __name__ == "__main__":
    main()
