#!/usr/bin/env python3
"""
تشغيل خادم الويب بشكل مبسط
"""

import uvicorn
import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """تشغيل الخادم"""
    try:
        print("🚀 بدء تشغيل خادم الويب...")
        print("📍 الرابط: http://localhost:8000")
        print("🔗 الصفحة الرئيسية: http://localhost:8000")
        print("👥 إدارة الموظفين: http://localhost:8000/employees")
        print("🔐 تسجيل الدخول: http://localhost:8000/login")
        print("=" * 50)
        
        # تشغيل الخادم
        uvicorn.run(
            "web_main:app",
            host="127.0.0.1",
            port=8000,
            reload=False,
            log_level="info"
        )
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الخادم بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")
        print("💡 تأكد من تثبيت المتطلبات: pip install fastapi uvicorn")

if __name__ == "__main__":
    main()
