# -*- coding: utf-8 -*-
"""
نموذج الاستقطاعات
Deduction Model
"""

from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from database.database_manager import Base

class Deduction(Base):
    __tablename__ = "deductions"
    
    id = Column(Integer, primary_key=True, index=True)
    employee_id = Column(Integer, ForeignKey("employees.id"), nullable=False)
    deduction_type = Column(String(50), nullable=False)
    amount = Column(Float, default=0.0)
    percentage = Column(Float)  # نسبة الاستقطاع من الراتب الأساسي
    is_active = Column(Boolean, default=True)
    effective_date = Column(DateTime, server_default=func.now())
    end_date = Column(DateTime)
    notes = Column(String(255))
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    
    # العلاقات
    employee = relationship("Employee", back_populates="deductions")
    
    def __init__(self, employee_id, deduction_type, amount=0.0, percentage=None, is_active=True, notes=None):
        self.employee_id = employee_id
        self.deduction_type = deduction_type
        self.amount = amount
        self.percentage = percentage
        self.is_active = is_active
        self.notes = notes
    
    @classmethod
    def get_deduction_types(cls):
        """الحصول على أنواع الاستقطاعات المتاحة"""
        from config.settings import SALARY_CONFIG
        basic_deductions = list(SALARY_CONFIG['deductions'].keys())
        additional_deductions = SALARY_CONFIG['additional_deductions']
        return basic_deductions + additional_deductions
    
    @classmethod
    def get_deduction_type_display(cls, deduction_type):
        """الحصول على اسم الاستقطاع للعرض"""
        from config.settings import SALARY_CONFIG
        
        # الاستقطاعات الأساسية
        basic_deductions = SALARY_CONFIG['deductions']
        if deduction_type in basic_deductions:
            return basic_deductions[deduction_type]['name']
        
        # الاستقطاعات الإضافية
        type_names = {
            'تنفيذ قضائي': 'تنفيذ قضائي',
            'أقساط': 'أقساط',
            'أمانات صحية': 'أمانات صحية',
            'حجز مصرفي': 'حجز للمصارف أو التنفيذ'
        }
        return type_names.get(deduction_type, deduction_type)
    
    @classmethod
    def get_default_percentage(cls, deduction_type):
        """الحصول على النسبة الافتراضية للاستقطاع"""
        from config.settings import SALARY_CONFIG
        basic_deductions = SALARY_CONFIG['deductions']
        if deduction_type in basic_deductions:
            return basic_deductions[deduction_type]['rate'] * 100
        return None
    
    def get_display_name(self):
        """الحصول على اسم الاستقطاع للعرض"""
        return self.get_deduction_type_display(self.deduction_type)
    
    def calculate_amount(self, basic_salary):
        """حساب مبلغ الاستقطاع"""
        if self.percentage:
            return basic_salary * (self.percentage / 100)
        return self.amount
    
    def is_percentage_based(self):
        """التحقق من كون الاستقطاع مبني على نسبة"""
        return self.percentage is not None and self.percentage > 0
    
    def deactivate(self):
        """إلغاء تفعيل الاستقطاع"""
        self.is_active = False
        self.end_date = func.now()
    
    def reactivate(self):
        """إعادة تفعيل الاستقطاع"""
        self.is_active = True
        self.end_date = None
    
    def to_dict(self):
        """تحويل إلى قاموس"""
        return {
            'id': self.id,
            'employee_id': self.employee_id,
            'deduction_type': self.deduction_type,
            'deduction_type_display': self.get_display_name(),
            'amount': self.amount,
            'percentage': self.percentage,
            'is_percentage_based': self.is_percentage_based(),
            'is_active': self.is_active,
            'effective_date': self.effective_date.isoformat() if self.effective_date else None,
            'end_date': self.end_date.isoformat() if self.end_date else None,
            'notes': self.notes,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    def __repr__(self):
        if self.percentage:
            return f"<Deduction(type='{self.deduction_type}', percentage={self.percentage}%, employee_id={self.employee_id})>"
        else:
            return f"<Deduction(type='{self.deduction_type}', amount={self.amount}, employee_id={self.employee_id})>"
