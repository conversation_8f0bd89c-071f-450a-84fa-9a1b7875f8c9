# -*- coding: utf-8 -*-
"""
نموذج الموظف
Employee Model
"""

from datetime import datetime, date
from database.database_manager import db_manager

class Employee:
    def __init__(self, employee_id=None, employee_number=None, full_name=None,
                 job_title=None, job_grade=None, qualification=None, stage=None,
                 basic_salary=0.0, ministry=None, department=None, section=None,
                 division=None, hire_date=None, is_active=True):
        self.id = employee_id
        self.employee_number = employee_number
        self.full_name = full_name
        self.job_title = job_title
        self.job_grade = job_grade
        self.qualification = qualification
        self.stage = stage
        self.basic_salary = basic_salary
        self.ministry = ministry
        self.department = department
        self.section = section
        self.division = division
        self.hire_date = hire_date
        self.is_active = is_active
        self.allowances = []
        self.deductions = []
    
    def save(self):
        """حفظ بيانات الموظف"""
        if self.id:
            return self.update()
        else:
            return self.create()
    
    def create(self):
        """إنشاء موظف جديد"""
        query = '''
            INSERT INTO employees 
            (employee_number, full_name, job_title, job_grade, qualification, 
             stage, basic_salary, ministry, department, section, division, 
             hire_date, is_active)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        '''
        
        params = (
            self.employee_number, self.full_name, self.job_title, self.job_grade,
            self.qualification, self.stage, self.basic_salary, self.ministry,
            self.department, self.section, self.division, self.hire_date,
            self.is_active
        )
        
        try:
            result = db_manager.execute_query(query, params)
            if result > 0:
                # الحصول على ID الموظف الجديد
                self.id = db_manager.execute_query(
                    "SELECT last_insert_rowid()"
                )[0][0]
                return True
            return False
        except Exception as e:
            print(f"خطأ في إنشاء الموظف: {e}")
            return False
    
    def update(self):
        """تحديث بيانات الموظف"""
        query = '''
            UPDATE employees SET
                employee_number = ?, full_name = ?, job_title = ?, job_grade = ?,
                qualification = ?, stage = ?, basic_salary = ?, ministry = ?,
                department = ?, section = ?, division = ?, hire_date = ?,
                is_active = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        '''
        
        params = (
            self.employee_number, self.full_name, self.job_title, self.job_grade,
            self.qualification, self.stage, self.basic_salary, self.ministry,
            self.department, self.section, self.division, self.hire_date,
            self.is_active, self.id
        )
        
        try:
            result = db_manager.execute_query(query, params)
            return result > 0
        except Exception as e:
            print(f"خطأ في تحديث الموظف: {e}")
            return False
    
    def delete(self):
        """حذف الموظف (إلغاء تفعيل)"""
        query = "UPDATE employees SET is_active = 0 WHERE id = ?"
        try:
            result = db_manager.execute_query(query, (self.id,))
            return result > 0
        except Exception as e:
            print(f"خطأ في حذف الموظف: {e}")
            return False
    
    def add_allowance(self, allowance_type, amount):
        """إضافة مخصص للموظف"""
        query = '''
            INSERT INTO allowances (employee_id, allowance_type, amount)
            VALUES (?, ?, ?)
        '''
        try:
            result = db_manager.execute_query(query, (self.id, allowance_type, amount))
            if result > 0:
                self.load_allowances()
                return True
            return False
        except Exception as e:
            print(f"خطأ في إضافة المخصص: {e}")
            return False
    
    def add_deduction(self, deduction_type, amount, percentage=None):
        """إضافة استقطاع للموظف"""
        query = '''
            INSERT INTO deductions (employee_id, deduction_type, amount, percentage)
            VALUES (?, ?, ?, ?)
        '''
        try:
            result = db_manager.execute_query(query, (self.id, deduction_type, amount, percentage))
            if result > 0:
                self.load_deductions()
                return True
            return False
        except Exception as e:
            print(f"خطأ في إضافة الاستقطاع: {e}")
            return False
    
    def load_allowances(self):
        """تحميل مخصصات الموظف"""
        query = '''
            SELECT allowance_type, amount FROM allowances 
            WHERE employee_id = ? AND is_active = 1
        '''
        try:
            results = db_manager.execute_query(query, (self.id,))
            self.allowances = [{'type': row[0], 'amount': row[1]} for row in results]
        except Exception as e:
            print(f"خطأ في تحميل المخصصات: {e}")
            self.allowances = []
    
    def load_deductions(self):
        """تحميل استقطاعات الموظف"""
        query = '''
            SELECT deduction_type, amount, percentage FROM deductions 
            WHERE employee_id = ? AND is_active = 1
        '''
        try:
            results = db_manager.execute_query(query, (self.id,))
            self.deductions = [
                {'type': row[0], 'amount': row[1], 'percentage': row[2]} 
                for row in results
            ]
        except Exception as e:
            print(f"خطأ في تحميل الاستقطاعات: {e}")
            self.deductions = []
    
    def calculate_total_allowances(self):
        """حساب إجمالي المخصصات"""
        return sum(allowance['amount'] for allowance in self.allowances)
    
    def calculate_total_deductions(self):
        """حساب إجمالي الاستقطاعات"""
        total = 0
        for deduction in self.deductions:
            if deduction['percentage']:
                # حساب الاستقطاع كنسبة من الراتب الأساسي
                total += self.basic_salary * (deduction['percentage'] / 100)
            else:
                total += deduction['amount']
        return total
    
    def calculate_net_salary(self):
        """حساب الراتب الصافي"""
        gross_salary = self.basic_salary + self.calculate_total_allowances()
        net_salary = gross_salary - self.calculate_total_deductions()
        return max(0, net_salary)  # التأكد من عدم كون الراتب سالباً
    
    @staticmethod
    def get_all_employees(active_only=True):
        """الحصول على جميع الموظفين"""
        query = "SELECT * FROM employees"
        if active_only:
            query += " WHERE is_active = 1"
        query += " ORDER BY full_name"
        
        try:
            results = db_manager.execute_query(query)
            employees = []
            for row in results:
                employee = Employee(
                    employee_id=row[0], employee_number=row[1], full_name=row[2],
                    job_title=row[3], job_grade=row[4], qualification=row[5],
                    stage=row[6], basic_salary=row[7], ministry=row[8],
                    department=row[9], section=row[10], division=row[11],
                    hire_date=row[12], is_active=row[13]
                )
                employee.load_allowances()
                employee.load_deductions()
                employees.append(employee)
            return employees
        except Exception as e:
            print(f"خطأ في جلب الموظفين: {e}")
            return []
    
    @staticmethod
    def get_by_id(employee_id):
        """الحصول على موظف بواسطة المعرف"""
        query = "SELECT * FROM employees WHERE id = ?"
        try:
            results = db_manager.execute_query(query, (employee_id,))
            if results:
                row = results[0]
                employee = Employee(
                    employee_id=row[0], employee_number=row[1], full_name=row[2],
                    job_title=row[3], job_grade=row[4], qualification=row[5],
                    stage=row[6], basic_salary=row[7], ministry=row[8],
                    department=row[9], section=row[10], division=row[11],
                    hire_date=row[12], is_active=row[13]
                )
                employee.load_allowances()
                employee.load_deductions()
                return employee
            return None
        except Exception as e:
            print(f"خطأ في جلب الموظف: {e}")
            return None
    
    @staticmethod
    def search_employees(search_term):
        """البحث عن الموظفين"""
        query = '''
            SELECT * FROM employees 
            WHERE (full_name LIKE ? OR employee_number LIKE ?) AND is_active = 1
            ORDER BY full_name
        '''
        search_pattern = f"%{search_term}%"
        try:
            results = db_manager.execute_query(query, (search_pattern, search_pattern))
            employees = []
            for row in results:
                employee = Employee(
                    employee_id=row[0], employee_number=row[1], full_name=row[2],
                    job_title=row[3], job_grade=row[4], qualification=row[5],
                    stage=row[6], basic_salary=row[7], ministry=row[8],
                    department=row[9], section=row[10], division=row[11],
                    hire_date=row[12], is_active=row[13]
                )
                employees.append(employee)
            return employees
        except Exception as e:
            print(f"خطأ في البحث عن الموظفين: {e}")
            return []
