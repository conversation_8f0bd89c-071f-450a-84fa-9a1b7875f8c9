# -*- coding: utf-8 -*-
"""
نموذج الموظف
Employee Model
"""

from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, Date, Text, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from database.database_manager import Base
from datetime import datetime, date

class Employee(Base):
    __tablename__ = "employees"
    
    id = Column(Integer, primary_key=True, index=True)
    employee_number = Column(String(20), unique=True, index=True, nullable=False)
    full_name = Column(String(100), nullable=False)
    job_title = Column(String(100), nullable=False)
    job_grade = Column(String(20), nullable=False)
    qualification = Column(String(50))
    stage = Column(String(20))
    basic_salary = Column(Float, nullable=False, default=0.0)
    
    # الهيكل التنظيمي
    ministry = Column(String(100), default='وزارة الشباب والرياضة')
    department = Column(String(100), default='دائرة الطب الرياضي')
    section = Column(String(100), default='قسم إداري')
    division = Column(String(100), default='شعبة الحسابات')
    
    # معلومات إضافية
    hire_date = Column(Date)
    birth_date = Column(Date)
    national_id = Column(String(20), unique=True)
    phone = Column(String(20))
    email = Column(String(100))
    address = Column(Text)
    
    # حالة الموظف
    is_active = Column(Boolean, default=True)
    termination_date = Column(Date)
    termination_reason = Column(Text)
    
    # تواريخ النظام
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    
    # العلاقات
    allowances = relationship("Allowance", back_populates="employee", cascade="all, delete-orphan")
    deductions = relationship("Deduction", back_populates="employee", cascade="all, delete-orphan")
    monthly_salaries = relationship("MonthlySalary", back_populates="employee", cascade="all, delete-orphan")
    
    def __init__(self, employee_number, full_name, job_title, job_grade, basic_salary,
                 qualification=None, stage=None, ministry=None, department=None,
                 section=None, division=None, hire_date=None, **kwargs):
        self.employee_number = employee_number
        self.full_name = full_name
        self.job_title = job_title
        self.job_grade = job_grade
        self.basic_salary = basic_salary
        self.qualification = qualification
        self.stage = stage
        self.ministry = ministry or 'وزارة الشباب والرياضة'
        self.department = department or 'دائرة الطب الرياضي'
        self.section = section or 'قسم إداري'
        self.division = division or 'شعبة الحسابات'
        self.hire_date = hire_date
        
        # معلومات إضافية
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def calculate_total_allowances(self):
        """حساب إجمالي المخصصات"""
        return sum(allowance.amount for allowance in self.allowances if allowance.is_active)
    
    def calculate_total_deductions(self):
        """حساب إجمالي الاستقطاعات"""
        total = 0
        for deduction in self.deductions:
            if not deduction.is_active:
                continue
                
            if deduction.percentage:
                # حساب الاستقطاع كنسبة من الراتب الأساسي
                total += self.basic_salary * (deduction.percentage / 100)
            else:
                total += deduction.amount
        return total
    
    def calculate_gross_salary(self):
        """حساب الراتب الإجمالي"""
        return self.basic_salary + self.calculate_total_allowances()
    
    def calculate_net_salary(self):
        """حساب الراتب الصافي"""
        gross_salary = self.calculate_gross_salary()
        net_salary = gross_salary - self.calculate_total_deductions()
        return max(0, net_salary)  # التأكد من عدم كون الراتب سالباً
    
    def get_allowance_by_type(self, allowance_type):
        """الحصول على مخصص معين"""
        for allowance in self.allowances:
            if allowance.allowance_type == allowance_type and allowance.is_active:
                return allowance
        return None
    
    def get_deduction_by_type(self, deduction_type):
        """الحصول على استقطاع معين"""
        for deduction in self.deductions:
            if deduction.deduction_type == deduction_type and deduction.is_active:
                return deduction
        return None
    
    def add_allowance(self, allowance_type, amount):
        """إضافة مخصص جديد"""
        from models.allowance import Allowance
        
        # التحقق من عدم وجود نفس المخصص
        existing = self.get_allowance_by_type(allowance_type)
        if existing:
            existing.amount = amount
            return existing
        
        allowance = Allowance(
            employee_id=self.id,
            allowance_type=allowance_type,
            amount=amount
        )
        self.allowances.append(allowance)
        return allowance
    
    def add_deduction(self, deduction_type, amount=None, percentage=None):
        """إضافة استقطاع جديد"""
        from models.deduction import Deduction
        
        # التحقق من عدم وجود نفس الاستقطاع
        existing = self.get_deduction_by_type(deduction_type)
        if existing:
            existing.amount = amount or existing.amount
            existing.percentage = percentage or existing.percentage
            return existing
        
        deduction = Deduction(
            employee_id=self.id,
            deduction_type=deduction_type,
            amount=amount or 0,
            percentage=percentage
        )
        self.deductions.append(deduction)
        return deduction
    
    def deactivate(self, reason=None):
        """إلغاء تفعيل الموظف"""
        self.is_active = False
        self.termination_date = date.today()
        self.termination_reason = reason
    
    def reactivate(self):
        """إعادة تفعيل الموظف"""
        self.is_active = True
        self.termination_date = None
        self.termination_reason = None
    
    def get_age(self):
        """حساب العمر"""
        if self.birth_date:
            today = date.today()
            return today.year - self.birth_date.year - ((today.month, today.day) < (self.birth_date.month, self.birth_date.day))
        return None
    
    def get_service_years(self):
        """حساب سنوات الخدمة"""
        if self.hire_date:
            end_date = self.termination_date or date.today()
            return end_date.year - self.hire_date.year
        return 0
    
    def to_dict(self):
        """تحويل إلى قاموس"""
        return {
            'id': self.id,
            'employee_number': self.employee_number,
            'full_name': self.full_name,
            'job_title': self.job_title,
            'job_grade': self.job_grade,
            'qualification': self.qualification,
            'stage': self.stage,
            'basic_salary': self.basic_salary,
            'ministry': self.ministry,
            'department': self.department,
            'section': self.section,
            'division': self.division,
            'hire_date': self.hire_date.isoformat() if self.hire_date else None,
            'birth_date': self.birth_date.isoformat() if self.birth_date else None,
            'national_id': self.national_id,
            'phone': self.phone,
            'email': self.email,
            'address': self.address,
            'is_active': self.is_active,
            'age': self.get_age(),
            'service_years': self.get_service_years(),
            'total_allowances': self.calculate_total_allowances(),
            'total_deductions': self.calculate_total_deductions(),
            'gross_salary': self.calculate_gross_salary(),
            'net_salary': self.calculate_net_salary()
        }
    
    def __repr__(self):
        return f"<Employee(number='{self.employee_number}', name='{self.full_name}', title='{self.job_title}')>"
