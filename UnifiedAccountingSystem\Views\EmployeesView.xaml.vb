Imports System.Windows
Imports System.Windows.Controls
Imports System.Collections.ObjectModel
Imports UnifiedAccountingSystem.Models
Imports UnifiedAccountingSystem.Services

Public Class EmployeesView
    Private _databaseService As DatabaseService
    Private _employees As ObservableCollection(Of Employee)
    Private _filteredEmployees As ObservableCollection(Of Employee)

    Public Sub New()
        InitializeComponent()
        _databaseService = New DatabaseService()
        _employees = New ObservableCollection(Of Employee)
        _filteredEmployees = New ObservableCollection(Of Employee)

        ' ربط البيانات بالجدول
        EmployeesGrid.ItemsSource = _filteredEmployees

        ' إضافة معالج الأحداث
        AddHandler Loaded, AddressOf UserControl_Loaded
    End Sub

    Private Sub AddEmployee_Click(sender As Object, e As RoutedEventArgs)
        ' سيتم تنفيذ إضافة موظف جديد
        MessageBox.Show("سيتم تنفيذ إضافة موظف جديد", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information)
    End Sub

    Private Sub RefreshEmployees_Click(sender As Object, e As RoutedEventArgs)
        ' سيتم تحديث قائمة الموظفين
        LoadEmployees()
    End Sub

    Private Sub SearchBox_TextChanged(sender As Object, e As TextChangedEventArgs)
        ' سيتم تنفيذ البحث في الموظفين
        ApplyFilters()
    End Sub

    Private Sub FilterComboBox_SelectionChanged(sender As Object, e As SelectionChangedEventArgs)
        ' سيتم تطبيق التصفية
        ApplyFilters()
    End Sub

    Private Sub EditEmployee_Click(sender As Object, e As RoutedEventArgs)
        Dim button = DirectCast(sender, Button)
        Dim employee = DirectCast(button.DataContext, Employee)
        ' سيتم تنفيذ تعديل بيانات الموظف
        MessageBox.Show($"سيتم تعديل بيانات الموظف: {employee.FullName}", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information)
    End Sub

    Private Sub DeleteEmployee_Click(sender As Object, e As RoutedEventArgs)
        Dim button = DirectCast(sender, Button)
        Dim employee = DirectCast(button.DataContext, Employee)
        ' سيتم تنفيذ حذف الموظف
        Dim result = MessageBox.Show($"هل أنت متأكد من حذف الموظف: {employee.FullName}؟", "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question)
        If result = MessageBoxResult.Yes Then
            ' تنفيذ عملية الحذف
        End If
    End Sub

    Private Sub LoadEmployees()
        Try
            ' مسح البيانات الحالية
            _employees.Clear()

            ' في التطبيق الحقيقي، سيتم تحميل البيانات من قاعدة البيانات
            ' Dim query = "SELECT * FROM Employees WHERE IsActive = 1"
            ' Dim employees = _databaseService.ExecuteQuery(Of Employee)(query, Nothing)

            ' للاختبار - إضافة بيانات تجريبية
            LoadSampleEmployees()

            ' تطبيق الفلاتر
            ApplyFilters()

        Catch ex As Exception
            MessageBox.Show($"حدث خطأ أثناء تحميل بيانات الموظفين: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error)
        End Try
    End Sub

    Private Sub LoadSampleEmployees()
        ' بيانات تجريبية للموظفين
        _employees.Add(New Employee With {
            .EmployeeId = 1001,
            .FullName = "أحمد محمد علي",
            .IBANNumber = "***********************",
            .Gender = Gender.Male,
            .MaritalStatus = MaritalStatus.Married,
            .BirthDate = New Date(1985, 5, 15),
            .HireDate = New Date(2010, 3, 1),
            .Status = EmployeeStatus.Active,
            .Position = "محاسب أول",
            .Department = "المحاسبة",
            .BasicSalary = 750000,
            .NetSalary = 650000
        })

        _employees.Add(New Employee With {
            .EmployeeId = 1002,
            .FullName = "فاطمة حسن محمود",
            .IBANNumber = "***********************",
            .Gender = Gender.Female,
            .MaritalStatus = MaritalStatus.Single,
            .BirthDate = New Date(1990, 8, 22),
            .HireDate = New Date(2015, 6, 15),
            .Status = EmployeeStatus.Active,
            .Position = "كاتبة",
            .Department = "الموارد البشرية",
            .BasicSalary = 580000,
            .NetSalary = 520000
        })

        _employees.Add(New Employee With {
            .EmployeeId = 1003,
            .FullName = "محمد عبد الله أحمد",
            .IBANNumber = "***********************",
            .Gender = Gender.Male,
            .MaritalStatus = MaritalStatus.Married,
            .BirthDate = New Date(1978, 12, 10),
            .HireDate = New Date(2005, 1, 20),
            .Status = EmployeeStatus.Active,
            .Position = "مدير الشعبة",
            .Department = "الإدارة",
            .BasicSalary = 950000,
            .NetSalary = 820000
        })

        _employees.Add(New Employee With {
            .EmployeeId = 1004,
            .FullName = "زينب علي حسن",
            .IBANNumber = "***********************",
            .Gender = Gender.Female,
            .MaritalStatus = MaritalStatus.Widowed,
            .BirthDate = New Date(1982, 4, 5),
            .HireDate = New Date(2012, 9, 10),
            .Status = EmployeeStatus.Active,
            .Position = "طبيبة",
            .Department = "الطب الرياضي",
            .BasicSalary = 1200000,
            .NetSalary = 1050000
        })

        _employees.Add(New Employee With {
            .EmployeeId = 1005,
            .FullName = "علي أحمد محمد",
            .IBANNumber = "***********************",
            .Gender = Gender.Male,
            .MaritalStatus = MaritalStatus.Divorced,
            .BirthDate = New Date(1988, 7, 18),
            .HireDate = New Date(2018, 2, 5),
            .Status = EmployeeStatus.Active,
            .Position = "مهندس",
            .Department = "الصيانة",
            .BasicSalary = 850000,
            .NetSalary = 740000
        })
    End Sub

    Private Sub ApplyFilters()
        Try
            ' مسح القائمة المفلترة
            _filteredEmployees.Clear()

            ' الحصول على نص البحث وقيمة الفلتر
            Dim searchText As String = If(SearchBox?.Text?.Trim()?.ToLower(), "")
            Dim filterValue As String = TryCast(FilterComboBox?.SelectedItem, ComboBoxItem)?.Content?.ToString()

            ' تطبيق الفلاتر
            For Each employee In _employees
                Dim matchesSearch As Boolean = String.IsNullOrEmpty(searchText) OrElse
                    employee.FullName.ToLower().Contains(searchText) OrElse
                    employee.EmployeeId.ToString().Contains(searchText) OrElse
                    employee.Department.ToLower().Contains(searchText) OrElse
                    employee.Position.ToLower().Contains(searchText) OrElse
                    employee.IBANNumber.ToLower().Contains(searchText)

                Dim matchesFilter As Boolean = True
                If Not String.IsNullOrEmpty(filterValue) Then
                    Select Case filterValue
                        Case "نشط"
                            matchesFilter = employee.Status = EmployeeStatus.Active
                        Case "غير نشط"
                            matchesFilter = employee.Status <> EmployeeStatus.Active
                        Case "الكل"
                            matchesFilter = True
                    End Select
                End If

                ' إضافة الموظف إذا كان يطابق الشروط
                If matchesSearch AndAlso matchesFilter Then
                    _filteredEmployees.Add(employee)
                End If
            Next

        Catch ex As Exception
            MessageBox.Show($"حدث خطأ أثناء تطبيق الفلاتر: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error)
        End Try
    End Sub

    Private Sub UserControl_Loaded(sender As Object, e As RoutedEventArgs)
        ' تحميل البيانات عند فتح الصفحة
        LoadEmployees()
        FilterComboBox.SelectedIndex = 0
    End Sub
End Class
