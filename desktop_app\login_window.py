# -*- coding: utf-8 -*-
"""
نافذة تسجيل الدخول
Login Window for Desktop Application
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QLineEdit, QPushButton, QFrame, QMessageBox,
                            QCheckBox, QProgressBar, QGroupBox)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, QThread
from PyQt5.QtGui import QFont, QPixmap, QPalette, QIcon

from database.database_manager import db_manager
from models.user import User
from utils.arabic_support import arabic_support
from config.settings import UI_CONFIG, ORGANIZATION_INFO

class LoginThread(QThread):
    """خيط تسجيل الدخول"""
    
    login_result = pyqtSignal(bool, object, str)  # success, user, message
    
    def __init__(self, username, password):
        super().__init__()
        self.username = username
        self.password = password
    
    def run(self):
        try:
            session = db_manager.get_session()
            
            # البحث عن المستخدم
            user = session.query(User).filter(User.username == self.username).first()
            
            if not user:
                self.login_result.emit(False, None, "اسم المستخدم غير موجود")
                return
            
            if not user.is_active:
                self.login_result.emit(False, None, "الحساب غير مفعل")
                return
            
            if user.is_locked():
                self.login_result.emit(False, None, "الحساب مقفل مؤقتاً")
                return
            
            if not user.verify_password(self.password):
                user.record_failed_login()
                session.commit()
                self.login_result.emit(False, None, "كلمة المرور غير صحيحة")
                return
            
            # تسجيل دخول ناجح
            user.record_login()
            session.commit()
            self.login_result.emit(True, user, "تم تسجيل الدخول بنجاح")
            
        except Exception as e:
            self.login_result.emit(False, None, f"خطأ في تسجيل الدخول: {str(e)}")
        finally:
            session.close()

class LoginWindow(QDialog):
    """نافذة تسجيل الدخول"""
    
    login_successful = pyqtSignal(object)  # user object
    
    def __init__(self):
        super().__init__()
        self.user = None
        self.login_thread = None
        self.setup_ui()
        self.setup_connections()
        self.load_saved_credentials()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إعدادات النافذة
        self.setWindowTitle("تسجيل الدخول - نظام المحاسبة المتكامل")
        self.setFixedSize(*UI_CONFIG['desktop']['login_size'])
        self.setWindowFlags(Qt.Dialog | Qt.WindowCloseButtonHint)
        self.setModal(True)
        
        # تطبيق الخط العربي
        arabic_support.setup_rtl_widget(self)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(30, 30, 30, 30)
        
        # رأس النافذة
        self.create_header(main_layout)
        
        # معلومات المؤسسة
        self.create_organization_info(main_layout)
        
        # نموذج تسجيل الدخول
        self.create_login_form(main_layout)
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)
        
        # أزرار التحكم
        self.create_buttons(main_layout)
        
        # مساحة فارغة
        main_layout.addStretch()
        
        # معلومات النظام
        self.create_footer(main_layout)
        
        self.setLayout(main_layout)
        
        # تطبيق الأنماط
        self.apply_styles()
    
    def create_header(self, layout):
        """إنشاء رأس النافذة"""
        header_frame = QFrame()
        header_layout = QVBoxLayout()
        
        # عنوان النظام
        title_label = QLabel("نظام المحاسبة المتكامل")
        title_font = QFont(arabic_support.default_font)
        title_font.setPointSize(18)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #2c3e50; margin: 10px;")
        
        # عنوان فرعي
        subtitle_label = QLabel("تسجيل الدخول إلى النظام")
        subtitle_font = QFont(arabic_support.default_font)
        subtitle_font.setPointSize(12)
        subtitle_label.setFont(subtitle_font)
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setStyleSheet("color: #7f8c8d; margin-bottom: 20px;")
        
        header_layout.addWidget(title_label)
        header_layout.addWidget(subtitle_label)
        header_frame.setLayout(header_layout)
        
        layout.addWidget(header_frame)
    
    def create_organization_info(self, layout):
        """إنشاء معلومات المؤسسة"""
        org_group = QGroupBox("معلومات المؤسسة")
        org_layout = QVBoxLayout()
        
        org_info = [
            f"الوزارة: {ORGANIZATION_INFO['ministry']}",
            f"الدائرة: {ORGANIZATION_INFO['department']}",
            f"القسم: {ORGANIZATION_INFO['section']}",
            f"الشعبة: {ORGANIZATION_INFO['division']}"
        ]
        
        for info in org_info:
            label = QLabel(info)
            label.setFont(arabic_support.get_arabic_font(10))
            label.setStyleSheet("color: #34495e; padding: 2px;")
            org_layout.addWidget(label)
        
        org_group.setLayout(org_layout)
        layout.addWidget(org_group)
    
    def create_login_form(self, layout):
        """إنشاء نموذج تسجيل الدخول"""
        form_group = QGroupBox("بيانات تسجيل الدخول")
        form_layout = QVBoxLayout()
        
        # اسم المستخدم
        username_label = QLabel("اسم المستخدم:")
        username_label.setFont(arabic_support.get_arabic_font(11, True))
        self.username_edit = QLineEdit()
        self.username_edit.setFont(arabic_support.get_arabic_font(11))
        self.username_edit.setPlaceholderText("أدخل اسم المستخدم")
        
        # كلمة المرور
        password_label = QLabel("كلمة المرور:")
        password_label.setFont(arabic_support.get_arabic_font(11, True))
        self.password_edit = QLineEdit()
        self.password_edit.setFont(arabic_support.get_arabic_font(11))
        self.password_edit.setEchoMode(QLineEdit.Password)
        self.password_edit.setPlaceholderText("أدخل كلمة المرور")
        
        # تذكر بيانات الدخول
        self.remember_checkbox = QCheckBox("تذكر بيانات الدخول")
        self.remember_checkbox.setFont(arabic_support.get_arabic_font(10))
        
        form_layout.addWidget(username_label)
        form_layout.addWidget(self.username_edit)
        form_layout.addWidget(password_label)
        form_layout.addWidget(self.password_edit)
        form_layout.addWidget(self.remember_checkbox)
        
        form_group.setLayout(form_layout)
        layout.addWidget(form_group)
    
    def create_buttons(self, layout):
        """إنشاء أزرار التحكم"""
        buttons_layout = QHBoxLayout()
        
        # زر تسجيل الدخول
        self.login_button = QPushButton("تسجيل الدخول")
        self.login_button.setFont(arabic_support.get_arabic_font(11, True))
        self.login_button.setDefault(True)
        
        # زر الإلغاء
        self.cancel_button = QPushButton("إلغاء")
        self.cancel_button.setFont(arabic_support.get_arabic_font(11))
        
        buttons_layout.addWidget(self.cancel_button)
        buttons_layout.addWidget(self.login_button)
        
        layout.addLayout(buttons_layout)
    
    def create_footer(self, layout):
        """إنشاء تذييل النافذة"""
        footer_label = QLabel("© 2024 نظام المحاسبة المتكامل - جميع الحقوق محفوظة")
        footer_label.setFont(arabic_support.get_arabic_font(9))
        footer_label.setAlignment(Qt.AlignCenter)
        footer_label.setStyleSheet("color: #95a5a6; margin-top: 20px;")
        
        layout.addWidget(footer_label)
    
    def apply_styles(self):
        """تطبيق الأنماط"""
        self.setStyleSheet("""
            QDialog {
                background-color: #ecf0f1;
            }
            
            QGroupBox {
                font-weight: bold;
                border: 2px solid #bdc3c7;
                border-radius: 8px;
                margin: 10px 0px;
                padding-top: 10px;
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: #2c3e50;
                background-color: #ecf0f1;
            }
            
            QLineEdit {
                padding: 8px;
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                font-size: 11pt;
                background-color: white;
            }
            
            QLineEdit:focus {
                border-color: #3498db;
            }
            
            QPushButton {
                padding: 10px 20px;
                border: none;
                border-radius: 6px;
                font-weight: bold;
                min-width: 100px;
            }
            
            QPushButton#login_button {
                background-color: #3498db;
                color: white;
            }
            
            QPushButton#login_button:hover {
                background-color: #2980b9;
            }
            
            QPushButton#login_button:pressed {
                background-color: #21618c;
            }
            
            QPushButton#cancel_button {
                background-color: #95a5a6;
                color: white;
            }
            
            QPushButton#cancel_button:hover {
                background-color: #7f8c8d;
            }
            
            QCheckBox {
                spacing: 8px;
            }
            
            QProgressBar {
                border: 2px solid #bdc3c7;
                border-radius: 6px;
                text-align: center;
                background-color: white;
            }
            
            QProgressBar::chunk {
                background-color: #3498db;
                border-radius: 4px;
            }
        """)
        
        # تعيين أسماء الكائنات للأنماط
        self.login_button.setObjectName("login_button")
        self.cancel_button.setObjectName("cancel_button")
    
    def setup_connections(self):
        """إعداد الاتصالات"""
        self.login_button.clicked.connect(self.handle_login)
        self.cancel_button.clicked.connect(self.reject)
        self.username_edit.returnPressed.connect(self.password_edit.setFocus)
        self.password_edit.returnPressed.connect(self.handle_login)
    
    def handle_login(self):
        """معالجة تسجيل الدخول"""
        username = self.username_edit.text().strip()
        password = self.password_edit.text()
        
        # التحقق من الحقول المطلوبة
        if not username:
            self.show_error("يرجى إدخال اسم المستخدم")
            self.username_edit.setFocus()
            return
        
        if not password:
            self.show_error("يرجى إدخال كلمة المرور")
            self.password_edit.setFocus()
            return
        
        # تعطيل الواجهة أثناء المعالجة
        self.set_ui_enabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # شريط تقدم غير محدد
        
        # بدء خيط تسجيل الدخول
        self.login_thread = LoginThread(username, password)
        self.login_thread.login_result.connect(self.on_login_result)
        self.login_thread.start()
    
    def on_login_result(self, success, user, message):
        """معالجة نتيجة تسجيل الدخول"""
        # إعادة تفعيل الواجهة
        self.set_ui_enabled(True)
        self.progress_bar.setVisible(False)
        
        if success:
            self.user = user
            
            # حفظ بيانات الدخول إذا كان مطلوباً
            if self.remember_checkbox.isChecked():
                self.save_credentials()
            
            # إرسال إشارة نجاح تسجيل الدخول
            self.login_successful.emit(user)
            self.accept()
        else:
            self.show_error(message)
            self.password_edit.clear()
            self.password_edit.setFocus()
    
    def set_ui_enabled(self, enabled):
        """تفعيل/تعطيل عناصر الواجهة"""
        self.username_edit.setEnabled(enabled)
        self.password_edit.setEnabled(enabled)
        self.login_button.setEnabled(enabled)
        self.remember_checkbox.setEnabled(enabled)
    
    def show_error(self, message):
        """عرض رسالة خطأ"""
        msg_box = QMessageBox(self)
        msg_box.setIcon(QMessageBox.Warning)
        msg_box.setWindowTitle("خطأ في تسجيل الدخول")
        msg_box.setText(message)
        msg_box.setStandardButtons(QMessageBox.Ok)
        msg_box.exec_()
    
    def load_saved_credentials(self):
        """تحميل بيانات الدخول المحفوظة"""
        # يمكن إضافة تحميل البيانات من الإعدادات
        pass
    
    def save_credentials(self):
        """حفظ بيانات الدخول"""
        # يمكن إضافة حفظ البيانات في الإعدادات
        pass
    
    def keyPressEvent(self, event):
        """معالجة أحداث لوحة المفاتيح"""
        if event.key() == Qt.Key_Escape:
            self.reject()
        else:
            super().keyPressEvent(event)
